"""
Simple script to copy manufacturers from remote MySQL to local SQLite
"""

import sqlite3
import mysql.connector
from mysql.connector import Error

def copy_manufacturers():
    print("Starting manufacturer data copy...")
    
    # MySQL connection parameters
    mysql_config = {
        'host': 'mysql.sophio.com',
        'port': 3306,
        'database': 'whi_aces',
        'user': 'facetedapi',
        'password': 'dtutuc954',
    }
    
    # SQLite database path
    sqlite_db = 'pnci-pricing/db.sqlite3'
    
    try:
        # Connect to MySQL database
        print('Connecting to MySQL database...')
        mysql_conn = mysql.connector.connect(**mysql_config)
        mysql_cursor = mysql_conn.cursor()
        
        # Get total count
        mysql_cursor.execute("SELECT COUNT(*) FROM manufacturers")
        total_count = mysql_cursor.fetchone()[0]
        print(f'Found {total_count} manufacturers in remote database')
        
        # Fetch first 100 manufacturers for testing
        mysql_cursor.execute("SELECT MfrID, MfrName FROM manufacturers ORDER BY MfrName LIMIT 100")
        manufacturers = mysql_cursor.fetchall()
        
        # Close MySQL connection
        mysql_cursor.close()
        mysql_conn.close()
        print('MySQL connection closed')
        
        # Connect to SQLite database
        print('Connecting to SQLite database...')
        sqlite_conn = sqlite3.connect(sqlite_db)
        sqlite_cursor = sqlite_conn.cursor()
        
        # Clear existing manufacturer codes
        print('Clearing existing manufacturer codes...')
        sqlite_cursor.execute('DELETE FROM whi_mfgcodes')
        sqlite_conn.commit()
        print('Existing data cleared')
        
        # Import data to SQLite
        print('Importing manufacturers to local SQLite...')
        imported_count = 0
        
        for mfr_id, mfr_name in manufacturers:
            # Use MfrID as mfgcode and MfrName as brand
            mfgcode = str(mfr_id)
            brand = mfr_name.strip() if mfr_name else f'Manufacturer {mfr_id}'
            
            # Insert into SQLite
            sqlite_cursor.execute(
                'INSERT INTO whi_mfgcodes (mfgcode, brand, created_at) VALUES (?, ?, datetime("now"))',
                (mfgcode, brand)
            )
            imported_count += 1
            
            if imported_count % 10 == 0:
                print(f'Imported {imported_count} manufacturers...')
        
        # Commit changes
        sqlite_conn.commit()
        
        # Get final count
        sqlite_cursor.execute('SELECT COUNT(*) FROM whi_mfgcodes')
        final_count = sqlite_cursor.fetchone()[0]
        
        # Close SQLite connection
        sqlite_cursor.close()
        sqlite_conn.close()
        
        # Summary
        print(f'''
Import completed!
  Total in remote DB: {total_count}
  Imported: {imported_count}
  Total in local DB: {final_count}
        ''')
        
    except Error as e:
        print(f'MySQL Error: {e}')
        return False
        
    except Exception as e:
        print(f'Error: {e}')
        return False
    
    return True

if __name__ == '__main__':
    success = copy_manufacturers()
    if success:
        print("✅ Manufacturer data copied successfully!")
    else:
        print("❌ Failed to copy manufacturer data")
