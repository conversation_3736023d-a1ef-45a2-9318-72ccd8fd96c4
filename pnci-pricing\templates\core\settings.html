<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - PNCI Tool</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    {% csrf_token %}
    
    <div class="min-h-screen py-8">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Settings</h1>
                        <p class="mt-2 text-gray-600">Configure proxy settings and database connections</p>
                    </div>
                    <div>
                        <a href="/" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-home mr-2"></i>
                            Home
                        </a>
                    </div>
                </div>
            </div>

            <!-- Settings Form -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Configuration</h2>
                </div>
                
                <form id="settingsForm" class="p-6 space-y-6">
                    <!-- Proxy Settings Section -->
                    <div class="border-b border-gray-200 pb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            <i class="fas fa-shield-alt mr-2 text-blue-600"></i>
                            Proxy Settings (Webshare.io)
                        </h3>
                        
                        <div class="space-y-6">
                            <div>
                                <label for="webshareApiKey" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-key mr-2"></i>
                                    Webshare.io API Key
                                </label>
                                <input type="password" id="webshareApiKey" name="webshareApiKey"
                                       placeholder="Enter your webshare.io API key"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <p class="mt-1 text-sm text-gray-500">
                                    Get your API key from <a href="https://www.webshare.io/dashboard" target="_blank" class="text-blue-600 hover:underline">webshare.io dashboard</a>
                                </p>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" id="proxyEnabled" name="proxyEnabled"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="proxyEnabled" class="ml-2 block text-sm text-gray-900">
                                    Enable Proxy for Web Scraping
                                </label>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <button type="button" id="testProxyBtn" 
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm">
                                <i class="fas fa-check-circle mr-2"></i>
                                Test Connection
                            </button>
                            <span id="proxyTestResult" class="ml-3 text-sm"></span>
                        </div>
                    </div>

                    <!-- Database Settings Section -->
                    <div class="border-b border-gray-200 pb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            <i class="fas fa-database mr-2 text-green-600"></i>
                            Database Settings
                        </h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="dbHost" class="block text-sm font-medium text-gray-700 mb-2">
                                    Database Host
                                </label>
                                <input type="text" id="dbHost" name="dbHost" 
                                       placeholder="localhost"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                            </div>
                            
                            <div>
                                <label for="dbPort" class="block text-sm font-medium text-gray-700 mb-2">
                                    Port
                                </label>
                                <input type="number" id="dbPort" name="dbPort" 
                                       placeholder="3306"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                            </div>
                            
                            <div>
                                <label for="dbName" class="block text-sm font-medium text-gray-700 mb-2">
                                    Database Name
                                </label>
                                <input type="text" id="dbName" name="dbName" 
                                       placeholder="pnci_tool"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                            </div>
                            
                            <div>
                                <label for="dbUsername" class="block text-sm font-medium text-gray-700 mb-2">
                                    Username
                                </label>
                                <input type="text" id="dbUsername" name="dbUsername" 
                                       placeholder="root"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                            </div>
                            
                            <div class="md:col-span-2">
                                <label for="dbPassword" class="block text-sm font-medium text-gray-700 mb-2">
                                    Password
                                </label>
                                <input type="password" id="dbPassword" name="dbPassword" 
                                       placeholder="your_password"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <button type="button" id="testDbBtn" 
                                    class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm">
                                <i class="fas fa-check-circle mr-2"></i>
                                Test Connection
                            </button>
                            <span id="dbTestResult" class="ml-3 text-sm"></span>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-end items-center pt-6">
                        <div class="space-x-3">
                            <button type="button" id="resetBtn"
                                    class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-md">
                                <i class="fas fa-undo mr-2"></i>
                                Reset
                            </button>
                            <button type="submit" id="saveBtn"
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md">
                                <i class="fas fa-save mr-2"></i>
                                Save Settings
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Status Messages -->
    <div id="statusMessage" class="fixed top-4 right-4 z-50 hidden">
        <div class="bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm">
            <div class="flex items-center">
                <div id="statusIcon" class="mr-3"></div>
                <div id="statusText" class="text-sm"></div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadCurrentSettings();
            
            document.getElementById('settingsForm').addEventListener('submit', saveSettings);
            document.getElementById('testProxyBtn').addEventListener('click', testProxyConnection);
            document.getElementById('testDbBtn').addEventListener('click', testDatabaseConnection);
            document.getElementById('resetBtn').addEventListener('click', loadCurrentSettings);
        });

        function loadCurrentSettings() {
            fetch('/api/settings/')
            .then(response => response.json())
            .then(data => {
                if (data.proxy) {
                    // Load proxy settings from ProxyConfiguration model
                    loadProxySettings();
                }
                // Database settings would come from environment/config
                loadDatabaseSettings();
            })
            .catch(error => {
                console.error('Error loading settings:', error);
                showStatus('error', 'Failed to load current settings');
            });
        }

        function loadProxySettings() {
            // Load from ProxyConfiguration model
            fetch('/api/proxy-config/')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.proxy) {
                    document.getElementById('webshareApiKey').value = data.proxy.api_key || '';
                    document.getElementById('proxyEnabled').checked = data.proxy.is_enabled || false;
                }
            })
            .catch(error => console.error('Error loading proxy settings:', error));
        }

        function loadDatabaseSettings() {
            // Database settings are typically in environment variables
            // For security, we don't expose actual values, just show placeholders
            document.getElementById('dbHost').placeholder = 'Current: localhost';
            document.getElementById('dbPort').placeholder = 'Current: 3306';
            document.getElementById('dbName').placeholder = 'Current: pnci_tool';
            document.getElementById('dbUsername').placeholder = 'Current: [configured]';
        }

        function saveSettings(event) {
            event.preventDefault();
            
            const saveBtn = document.getElementById('saveBtn');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
            saveBtn.disabled = true;

            const formData = {
                proxy: {
                    api_key: document.getElementById('webshareApiKey').value,
                    is_enabled: document.getElementById('proxyEnabled').checked
                },
                database: {
                    host: document.getElementById('dbHost').value,
                    port: document.getElementById('dbPort').value,
                    name: document.getElementById('dbName').value,
                    username: document.getElementById('dbUsername').value,
                    password: document.getElementById('dbPassword').value
                }
            };

            fetch('/api/save-settings/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showStatus('success', 'Settings saved successfully!');
                } else {
                    showStatus('error', data.error || 'Failed to save settings');
                }
            })
            .catch(error => {
                console.error('Error saving settings:', error);
                showStatus('error', 'Failed to save settings');
            })
            .finally(() => {
                saveBtn.innerHTML = originalText;
                saveBtn.disabled = false;
            });
        }

        function testProxyConnection() {
            const testBtn = document.getElementById('testProxyBtn');
            const resultSpan = document.getElementById('proxyTestResult');
            
            testBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Testing...';
            testBtn.disabled = true;
            resultSpan.textContent = '';

            const proxyData = {
                api_key: document.getElementById('webshareApiKey').value
            };

            fetch('/api/test-proxy/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                },
                body: JSON.stringify(proxyData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultSpan.innerHTML = '<span class="text-green-600"><i class="fas fa-check-circle mr-1"></i>Connection successful</span>';
                } else {
                    resultSpan.innerHTML = '<span class="text-red-600"><i class="fas fa-times-circle mr-1"></i>' + (data.error || 'Connection failed') + '</span>';
                }
            })
            .catch(error => {
                resultSpan.innerHTML = '<span class="text-red-600"><i class="fas fa-times-circle mr-1"></i>Test failed</span>';
            })
            .finally(() => {
                testBtn.innerHTML = '<i class="fas fa-check-circle mr-2"></i>Test Connection';
                testBtn.disabled = false;
            });
        }

        function testDatabaseConnection() {
            const testBtn = document.getElementById('testDbBtn');
            const resultSpan = document.getElementById('dbTestResult');
            
            testBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Testing...';
            testBtn.disabled = true;
            resultSpan.textContent = '';

            const dbData = {
                host: document.getElementById('dbHost').value,
                port: document.getElementById('dbPort').value,
                name: document.getElementById('dbName').value,
                username: document.getElementById('dbUsername').value,
                password: document.getElementById('dbPassword').value
            };

            fetch('/api/test-database/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                },
                body: JSON.stringify(dbData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultSpan.innerHTML = '<span class="text-green-600"><i class="fas fa-check-circle mr-1"></i>Connection successful</span>';
                } else {
                    resultSpan.innerHTML = '<span class="text-red-600"><i class="fas fa-times-circle mr-1"></i>' + (data.error || 'Connection failed') + '</span>';
                }
            })
            .catch(error => {
                resultSpan.innerHTML = '<span class="text-red-600"><i class="fas fa-times-circle mr-1"></i>Test failed</span>';
            })
            .finally(() => {
                testBtn.innerHTML = '<i class="fas fa-check-circle mr-2"></i>Test Connection';
                testBtn.disabled = false;
            });
        }

        function showStatus(type, message) {
            const statusMessage = document.getElementById('statusMessage');
            const statusIcon = document.getElementById('statusIcon');
            const statusText = document.getElementById('statusText');

            if (type === 'success') {
                statusIcon.innerHTML = '<i class="fas fa-check-circle text-green-500"></i>';
            } else {
                statusIcon.innerHTML = '<i class="fas fa-exclamation-circle text-red-500"></i>';
            }

            statusText.textContent = message;
            statusMessage.classList.remove('hidden');

            setTimeout(() => {
                statusMessage.classList.add('hidden');
            }, 5000);
        }

        function getCsrfToken() {
            return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
        }
    </script>
</body>
</html>
