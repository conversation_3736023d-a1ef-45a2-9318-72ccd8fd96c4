# Generated by Django 4.2.7 on 2025-06-03 21:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0006_recreate_interchangedata'),
    ]

    operations = [
        migrations.RenameIndex(
            model_name='interchangedata',
            new_name='interchange_interch_2632d6_idx',
            old_name='interchange_data_interchange_part_number_idx',
        ),
        migrations.RenameIndex(
            model_name='interchangedata',
            new_name='interchange_whi_mfg_6d4f61_idx',
            old_name='interchange_data_whi_mfgcode_idx',
        ),
        migrations.RenameIndex(
            model_name='interchangedata',
            new_name='interchange_interch_7204f2_idx',
            old_name='interchange_data_interchange_mfgcode_idx',
        ),
        migrations.RenameIndex(
            model_name='interchangedata',
            new_name='interchange_interch_f2e55c_idx',
            old_name='interchange_data_interchange_type_idx',
        ),
        migrations.AddField(
            model_name='proxyconfiguration',
            name='api_key',
            field=models.<PERSON>r<PERSON>ield(blank=True, max_length=255),
        ),
        migrations.Alter<PERSON>ield(
            model_name='proxyconfiguration',
            name='endpoint',
            field=models.Char<PERSON>ield(blank=True, max_length=255),
        ),
    ]
