from django.core.management.base import BaseCommand
import requests
from bs4 import BeautifulSoup


class Command(BaseCommand):
    help = 'Test scraping a WHI smart page to see HTML structure'

    def handle(self, *args, **options):
        # Test URL from our debug records
        test_url = "https://smartpages.nexpart.com/smartpage.php?mfrlinecode=AIS&partnumber=ATF-0WS&acesptermid=11387"
        
        self.stdout.write(f"Testing URL: {test_url}")
        
        try:
            # Try to fetch the page (without proxy for now)
            response = requests.get(test_url, timeout=30)
            self.stdout.write(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Look for tables
                tables = soup.find_all('table')
                self.stdout.write(f"Found {len(tables)} tables")
                
                for i, table in enumerate(tables):
                    self.stdout.write(f"\n--- Table {i+1} ---")
                    
                    # Get table headers
                    headers = table.find_all('th')
                    if headers:
                        header_text = [h.get_text().strip() for h in headers]
                        self.stdout.write(f"Headers: {header_text}")
                    
                    # Get first few rows
                    rows = table.find_all('tr')
                    self.stdout.write(f"Total rows: {len(rows)}")
                    
                    for j, row in enumerate(rows[:3]):  # Show first 3 rows
                        cells = row.find_all(['td', 'th'])
                        cell_text = [cell.get_text().strip() for cell in cells]
                        self.stdout.write(f"  Row {j+1}: {cell_text}")
                
                # Look for specific text that might indicate interchange sections
                page_text = soup.get_text()
                if "OE Interchange" in page_text:
                    self.stdout.write("\n✅ Found 'OE Interchange' text")
                if "Aftermarket Interchange" in page_text:
                    self.stdout.write("✅ Found 'Aftermarket Interchange' text")
                
                # Look for divs or sections that might contain the interchange data
                divs = soup.find_all('div')
                self.stdout.write(f"\nFound {len(divs)} div elements")
                
                # Save the HTML to a file for inspection
                with open('whi_page_sample.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                self.stdout.write("✅ Saved HTML to 'whi_page_sample.html' for inspection")
                
            else:
                self.stdout.write(f"❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            self.stdout.write(f"❌ Error: {str(e)}")
