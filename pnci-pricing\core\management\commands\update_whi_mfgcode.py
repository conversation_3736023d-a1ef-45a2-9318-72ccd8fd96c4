from django.core.management.base import BaseCommand
from django.db import models
from core.models import UploadedFile, ProductData


class Command(BaseCommand):
    help = 'Update ProductData records with WHI manufacturer codes from their UploadedFile'

    def add_arguments(self, parser):
        parser.add_argument(
            '--file-id',
            type=str,
            help='Update only products from a specific uploaded file',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes',
        )

    def handle(self, *args, **options):
        file_id = options.get('file_id')
        dry_run = options.get('dry_run', False)

        if file_id:
            # Update specific file
            try:
                uploaded_file = UploadedFile.objects.get(id=file_id)
                files_to_process = [uploaded_file]
            except UploadedFile.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'UploadedFile with ID {file_id} not found')
                )
                return
        else:
            # Update all files that have a manufacturer code but products without it
            files_to_process = UploadedFile.objects.filter(
                selected_mfgcode__isnull=False
            )

        total_updated = 0

        for uploaded_file in files_to_process:
            if not uploaded_file.selected_mfgcode:
                self.stdout.write(
                    self.style.WARNING(
                        f'Skipping {uploaded_file.filename} - no manufacturer code set'
                    )
                )
                continue

            # Get products that need updating
            products = ProductData.objects.filter(
                uploaded_file=uploaded_file
            ).filter(
                models.Q(whi_mfgcode__isnull=True) | models.Q(whi_mfgcode='')
            )

            if products.count() == 0:
                self.stdout.write(
                    f'No products need updating for {uploaded_file.filename}'
                )
                continue

            mfgcode = uploaded_file.selected_mfgcode.mfgcode

            self.stdout.write(
                f'File: {uploaded_file.filename} ({uploaded_file.id})'
            )
            self.stdout.write(
                f'  Manufacturer Code: {mfgcode} ({uploaded_file.selected_mfgcode.brand})'
            )
            self.stdout.write(
                f'  Products to update: {products.count()}'
            )

            if not dry_run:
                updated_count = products.update(whi_mfgcode=mfgcode)
                self.stdout.write(
                    self.style.SUCCESS(f'  Updated {updated_count} products')
                )
                total_updated += updated_count
            else:
                self.stdout.write(
                    self.style.WARNING(f'  Would update {products.count()} products (dry run)')
                )

        if dry_run:
            self.stdout.write(
                self.style.WARNING('Dry run completed - no changes made')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'Total products updated: {total_updated}')
            )
