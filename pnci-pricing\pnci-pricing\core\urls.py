from django.urls import path
from . import views

app_name = 'core'

urlpatterns = [
    # Main wizard view
    path('', views.WizardView.as_view(), name='wizard'),
    
    # API endpoints
    path('api/upload-file/', views.UploadFileView.as_view(), name='upload_file'),
    path('api/manufacturer-codes/', views.ManufacturerCodesView.as_view(), name='manufacturer_codes'),
    path('api/start-crawl/', views.StartCrawlView.as_view(), name='start_crawl'),
    path('api/crawl-status/<uuid:job_id>/', views.CrawlStatusView.as_view(), name='crawl_status'),
    path('api/export-results/<uuid:file_id>/', views.ExportResultsView.as_view(), name='export_results'),
    path('api/settings/', views.SettingsView.as_view(), name='settings'),
    path('settings/', views.SettingsPageView.as_view(), name='settings_page'),
    path('api/save-settings/', views.SaveSettingsView.as_view(), name='save_settings'),
    path('api/proxy-config/', views.ProxyConfigView.as_view(), name='proxy_config'),
    path('api/test-proxy/', views.TestProxyView.as_view(), name='test_proxy'),
    path('api/test-database/', views.TestDatabaseView.as_view(), name='test_database'),
    path('api/check-existing-uploads/', views.CheckExistingUploadsView.as_view(), name='check_existing_uploads'),

    # Data mapping and validation
    path('mapping/<uuid:file_id>/', views.DataMappingPageView.as_view(), name='data_mapping_page'),
    path('interchange/<uuid:file_id>/', views.InterchangePageView.as_view(), name='interchange_page'),
    path('api/data-mapping/<uuid:file_id>/', views.DataMappingView.as_view(), name='data_mapping'),
    path('api/validate-data/<uuid:file_id>/', views.ValidateDataView.as_view(), name='validate_data'),
    path('api/import-to-production/<uuid:file_id>/', views.ImportToProductionView.as_view(), name='import_to_production'),
    path('api/interchange-lookup/<uuid:file_id>/', views.InterchangeLookupView.as_view(), name='interchange_lookup'),
    path('api/interchange-results/<uuid:file_id>/', views.InterchangeResultsView.as_view(), name='interchange_results'),
    path('api/debug-urls/<uuid:file_id>/', views.DebugURLsView.as_view(), name='debug_urls'),
    path('api/interchange-stats/<uuid:file_id>/', views.InterchangeStatsView.as_view(), name='interchange_stats'),

    # File downloads
    path('download/<uuid:file_id>/<str:format>/', views.DownloadResultsView.as_view(), name='download_results'),
]
