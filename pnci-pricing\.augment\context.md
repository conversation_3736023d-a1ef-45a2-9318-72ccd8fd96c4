# PNCI Competitive Pricing Analysis Tool

## Project Overview
This is a Django web application that provides a wizard-based interface for competitive pricing analysis in the automotive parts industry. The application allows users to import Excel files with part data, select retailers to scrape, and export pricing analysis results.

## Key Features

### 1. Multi-Step Wizard Interface
- **Step 1**: Import Excel file from Part Share and select WHI manufacturer code
- **Step 2**: Select retailers to scrape (Advance Auto, AutoZone, NAPA, O<PERSON>Reilly)
- **Step 3**: Run crawling operations with progress tracking
- **Step 4**: Export results in Excel or CSV format

### 2. Data Processing Workflow
1. User uploads Excel export from Part Share
2. System extracts part numbers and allows selection of WHI manufacturer code
3. System crawls WHI smartpages (e.g., https://smartpages.nexpart.com/smartpage.php?mfrlinecode=CEC&partnumber=301.00520&acesptermid=1684) to get interchange part numbers
4. System crawls selected retailers using the interchange part numbers
5. If interchange crawling yields limited results, fallback to manufacturer part numbers
6. Results are compiled into a pivot table format with one column per retailer

### 3. Technical Components
- **Backend**: Django with MySQL database
- **Frontend**: Tailwind CSS for styling
- **Proxy Support**: Integration with webshare.io proxy service
- **File Processing**: Excel file upload and parsing
- **Web Scraping**: Retailer website crawling with proxy support
- **Data Export**: Excel and CSV export functionality

### 4. Database Schema
- **Uploaded Files**: Store Excel file metadata and processing status
- **Part Numbers**: Store original part numbers from uploaded files
- **Manufacturer Codes**: Store WHI manufacturer codes and brand names
- **Interchange Data**: Store part number interchanges from WHI smartpages
- **Pricing Data**: Store scraped pricing information from retailers
- **Crawl Jobs**: Track crawling operations and their status

### 5. Configuration
- **Proxy Settings**: Configurable webshare.io proxy credentials
- **Database Settings**: MySQL connection configuration
- **Retailer Settings**: Configurable retailer endpoints and scraping parameters

## Architecture
- Django MVC pattern with class-based views
- Celery for background task processing (crawling operations)
- Redis for task queue and caching
- MySQL for persistent data storage
- RESTful API endpoints for AJAX operations
