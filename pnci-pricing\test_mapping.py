"""
Test the mapping functionality
"""

import requests
import json

def test_mapping_api():
    # Test with a fake file ID to see if the endpoint responds
    test_file_id = "12345678-1234-5678-9012-123456789012"  # Fake UUID
    
    print("🔍 Testing mapping API endpoint...")
    
    url = f'http://localhost:8000/api/data-mapping/{test_file_id}/'
    
    try:
        response = requests.get(url, timeout=10)
        print(f"📊 Response Status: {response.status_code}")
        print(f"📄 Response Content: {response.text[:500]}...")
        
        if response.status_code == 404:
            print("✅ Expected 404 for non-existent file ID - endpoint is working")
        elif response.status_code == 200:
            print("✅ Mapping API is responding")
        else:
            print(f"⚠️ Unexpected status code: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing mapping API: {e}")

def test_mapping_page():
    # Test the mapping page URL
    test_file_id = "12345678-1234-5678-9012-123456789012"  # Fake UUID
    
    print("\n🔍 Testing mapping page...")
    
    url = f'http://localhost:8000/mapping/{test_file_id}/'
    
    try:
        response = requests.get(url, timeout=10)
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Mapping page is accessible")
            # Check if it contains expected content
            if 'Data Mapping' in response.text:
                print("✅ Mapping page contains expected content")
            else:
                print("⚠️ Mapping page doesn't contain expected content")
        else:
            print(f"⚠️ Unexpected status code: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing mapping page: {e}")

if __name__ == '__main__':
    test_mapping_api()
    test_mapping_page()
