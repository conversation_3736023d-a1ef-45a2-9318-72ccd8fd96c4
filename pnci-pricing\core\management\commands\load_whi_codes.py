from django.core.management.base import BaseCommand
from core.models import ManufacturerCode
import re
import os
from django.conf import settings


class Command(BaseCommand):
    help = 'Load WHI manufacturer codes from whi-mfg-codes.txt file'

    def handle(self, *args, **options):
        self.stdout.write('Loading WHI manufacturer codes from file...')
        
        # Path to the file in project root
        file_path = os.path.join(settings.BASE_DIR, 'whi-mfg-codes.txt')
        
        if not os.path.exists(file_path):
            self.stdout.write(
                self.style.ERROR(f'File not found: {file_path}')
            )
            return
        
        # Clear existing codes
        deleted_count = ManufacturerCode.objects.count()
        if deleted_count > 0:
            ManufacturerCode.objects.all().delete()
            self.stdout.write(f'Deleted {deleted_count} existing manufacturer codes')
        
        # Read and parse the file
        codes_to_create = []
        pattern = r'<option value="([^"]+)">([^<]+)</option>'
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
                
                # Find all matches
                matches = re.findall(pattern, content)
                
                for mfg_code, brand_name in matches:
                    # Clean up the data
                    mfg_code = mfg_code.strip()
                    brand_name = brand_name.strip()

                    if mfg_code and brand_name:
                        codes_to_create.append(
                            ManufacturerCode(
                                mfgcode=mfg_code,
                                brand=brand_name
                            )
                        )
                
                # Bulk create
                if codes_to_create:
                    ManufacturerCode.objects.bulk_create(codes_to_create, batch_size=100)
                    self.stdout.write(
                        self.style.SUCCESS(f'Successfully loaded {len(codes_to_create)} manufacturer codes')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING('No manufacturer codes found in file')
                    )
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error reading file: {str(e)}')
            )
            
        # Show some examples
        sample_codes = ManufacturerCode.objects.all()[:5]
        if sample_codes:
            self.stdout.write('\nSample loaded codes:')
            for code in sample_codes:
                self.stdout.write(f'  {code.mfgcode} - {code.brand}')
                
        total_count = ManufacturerCode.objects.count()
        self.stdout.write(f'\nTotal manufacturer codes in database: {total_count}')
