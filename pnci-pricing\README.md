# PNCI Competitive Pricing Analysis Tool

A Django-based web application for competitive pricing analysis in the automotive parts industry. This tool provides a wizard-style interface for importing Excel files, scraping retailer websites, and exporting pricing analysis results.

## Features

### 🧙‍♂️ Wizard Interface
- **Step 1**: Import Excel files from Part Share exports
- **Step 2**: Select retailers to scrape (Advance Auto, AutoZone, NAPA, O'Reilly)
- **Step 3**: Run crawling operations with real-time progress tracking
- **Step 4**: Export results in Excel or CSV format

### 🔍 Data Processing
1. Upload Excel file with part numbers
2. Map columns to match AAIA standard format
3. Select WHI manufacturer code for interchange lookup
4. Process interchange lookup with proxy rotation
5. Crawl WHI smartpages to get aftermarket and OE interchange part numbers
6. Scrape selected retailers using interchange and original part numbers
7. Generate pivot table with pricing data from all retailers

### 🚀 Recent Improvements
- **Smart Query Logic**: Only processes products that need interchange lookup
- **Proxy Rotation**: Each request uses a unique IP address for reliable scraping
- **Simplified Output**: Clean progress tracking with part number and interchange count
- **Resume Functionality**: Continue processing from where you left off
- **Real-time Progress**: Live updates with accurate completion percentages

### 🛠️ Technical Features
- **Backend**: Django 4.2 with MySQL database
- **Frontend**: Tailwind CSS with responsive design
- **Background Tasks**: Celery with Redis for crawling operations
- **Proxy Support**: Full integration with webshare.io API for proxy rotation
- **File Processing**: Excel file upload and parsing with openpyxl
- **Export**: Excel and CSV export with pivot table formatting
- **Rate Limiting**: Intelligent proxy rotation to avoid IP blocking

## Installation

### Prerequisites
- Python 3.9+
- MySQL 5.7+ or 8.0+
- Redis (for Celery task queue)

### Setup Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd competitor-pricing
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your database and proxy settings
   ```

4. **Setup database**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   python manage.py setup_initial_data
   ```

5. **Create superuser (optional)**
   ```bash
   python manage.py createsuperuser
   ```

6. **Run the development server**
   ```bash
   python manage.py runserver
   ```

7. **Start Celery worker (in separate terminal)**
   ```bash
   celery -A pnci_tool worker --loglevel=info
   ```

## Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration
DB_NAME=pnci_tool
DB_USER=root
DB_PASSWORD=your-mysql-password
DB_HOST=localhost
DB_PORT=3306

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Webshare.io Proxy Configuration
WEBSHARE_PROXY_ENABLED=False
WEBSHARE_API_TOKEN=your-webshare-api-token
WEBSHARE_PROXY_MODE=direct
WEBSHARE_COUNTRY_CODES=US,CA,GB
```

### Database Setup

1. Create MySQL database:
   ```sql
   CREATE DATABASE pnci_tool CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. Create MySQL user (optional):
   ```sql
   CREATE USER 'pnci_user'@'localhost' IDENTIFIED BY 'your_password';
   GRANT ALL PRIVILEGES ON pnci_tool.* TO 'pnci_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

## Usage

### 1. Access the Application
Navigate to `http://localhost:8000` in your web browser.

### 2. Upload Excel File
- Click on the file drop area in Step 1
- Select an Excel file (.xlsx or .xls) containing part numbers
- The system will automatically process the file and extract part numbers

### 3. Select Manufacturer Code
- Choose the appropriate WHI manufacturer code from the dropdown
- Use the search box to filter codes by name or brand

### 4. Choose Retailers
- Select one or more retailers to scrape for pricing data
- Available retailers: Advance Auto Parts, AutoZone, NAPA, O'Reilly

### 5. Configure Webshare.io Proxy (Optional but Recommended)
- Click the Settings button to configure proxy settings
- Sign up for a [webshare.io](https://www.webshare.io) account
- Get your API token from the webshare.io dashboard
- Enter your API token in the settings and test the connection
- Enable proxy for web scraping to avoid IP blocking

### 6. Run Crawling
- Click "Start Crawling" to begin the analysis
- Monitor real-time progress for each retailer
- The system will first get interchanges from WHI smartpages
- Then crawl each retailer for pricing data

### 7. Export Results
- Once crawling is complete, export results in Excel or CSV format
- Excel export includes pivot table formatting
- CSV export provides raw data for further analysis

## API Endpoints

- `GET /` - Main wizard interface
- `POST /api/upload-file/` - Upload Excel file
- `GET /api/manufacturer-codes/` - Get manufacturer codes
- `POST /api/start-crawl/` - Start crawling process
- `GET /api/crawl-status/<job_id>/` - Get crawling status
- `GET /api/export-results/<file_id>/` - Get export summary
- `GET /download/<file_id>/<format>/` - Download results
- `GET|POST /api/settings/` - Manage settings

## Development

### Running Tests
```bash
python manage.py test
```

### Testing Webshare.io Integration
```bash
# Test webshare.io proxy connection (requires API token in .env)
python manage.py test_webshare

# Test with custom URL and number of tests
python manage.py test_webshare --test-url https://httpbin.org/ip --num-tests 10
```

### Code Structure
```
pnci_tool/
├── core/                   # Main application
│   ├── models.py          # Database models
│   ├── views.py           # API views
│   ├── tasks.py           # Celery tasks
│   ├── scrapers.py        # Web scraping logic
│   ├── utils.py           # Utility functions
│   └── management/        # Management commands
├── templates/             # HTML templates
├── static/               # Static files (CSS, JS)
├── media/                # Uploaded files and exports
└── requirements.txt      # Python dependencies
```

### Adding New Retailers
1. Create scraper class in `core/scrapers.py`
2. Add retailer to `RetailerScraperFactory`
3. Add retailer data in `setup_initial_data` command
4. Update frontend template with new retailer option

## Troubleshooting

### Common Issues

1. **MySQL Connection Error**
   - Verify MySQL is running
   - Check database credentials in `.env`
   - Ensure database exists

2. **Celery Tasks Not Running**
   - Start Redis server
   - Start Celery worker: `celery -A pnci_tool worker --loglevel=info`

3. **File Upload Errors**
   - Check file permissions on `media/uploads/` directory
   - Verify file size is under 10MB limit

4. **Scraping Failures**
   - Enable proxy settings if getting blocked
   - Check retailer website structure (may need scraper updates)
   - Verify internet connection

### Logs
- Django logs: `logs/django.log`
- Celery logs: Console output from worker process

## License

This project is proprietary software. All rights reserved.

## Support

For support and questions, please contact the development team.
