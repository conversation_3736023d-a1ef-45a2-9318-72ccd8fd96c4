# Generated manually to recreate InterchangeData model

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0005_remove_productdata_product_dat_uploade_97a8ad_idx_and_more'),
    ]

    operations = [
        # Drop the old InterchangeData table
        migrations.DeleteModel(
            name='InterchangeData',
        ),
        
        # Create the new InterchangeData model
        migrations.CreateModel(
            name='InterchangeData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('interchange_type', models.CharField(choices=[('AM', 'Aftermarket'), ('OE', 'Original Equipment')], max_length=2)),
                ('whi_mfgcode', models.Char<PERSON>ield(db_index=True, max_length=10)),
                ('whi_brand_name', models.Char<PERSON>ield(max_length=255)),
                ('interchange_brand_name', models.Char<PERSON><PERSON>(max_length=255)),
                ('interchange_part_number', models.Char<PERSON>ield(db_index=True, max_length=100)),
                ('interchange_mfgcode', models.CharField(blank=True, db_index=True, max_length=10, null=True)),
                ('smartpage_url', models.URLField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('product_data', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interchanges', to='core.productdata')),
            ],
            options={
                'db_table': 'interchange_data',
                'unique_together': {('product_data', 'interchange_type', 'interchange_part_number', 'interchange_brand_name')},
            },
        ),
        
        # Add indexes
        migrations.AddIndex(
            model_name='interchangedata',
            index=models.Index(fields=['interchange_part_number'], name='interchange_data_interchange_part_number_idx'),
        ),
        migrations.AddIndex(
            model_name='interchangedata',
            index=models.Index(fields=['whi_mfgcode'], name='interchange_data_whi_mfgcode_idx'),
        ),
        migrations.AddIndex(
            model_name='interchangedata',
            index=models.Index(fields=['interchange_mfgcode'], name='interchange_data_interchange_mfgcode_idx'),
        ),
        migrations.AddIndex(
            model_name='interchangedata',
            index=models.Index(fields=['interchange_type'], name='interchange_data_interchange_type_idx'),
        ),
    ]
