import pandas as pd
import xlsxwriter
import os
from django.conf import settings
from .models import PricingData, PartNumber


def export_to_excel(crawl_job):
    """Export crawl results to Excel with pivot table format"""
    try:
        # Get all pricing data for the job
        pricing_data = PricingData.objects.filter(crawl_job=crawl_job).select_related(
            'part_number', 'retailer'
        )
        
        # Create DataFrame
        data = []
        for item in pricing_data:
            data.append({
                'Part Number': item.part_number.part_number,
                'Manufacturer': item.part_number.manufacturer,
                'Searched Part': item.searched_part,
                'Found Part': item.found_part,
                'Retailer': item.retailer.name,
                'Price': float(item.price) if item.price else None,
                'Availability': item.availability,
                'Product Name': item.product_name,
                'Brand': item.brand,
                'Product URL': item.product_url,
                'Crawled At': item.crawled_at
            })
        
        df = pd.DataFrame(data)
        
        if df.empty:
            # Create empty file with headers
            df = pd.DataFrame(columns=[
                'Part Number', 'Manufacturer', 'Searched Part', 'Found Part',
                'Retailer', 'Price', 'Availability', 'Product Name', 'Brand',
                'Product URL', 'Crawled At'
            ])
        
        # Create pivot table
        if not df.empty and 'Price' in df.columns:
            pivot_df = df.pivot_table(
                index=['Part Number', 'Manufacturer'],
                columns='Retailer',
                values='Price',
                aggfunc='first'
            ).reset_index()
            
            # Fill NaN values
            pivot_df = pivot_df.fillna('')
        else:
            pivot_df = df
        
        # Create Excel file
        filename = f"pricing_analysis_{crawl_job.id}.xlsx"
        file_path = os.path.join(settings.MEDIA_ROOT, 'exports', filename)
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:
            # Write pivot table
            pivot_df.to_excel(writer, sheet_name='Pricing Analysis', index=False)
            
            # Write raw data
            df.to_excel(writer, sheet_name='Raw Data', index=False)
            
            # Get workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Pricing Analysis']
            
            # Add formatting
            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'top',
                'fg_color': '#D7E4BC',
                'border': 1
            })
            
            # Format headers
            for col_num, value in enumerate(pivot_df.columns.values):
                worksheet.write(0, col_num, value, header_format)
            
            # Auto-adjust column widths
            for i, col in enumerate(pivot_df.columns):
                max_len = max(
                    pivot_df[col].astype(str).map(len).max(),
                    len(str(col))
                ) + 2
                worksheet.set_column(i, i, min(max_len, 50))
        
        return file_path
        
    except Exception as e:
        raise Exception(f"Error exporting to Excel: {str(e)}")


def export_to_csv(crawl_job):
    """Export crawl results to CSV format"""
    try:
        # Get all pricing data for the job
        pricing_data = PricingData.objects.filter(crawl_job=crawl_job).select_related(
            'part_number', 'retailer'
        )
        
        # Create DataFrame
        data = []
        for item in pricing_data:
            data.append({
                'Part Number': item.part_number.part_number,
                'Manufacturer': item.part_number.manufacturer,
                'Searched Part': item.searched_part,
                'Found Part': item.found_part,
                'Retailer': item.retailer.name,
                'Price': float(item.price) if item.price else None,
                'Availability': item.availability,
                'Product Name': item.product_name,
                'Brand': item.brand,
                'Product URL': item.product_url,
                'Crawled At': item.crawled_at
            })
        
        df = pd.DataFrame(data)
        
        if df.empty:
            # Create empty file with headers
            df = pd.DataFrame(columns=[
                'Part Number', 'Manufacturer', 'Searched Part', 'Found Part',
                'Retailer', 'Price', 'Availability', 'Product Name', 'Brand',
                'Product URL', 'Crawled At'
            ])
        
        # Create CSV file
        filename = f"pricing_analysis_{crawl_job.id}.csv"
        file_path = os.path.join(settings.MEDIA_ROOT, 'exports', filename)
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        df.to_csv(file_path, index=False)
        
        return file_path
        
    except Exception as e:
        raise Exception(f"Error exporting to CSV: {str(e)}")


def create_pivot_table_data(crawl_job):
    """Create pivot table data structure for frontend display"""
    try:
        # Get all pricing data
        pricing_data = PricingData.objects.filter(crawl_job=crawl_job).select_related(
            'part_number', 'retailer'
        )
        
        # Group by part number
        pivot_data = {}
        retailers = set()
        
        for item in pricing_data:
            part_key = item.part_number.part_number
            retailer_name = item.retailer.name
            retailers.add(retailer_name)
            
            if part_key not in pivot_data:
                pivot_data[part_key] = {
                    'part_number': item.part_number.part_number,
                    'manufacturer': item.part_number.manufacturer,
                    'prices': {}
                }
            
            pivot_data[part_key]['prices'][retailer_name] = {
                'price': float(item.price) if item.price else None,
                'availability': item.availability,
                'url': item.product_url
            }
        
        return {
            'data': list(pivot_data.values()),
            'retailers': sorted(list(retailers))
        }
        
    except Exception as e:
        raise Exception(f"Error creating pivot table data: {str(e)}")


def validate_excel_file(file_path):
    """Validate uploaded Excel file structure"""
    try:
        # Try to read the file
        df = pd.read_excel(file_path, nrows=5)  # Read first 5 rows for validation
        
        if df.empty:
            return False, "File appears to be empty"
        
        # Check if there are any columns that might contain part numbers
        possible_part_cols = []
        for col in df.columns:
            col_lower = str(col).lower()
            if any(keyword in col_lower for keyword in ['part', 'pn', 'item', 'sku', 'number']):
                possible_part_cols.append(col)
        
        if not possible_part_cols:
            return False, "No part number columns detected. Please ensure your file contains part number data."
        
        return True, f"File validated successfully. Found potential part number columns: {', '.join(possible_part_cols)}"
        
    except Exception as e:
        return False, f"Error reading file: {str(e)}"


def get_file_info(file_path):
    """Get information about uploaded file"""
    try:
        df = pd.read_excel(file_path)
        
        return {
            'rows': len(df),
            'columns': len(df.columns),
            'column_names': list(df.columns),
            'sample_data': df.head(3).to_dict('records') if not df.empty else []
        }
        
    except Exception as e:
        return {
            'error': f"Error reading file: {str(e)}"
        }
