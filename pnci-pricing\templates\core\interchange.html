<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interchange Lookup - PNCI Tool</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .progress-bar {
            transition: width 0.3s ease;
        }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body class="bg-gray-50">
    {% csrf_token %}
    <div class="min-h-screen">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center">
                        <i class="fas fa-exchange-alt text-blue-600 text-2xl mr-3"></i>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">Interchange Lookup</h1>
                            <p class="text-sm text-gray-600" id="fileInfo">Loading file information...</p>
                        </div>
                    </div>
                    <div class="flex space-x-3">
                        <button onclick="window.history.back()" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg">
                            <i class="fas fa-arrow-left mr-2"></i>Back
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            
            <!-- Progress Steps -->
            <div class="mb-8">
                <div class="flex items-center justify-center">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                                <i class="fas fa-check"></i>
                            </div>
                            <span class="ml-2 text-sm font-medium text-green-600">File Uploaded</span>
                        </div>
                        <div class="w-16 h-1 bg-green-500"></div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                                <i class="fas fa-check"></i>
                            </div>
                            <span class="ml-2 text-sm font-medium text-green-600">Columns Mapped</span>
                        </div>
                        <div class="w-16 h-1 bg-green-500"></div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                                <i class="fas fa-check"></i>
                            </div>
                            <span class="ml-2 text-sm font-medium text-green-600">Data Validated</span>
                        </div>
                        <div class="w-16 h-1 bg-green-500"></div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                                <i class="fas fa-check"></i>
                            </div>
                            <span class="ml-2 text-sm font-medium text-green-600">Imported to Production</span>
                        </div>
                        <div class="w-16 h-1 bg-blue-500"></div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                                5
                            </div>
                            <span class="ml-2 text-sm font-medium text-blue-600">Interchange Lookup</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Interchange Summary Section -->
            <div class="bg-white rounded-lg shadow-sm border mb-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-chart-bar text-blue-600 mr-2"></i>
                        Interchange Summary
                    </h2>
                    <p class="text-sm text-gray-600 mt-1">Overview of products needing interchange lookup</p>
                </div>
                
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6" id="summaryStats">
                        <!-- Stats will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Sample Products Section -->
            <div class="bg-white rounded-lg shadow-sm border mb-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-list text-blue-600 mr-2"></i>
                        Products Needing Interchange Lookup
                    </h2>
                    <p class="text-sm text-gray-600 mt-1">Sample of products that will be processed (first 10 shown)</p>
                </div>
                
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200" id="sampleProductsTable">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Part Number</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Part Type</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Brand</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200" id="sampleProductsBody">
                                <!-- Sample products will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Preview Results Section (Hidden initially) -->
            <div id="previewResultsSection" class="bg-white rounded-lg shadow-sm border mb-8 hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-eye text-green-600 mr-2"></i>
                        Preview Results (First 5 Products)
                    </h2>
                    <p class="text-sm text-gray-600 mt-1">Review the interchange data collected from smart pages</p>
                </div>

                <div class="p-6">
                    <div id="previewResultsContainer">
                        <!-- Preview results will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-600" id="statusMessage">
                    Ready to process interchange lookup
                </div>

                <div class="flex space-x-3">
                    <button id="previewBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
                        <i class="fas fa-play mr-2"></i>
                        Process Interchange
                    </button>
                    <button id="fullProcessBtn" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg hidden">
                        <i class="fas fa-play mr-2"></i>
                        Process All Products
                    </button>
                    <button id="processRestBtn" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg hidden">
                        <i class="fas fa-forward mr-2"></i>
                        Process Rest of Items
                    </button>
                    <button id="viewResultsBtn" class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg hidden">
                        <i class="fas fa-eye mr-2"></i>
                        View All Results
                    </button>
                    <button id="debugUrlsBtn" class="bg-orange-600 hover:bg-orange-700 text-white px-6 py-2 rounded-lg hidden">
                        <i class="fas fa-bug mr-2"></i>
                        Debug URLs
                    </button>
                </div>
            </div>

            <!-- Progress Modal -->
            <div id="progressModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
                <div class="flex items-center justify-center min-h-screen p-4">
                    <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                            <h3 class="text-lg font-semibold text-gray-900">Interchange Lookup in Progress</h3>
                            <button onclick="hideProgressModal()" class="text-gray-400 hover:text-gray-600 text-xl font-bold">&times;</button>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div class="flex items-center">
                                    <div class="pulse-animation">
                                        <i class="fas fa-spinner fa-spin text-blue-600 text-2xl mr-3"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">Processing Smart Pages...</div>
                                        <div class="text-xs text-gray-500">This may take several minutes</div>
                                    </div>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full progress-bar" style="width: 0%" id="progressBar"></div>
                                </div>
                                <div class="text-center text-sm text-gray-600" id="progressText">
                                    Initializing...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let fileId = null;
        let taskId = null;
        let progressInterval = null;
        let isPreviewMode = false;

        // Get file ID from URL
        const urlParts = window.location.pathname.split('/');
        fileId = urlParts[urlParts.length - 2]; // Assuming URL like /interchange/{file_id}/

        document.addEventListener('DOMContentLoaded', function() {
            loadInterchangeData();

            // Load initial stats
            updateInterchangeStats();

            document.getElementById('previewBtn').addEventListener('click', startPreviewLookup);
            document.getElementById('fullProcessBtn').addEventListener('click', startFullProcess);
            document.getElementById('processRestBtn').addEventListener('click', startFullProcess); // Same function as full process
            document.getElementById('viewResultsBtn').addEventListener('click', viewAllResults);
            document.getElementById('debugUrlsBtn').addEventListener('click', viewDebugUrls);
        });

        function loadInterchangeData() {
            fetch(`/api/interchange-lookup/${fileId}/`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert('Error: ' + data.error);
                    return;
                }
                
                updateFileInfo(data);
                renderSummaryStats(data);
                renderSampleProducts(data.sample_products);
            })
            .catch(error => {
                console.error('Error loading interchange data:', error);
                alert('Failed to load interchange data');
            });
        }

        function updateFileInfo(data) {
            document.getElementById('fileInfo').textContent = 
                `${data.total_products} products imported - ${data.products_needing_lookup} need interchange lookup`;
        }

        function renderSummaryStats(data) {
            const container = document.getElementById('summaryStats');
            
            container.innerHTML = `
                <div class="bg-blue-50 p-4 rounded-lg text-center">
                    <div class="text-2xl font-bold text-blue-600">${data.total_products}</div>
                    <div class="text-sm text-blue-600">Total Products</div>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg text-center">
                    <div class="text-2xl font-bold text-yellow-600">${data.products_needing_lookup}</div>
                    <div class="text-sm text-yellow-600">Need Interchange</div>
                </div>
                <div class="bg-green-50 p-4 rounded-lg text-center">
                    <div class="text-2xl font-bold text-green-600">${data.products_with_interchanges}</div>
                    <div class="text-sm text-green-600">Already Processed</div>
                </div>
            `;
        }

        function renderSampleProducts(products) {
            const tbody = document.getElementById('sampleProductsBody');
            
            if (products.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="4" class="px-6 py-4 text-center text-gray-500">
                            All products already have interchange data
                        </td>
                    </tr>
                `;
                document.getElementById('previewBtn').style.display = 'none';
                document.getElementById('viewResultsBtn').classList.remove('hidden');
                return;
            }
            
            let html = '';
            products.forEach(product => {
                html += `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${product.part_number}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${product.part_type || 'N/A'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${product.brand || 'N/A'}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                Pending
                            </span>
                        </td>
                    </tr>
                `;
            });
            tbody.innerHTML = html;
        }

        function startPreviewLookup() {
            if (!confirm('Start interchange lookup processing? This will help verify the system is working correctly.')) {
                return;
            }

            const previewBtn = document.getElementById('previewBtn');
            previewBtn.disabled = true;
            previewBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Testing...';

            const formData = new FormData();
            formData.append('action', 'preview');
            formData.append('csrfmiddlewaretoken', getCsrfToken());

            fetch(`/api/interchange-lookup/${fileId}/`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    taskId = data.task_id;
                    isPreviewMode = true;
                    showProgressModal();
                    startPreviewProgressTracking();
                } else {
                    alert('Error: ' + data.error);
                    previewBtn.disabled = false;
                    previewBtn.innerHTML = '<i class="fas fa-play mr-2"></i>Process Interchange';
                }
            })
            .catch(error => {
                console.error('Error starting preview lookup:', error);
                alert('Failed to start preview lookup');
                previewBtn.disabled = false;
                previewBtn.innerHTML = '<i class="fas fa-play mr-2"></i>Process Interchange';
            });
        }

        function startFullProcess() {
            if (!confirm('Process ALL products for interchange lookup? This may take a very long time for large files.')) {
                return;
            }

            const fullBtn = document.getElementById('fullProcessBtn');
            fullBtn.disabled = true;
            fullBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';

            const formData = new FormData();
            formData.append('action', 'full_process');
            formData.append('csrfmiddlewaretoken', getCsrfToken());

            fetch(`/api/interchange-lookup/${fileId}/`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    taskId = data.task_id;
                    isPreviewMode = false;
                    showProgressModal();
                    startFullProgressTracking();
                } else {
                    alert('Error: ' + data.error);
                    fullBtn.disabled = false;
                    fullBtn.innerHTML = '<i class="fas fa-play mr-2"></i>Process All Products';
                }
            })
            .catch(error => {
                console.error('Error starting full process:', error);
                alert('Failed to start full process');
                fullBtn.disabled = false;
                fullBtn.innerHTML = '<i class="fas fa-play mr-2"></i>Process All Products';
            });
        }

        function viewAllResults() {
            // Load and display all interchange results
            fetch(`/api/interchange-results/${fileId}/`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayAllResults(data.results);
                } else {
                    alert('Error loading results: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error loading results:', error);
                alert('Failed to load results');
            });
        }

        function displayAllResults(results) {
            // For now, just show an alert with summary
            // In a full implementation, this would show a detailed results page
            const totalInterchanges = results.reduce((sum, r) => sum + r.total_interchanges, 0);
            alert(`Found ${results.length} products with ${totalInterchanges} total interchanges. Full results view coming soon!`);
        }

        function viewDebugUrls() {
            fetch(`/api/debug-urls/${fileId}/`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let debugInfo = `Debug URLs (${data.total_attempts} attempts):\n\n`;
                    data.debug_urls.forEach(url => {
                        debugInfo += `Part: ${url.part_number} (${url.part_type})\n`;
                        debugInfo += `Mfg Code: ${url.whi_mfgcode}\n`;
                        debugInfo += `URL: ${url.smartpage_url}\n`;
                        debugInfo += `Time: ${url.created_at}\n\n`;
                    });

                    // Create a modal or new window to display the URLs
                    const newWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
                    newWindow.document.write(`
                        <html>
                        <head><title>Debug URLs</title></head>
                        <body style="font-family: monospace; padding: 20px;">
                        <h2>Smart Page URLs Attempted</h2>
                        <pre>${debugInfo}</pre>
                        </body>
                        </html>
                    `);
                } else {
                    alert('Error loading debug URLs: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error loading debug URLs:', error);
                alert('Failed to load debug URLs');
            });
        }

        function showProgressModal() {
            document.getElementById('progressModal').classList.remove('hidden');
        }

        function hideProgressModal() {
            document.getElementById('progressModal').classList.add('hidden');
            // Clear any running intervals
            if (progressInterval) {
                clearInterval(progressInterval);
                progressInterval = null;
            }
            isPreviewMode = false;
        }

        function startPreviewProgressTracking() {
            // Simulate progress for preview (5 products)
            let progress = 0;
            progressInterval = setInterval(() => {
                progress += Math.random() * 20;
                if (progress > 100) progress = 100;

                document.getElementById('progressBar').style.width = progress + '%';
                document.getElementById('progressText').textContent =
                    `Testing first 5 products... ${Math.round(progress)}% complete`;

                if (progress >= 100) {
                    clearInterval(progressInterval);
                    setTimeout(() => {
                        isPreviewMode = false;
                        hideProgressModal();
                        loadPreviewResults();
                    }, 1000);
                }
            }, 1500);
        }

        function startFullProgressTracking() {
            // Real-time progress tracking with stats updates
            progressInterval = setInterval(() => {
                // Update stats and progress
                updateInterchangeStats();

                // Check if processing is complete by comparing stats
                fetch(`/api/interchange-stats/${fileId}/`)
                .then(response => response.json())
                .then(statsData => {
                    if (statsData.success) {
                        // Processing is complete when products_needing_lookup reaches 0
                        // OR when processing_progress reaches 100%
                        if (statsData.products_needing_lookup === 0 || statsData.processing_progress >= 100) {
                            clearInterval(progressInterval);
                            setTimeout(() => {
                                hideProgressModal();
                                alert(`Full interchange lookup completed! Processed ${statsData.products_with_interchanges} products and found ${statsData.total_interchanges_found} total interchanges.`);
                                loadInterchangeData();
                                document.getElementById('viewResultsBtn').classList.remove('hidden');
                                document.getElementById('debugUrlsBtn').classList.remove('hidden');
                            }, 1000);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error checking completion status:', error);
                });
            }, 3000); // Check every 3 seconds
        }

        function updateInterchangeStats() {
            fetch(`/api/interchange-stats/${fileId}/`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the summary stats
                    renderSummaryStats(data);

                    // Update file info
                    updateFileInfo(data);

                    // Update progress in modal if visible (but only for full processing, not preview)
                    const progressModal = document.getElementById('progressModal');
                    if (!progressModal.classList.contains('hidden') && !isPreviewMode) {
                        const progressBar = document.getElementById('progressBar');
                        const progressText = document.getElementById('progressText');

                        if (progressBar && progressText) {
                            progressBar.style.width = data.processing_progress + '%';
                            progressText.textContent =
                                `Processing... ${data.products_with_interchanges}/${data.total_products} products completed (${data.processing_progress}%)`;
                        }
                    }
                }
            })
            .catch(error => {
                console.error('Error updating stats:', error);
            });
        }

        function loadPreviewResults() {
            fetch(`/api/interchange-results/${fileId}/`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.results.length > 0) {
                    displayPreviewResults(data.results);
                    document.getElementById('previewBtn').style.display = 'none';
                    document.getElementById('processRestBtn').classList.remove('hidden');
                    document.getElementById('statusMessage').textContent =
                        `Preview completed! Found ${data.total_interchanges_found} interchanges. Ready to process remaining products.`;
                } else {
                    alert('No interchange data found in preview. Please check your manufacturer code and try again.');
                    document.getElementById('previewBtn').disabled = false;
                    document.getElementById('previewBtn').innerHTML = '<i class="fas fa-play mr-2"></i>Process Interchange';
                    // Show debug button to view attempted URLs
                    document.getElementById('debugUrlsBtn').classList.remove('hidden');
                }
            })
            .catch(error => {
                console.error('Error loading preview results:', error);
                alert('Failed to load preview results');
            });
        }

        function displayPreviewResults(results) {
            const section = document.getElementById('previewResultsSection');
            const container = document.getElementById('previewResultsContainer');

            let html = '<div class="space-y-6">';

            results.forEach(result => {
                html += `
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex justify-between items-start mb-3">
                            <div>
                                <h3 class="font-semibold text-gray-900">${result.product.part_number}</h3>
                                <p class="text-sm text-gray-600">${result.product.part_type} - ${result.product.brand}</p>
                            </div>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                ${result.total_interchanges} interchanges
                            </span>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-2">Aftermarket (${result.am_interchanges.length})</h4>
                                <div class="space-y-1">
                                    ${result.am_interchanges.map(am => `
                                        <div class="text-sm bg-blue-50 p-2 rounded">
                                            <span class="font-medium">${am.brand_name}</span> ${am.part_number}
                                            <span class="text-gray-500">(${am.mfgcode})</span>
                                        </div>
                                    `).join('')}
                                    ${result.am_interchanges.length === 0 ? '<div class="text-sm text-gray-500">No aftermarket interchanges</div>' : ''}
                                </div>
                            </div>

                            <div>
                                <h4 class="font-medium text-gray-900 mb-2">Original Equipment (${result.oe_interchanges.length})</h4>
                                <div class="space-y-1">
                                    ${result.oe_interchanges.map(oe => `
                                        <div class="text-sm bg-yellow-50 p-2 rounded">
                                            <span class="font-medium">${oe.brand_name}</span> ${oe.part_number}
                                            <span class="text-gray-500">(${oe.mfgcode})</span>
                                        </div>
                                    `).join('')}
                                    ${result.oe_interchanges.length === 0 ? '<div class="text-sm text-gray-500">No OE interchanges</div>' : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            container.innerHTML = html;
            section.classList.remove('hidden');
        }

        function getCsrfToken() {
            return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
        }
    </script>
</body>
</html>
