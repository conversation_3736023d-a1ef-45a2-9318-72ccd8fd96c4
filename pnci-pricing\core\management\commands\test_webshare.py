from django.core.management.base import BaseCommand
from django.conf import settings
from core.webshare_client import WebshareClient, ProxyRotator


class Command(BaseCommand):
    help = 'Test webshare.io proxy integration'

    def add_arguments(self, parser):
        parser.add_argument(
            '--test-url',
            type=str,
            default='https://httpbin.org/ip',
            help='URL to test proxy with (default: https://httpbin.org/ip)'
        )
        parser.add_argument(
            '--num-tests',
            type=int,
            default=5,
            help='Number of proxy tests to run (default: 5)'
        )

    def handle(self, *args, **options):
        self.stdout.write('Testing webshare.io proxy integration...')
        
        # Check if API token is configured
        api_token = getattr(settings, 'WEBSHARE_API_TOKEN', None)
        if not api_token:
            self.stdout.write(
                self.style.ERROR('WEBSHARE_API_TOKEN not configured in settings')
            )
            self.stdout.write('Please add your webshare.io API token to .env file:')
            self.stdout.write('WEBSHARE_API_TOKEN=your-api-token-here')
            return
        
        # Test API connection
        self.stdout.write('\n1. Testing API connection...')
        client = WebshareClient(api_token)
        
        connection_test = client.test_connection()
        if connection_test['success']:
            profile = connection_test['data']
            self.stdout.write(
                self.style.SUCCESS(f"✓ Connected to webshare.io API")
            )
            self.stdout.write(f"  Username: {profile.get('username', 'N/A')}")
            self.stdout.write(f"  Email: {profile.get('email', 'N/A')}")
            self.stdout.write(f"  Balance: ${profile.get('balance', 'N/A')}")
        else:
            self.stdout.write(
                self.style.ERROR(f"✗ API connection failed: {connection_test['error']}")
            )
            return
        
        # Test proxy list
        self.stdout.write('\n2. Testing proxy list retrieval...')
        proxy_result = client.get_proxy_list(page_size=10)
        
        if proxy_result['success']:
            data = proxy_result['data']
            proxies = data.get('results', [])
            total_count = data.get('count', 0)
            
            self.stdout.write(
                self.style.SUCCESS(f"✓ Retrieved proxy list")
            )
            self.stdout.write(f"  Total proxies available: {total_count}")
            self.stdout.write(f"  Sample proxies retrieved: {len(proxies)}")
            
            if proxies:
                sample_proxy = proxies[0]
                self.stdout.write(f"  Sample proxy: {sample_proxy['proxy_address']}:{sample_proxy['port']}")
                self.stdout.write(f"  Country: {sample_proxy.get('country_code', 'N/A')}")
                self.stdout.write(f"  Valid: {sample_proxy.get('valid', 'N/A')}")
        else:
            self.stdout.write(
                self.style.ERROR(f"✗ Proxy list retrieval failed: {proxy_result['error']}")
            )
            return
        
        # Test proxy rotation
        self.stdout.write('\n3. Testing proxy rotation...')
        rotator = ProxyRotator(client)
        
        if rotator.initialize_proxy_pool():
            info = rotator.get_proxy_info()
            self.stdout.write(
                self.style.SUCCESS(f"✓ Proxy pool initialized")
            )
            self.stdout.write(f"  Total proxies in pool: {info['total_proxies']}")
            
            # Test making requests through proxies
            self.stdout.write(f'\n4. Testing {options["num_tests"]} requests through different proxies...')
            
            import requests
            test_url = options['test_url']
            
            for i in range(options['num_tests']):
                try:
                    proxy = rotator.get_next_proxy()
                    if proxy:
                        response = requests.get(
                            test_url, 
                            proxies=proxy, 
                            timeout=10,
                            headers={
                                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                            }
                        )
                        
                        if response.status_code == 200:
                            # Extract IP from response if using httpbin
                            if 'httpbin.org' in test_url:
                                ip_data = response.json()
                                ip = ip_data.get('origin', 'Unknown')
                            else:
                                ip = 'Response received'
                            
                            proxy_endpoint = proxy['http'].split('@')[1]
                            self.stdout.write(f"  Test {i+1}: ✓ Success via {proxy_endpoint} -> {ip}")
                        else:
                            self.stdout.write(f"  Test {i+1}: ✗ HTTP {response.status_code}")
                    else:
                        self.stdout.write(f"  Test {i+1}: ✗ No proxy available")
                        
                except Exception as e:
                    self.stdout.write(f"  Test {i+1}: ✗ Error: {str(e)}")
        else:
            self.stdout.write(
                self.style.ERROR('✗ Failed to initialize proxy pool')
            )
            return
        
        self.stdout.write('\n5. Testing proxy refresh...')
        refresh_result = client.refresh_proxy_list()
        if refresh_result['success']:
            self.stdout.write(
                self.style.SUCCESS('✓ Proxy list refresh requested')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'⚠ Proxy refresh failed: {refresh_result["error"]}')
            )
        
        self.stdout.write('\n' + '='*50)
        self.stdout.write(
            self.style.SUCCESS('Webshare.io integration test completed!')
        )
        self.stdout.write('\nYour proxy integration is ready to use.')
        self.stdout.write('Enable proxies in the web interface settings or set WEBSHARE_PROXY_ENABLED=True in .env')
