#!/usr/bin/env python
"""
Setup script for PNCI Competitive Pricing Analysis Tool
"""

import os
import sys
import subprocess
import secrets
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def create_env_file():
    """Create .env file from template"""
    env_example = Path('.env.example')
    env_file = Path('.env')
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    if not env_example.exists():
        print("❌ .env.example file not found")
        return False
    
    print("\n🔄 Creating .env file...")
    
    # Read template
    with open(env_example, 'r') as f:
        content = f.read()
    
    # Generate secret key
    secret_key = secrets.token_urlsafe(50)
    content = content.replace('your-secret-key-here', secret_key)
    
    # Write .env file
    with open(env_file, 'w') as f:
        f.write(content)
    
    print("✅ .env file created with generated secret key")
    print("⚠️  Please update database credentials in .env file")
    return True

def check_python_version():
    """Check if Python version is compatible"""
    print("🔍 Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 9:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible")
        print("   Please install Python 3.9 or higher")
        return False

def install_dependencies():
    """Install Python dependencies"""
    return run_command("pip install -r requirements.txt", "Installing Python dependencies")

def setup_database():
    """Setup database migrations"""
    commands = [
        ("python manage.py makemigrations", "Creating database migrations"),
        ("python manage.py migrate", "Running database migrations"),
        ("python manage.py setup_initial_data", "Setting up initial data")
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            return False
    return True

def create_superuser():
    """Prompt to create superuser"""
    print("\n🔄 Creating superuser account...")
    print("You can create an admin account to access the Django admin interface.")
    create = input("Would you like to create a superuser now? (y/N): ").lower().strip()
    
    if create in ['y', 'yes']:
        try:
            subprocess.run("python manage.py createsuperuser", shell=True, check=True)
            print("✅ Superuser created successfully")
        except subprocess.CalledProcessError:
            print("❌ Failed to create superuser")
            return False
    else:
        print("⏭️  Skipping superuser creation")
    
    return True

def check_services():
    """Check if required services are available"""
    print("\n🔍 Checking required services...")
    
    # Check MySQL
    mysql_check = run_command("mysql --version", "Checking MySQL installation")
    if not mysql_check:
        print("⚠️  MySQL not found. Please install MySQL 5.7+ or 8.0+")
    
    # Check Redis
    redis_check = run_command("redis-cli ping", "Checking Redis connection")
    if not redis_check:
        print("⚠️  Redis not available. Please install and start Redis server")
    
    return mysql_check and redis_check

def main():
    """Main setup function"""
    print("🚀 PNCI Competitive Pricing Analysis Tool Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create .env file
    if not create_env_file():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies")
        sys.exit(1)
    
    # Check services
    services_ok = check_services()
    
    # Setup database (only if services are available)
    if services_ok:
        if not setup_database():
            print("❌ Database setup failed")
            print("   Please check your database configuration in .env file")
            sys.exit(1)
        
        # Create superuser
        create_superuser()
    else:
        print("\n⚠️  Skipping database setup due to missing services")
        print("   Please install MySQL and Redis, then run:")
        print("   python manage.py makemigrations")
        print("   python manage.py migrate")
        print("   python manage.py setup_initial_data")
    
    print("\n🎉 Setup completed!")
    print("\nNext steps:")
    print("1. Update database credentials in .env file")
    print("2. Start Redis server")
    print("3. Run: python manage.py runserver")
    print("4. In another terminal, run: celery -A pnci_tool worker --loglevel=info")
    print("5. Open http://localhost:8000 in your browser")
    
    if not services_ok:
        print("\n⚠️  Don't forget to install missing services:")
        print("   - MySQL 5.7+ or 8.0+")
        print("   - Redis server")

if __name__ == "__main__":
    main()
