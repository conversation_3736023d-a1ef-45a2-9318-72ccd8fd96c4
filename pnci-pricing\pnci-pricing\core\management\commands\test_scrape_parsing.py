from django.core.management.base import BaseCommand
from bs4 import BeautifulSoup
from core.models import UploadedFile, ProductData, InterchangeData
from core.tasks import scrape_interchange_table_with_proxy


class Command(BaseCommand):
    help = 'Test scraping parsing with saved HTML'

    def add_arguments(self, parser):
        parser.add_argument('file_id', type=str, help='UploadedFile ID')

    def handle(self, *args, **options):
        file_id = options['file_id']
        
        try:
            # Get the uploaded file and first product
            uploaded_file = UploadedFile.objects.get(id=file_id)
            product = ProductData.objects.filter(uploaded_file=uploaded_file).first()
            
            if not product:
                self.stdout.write(self.style.ERROR("No products found"))
                return
                
            whi_mfgcode = uploaded_file.selected_mfgcode.mfgcode
            whi_brand_name = uploaded_file.selected_mfgcode.brand
            
            self.stdout.write(f"Testing with product: {product.PartNumber}")
            self.stdout.write(f"WHI Code: {whi_mfgcode}")
            
            # Load the saved HTML
            try:
                with open('whi_page_sample.html', 'r', encoding='utf-8') as f:
                    html_content = f.read()
            except FileNotFoundError:
                self.stdout.write(self.style.ERROR("whi_page_sample.html not found. Run test_scrape_whi first."))
                return
            
            soup = BeautifulSoup(html_content, 'html.parser')
            smartpage_url = "https://smartpages.nexpart.com/smartpage.php?mfrlinecode=AIS&partnumber=ATF-0WS&acesptermid=11387"
            
            # Clean up any existing interchange records for this product
            existing_interchanges = InterchangeData.objects.filter(product_data=product)
            if existing_interchanges.exists():
                count = existing_interchanges.count()
                existing_interchanges.delete()
                self.stdout.write(f"Deleted {count} existing interchange records")
            
            # Test OE scraping
            self.stdout.write("\n--- Testing OE Interchange Scraping ---")
            oe_count = scrape_interchange_table_with_proxy(soup, 'OE', product, whi_mfgcode, whi_brand_name, smartpage_url)
            self.stdout.write(f"OE Interchanges found: {oe_count}")
            
            # Test AM scraping
            self.stdout.write("\n--- Testing AM Interchange Scraping ---")
            am_count = scrape_interchange_table_with_proxy(soup, 'AM', product, whi_mfgcode, whi_brand_name, smartpage_url)
            self.stdout.write(f"AM Interchanges found: {am_count}")
            
            # Show some sample records
            self.stdout.write(f"\n--- Sample Interchange Records ---")
            sample_records = InterchangeData.objects.filter(product_data=product)[:10]
            for record in sample_records:
                self.stdout.write(f"{record.interchange_type}: {record.interchange_brand_name} -> {record.interchange_part_number}")
            
            total_count = oe_count + am_count
            self.stdout.write(f"\n✅ Total interchanges created: {total_count}")
            self.stdout.write("Expected: 15 OE + 21 AM = 36 total")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error: {str(e)}"))
