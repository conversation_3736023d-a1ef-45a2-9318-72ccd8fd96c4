"""
Custom middleware for PNCI Pricing Tool
"""

class SecurityHeadersMiddleware:
    """
    Custom middleware to handle security headers and prevent console warnings
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        
        # Remove or modify problematic headers
        if 'Cross-Origin-Opener-Policy' in response:
            del response['Cross-Origin-Opener-Policy']
        
        # Add cache control for static-like content
        if request.path.endswith('.js') or request.path.endswith('.css'):
            response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response['Pragma'] = 'no-cache'
            response['Expires'] = '0'
        
        # Add security headers for main pages
        if request.path == '/':
            response['X-Content-Type-Options'] = 'nosniff'
            response['X-Frame-Options'] = 'DENY'
            response['Referrer-Policy'] = 'same-origin'
        
        return response


class CacheBustingMiddleware:
    """
    Middleware to add cache-busting headers for development/production
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        
        # Add cache-busting headers for HTML pages
        if response.get('Content-Type', '').startswith('text/html'):
            response['Cache-Control'] = 'no-cache, no-store, must-revalidate, max-age=0'
            response['Pragma'] = 'no-cache'
            response['Expires'] = '0'
            response['Last-Modified'] = 'Thu, 01 Jan 1970 00:00:00 GMT'
        
        return response
