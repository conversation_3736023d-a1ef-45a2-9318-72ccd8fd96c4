"""
Crawl logging utility for tracking URL requests and proxy usage
"""

import time
import logging
from datetime import datetime
from urllib.parse import urlparse
from django.utils import timezone
from .models import CrawlLog

logger = logging.getLogger(__name__)


class CrawlLogger:
    """Context manager for logging crawl activities"""
    
    def __init__(self, url, method='GET', crawl_type='interchange', 
                 part_number=None, manufacturer_code=None):
        self.url = url
        self.method = method
        self.crawl_type = crawl_type
        self.part_number = part_number
        self.manufacturer_code = manufacturer_code
        self.start_time = None
        self.log_entry = None
        
    def __enter__(self):
        """Start logging the crawl request"""
        self.start_time = time.time()
        
        # Create initial log entry
        self.log_entry = CrawlLog.objects.create(
            url=self.url,
            method=self.method,
            crawl_type=self.crawl_type,
            part_number=self.part_number,
            manufacturer_code=self.manufacturer_code,
            started_at=timezone.now()
        )
        
        logger.info(f"Starting crawl: {self.method} {self.url}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Complete logging the crawl request"""
        if self.log_entry:
            self.log_entry.completed_at = timezone.now()
            
            # Calculate response time
            if self.start_time:
                response_time_ms = int((time.time() - self.start_time) * 1000)
                self.log_entry.response_time_ms = response_time_ms
            
            # Log any errors
            if exc_type:
                self.log_entry.error_message = str(exc_val)
                logger.error(f"Crawl failed: {self.url} - {exc_val}")
            
            self.log_entry.save()
            
            # Log completion
            proxy_info = ""
            if self.log_entry.proxy_used:
                proxy_info = f" via {self.log_entry.proxy_ip}:{self.log_entry.proxy_port}"
            
            logger.info(f"Completed crawl: {self.url}{proxy_info} - "
                       f"{self.log_entry.status_code} ({self.log_entry.response_time_ms}ms)")
    
    def set_proxy_info(self, proxy_dict, provider='webshare.io'):
        """Set proxy information from proxy dictionary"""
        if proxy_dict and self.log_entry:
            self.log_entry.proxy_used = True
            self.log_entry.proxy_provider = provider
            
            # Extract IP and port from proxy URL
            # Format: ***************************:port
            if 'http' in proxy_dict:
                proxy_url = proxy_dict['http']
                try:
                    # Parse the proxy URL
                    if '@' in proxy_url:
                        # Extract IP:port from username:password@ip:port
                        ip_port = proxy_url.split('@')[1]
                        if ':' in ip_port:
                            ip, port = ip_port.split(':')
                            self.log_entry.proxy_ip = ip
                            self.log_entry.proxy_port = int(port)
                except Exception as e:
                    logger.warning(f"Failed to parse proxy URL: {proxy_url} - {e}")
            
            self.log_entry.save()
    
    def set_response_info(self, response):
        """Set response information from requests response object"""
        if response and self.log_entry:
            self.log_entry.status_code = response.status_code
            
            # Get content length
            if hasattr(response, 'content'):
                self.log_entry.content_length = len(response.content)
            elif 'content-length' in response.headers:
                try:
                    self.log_entry.content_length = int(response.headers['content-length'])
                except ValueError:
                    pass
            
            self.log_entry.save()
    
    def set_retry_count(self, count):
        """Set the retry count for this request"""
        if self.log_entry:
            self.log_entry.retry_count = count
            self.log_entry.save()


def log_crawl_request(url, method='GET', crawl_type='interchange', 
                     part_number=None, manufacturer_code=None):
    """
    Decorator/context manager factory for logging crawl requests
    
    Usage:
        with log_crawl_request(url, part_number='ABC123') as logger:
            logger.set_proxy_info(proxy_dict)
            response = requests.get(url, proxies=proxy_dict)
            logger.set_response_info(response)
    """
    return CrawlLogger(url, method, crawl_type, part_number, manufacturer_code)


def get_crawl_stats(hours=24):
    """Get crawl statistics for the last N hours"""
    from django.utils import timezone
    from datetime import timedelta
    
    since = timezone.now() - timedelta(hours=hours)
    logs = CrawlLog.objects.filter(started_at__gte=since)
    
    stats = {
        'total_requests': logs.count(),
        'successful_requests': logs.filter(status_code__range=(200, 299)).count(),
        'failed_requests': logs.filter(status_code__gte=400).count(),
        'proxy_requests': logs.filter(proxy_used=True).count(),
        'direct_requests': logs.filter(proxy_used=False).count(),
        'avg_response_time': 0,
        'unique_ips': [],
        'crawl_types': {},
    }
    
    # Calculate average response time
    response_times = logs.filter(response_time_ms__isnull=False).values_list('response_time_ms', flat=True)
    if response_times:
        stats['avg_response_time'] = sum(response_times) / len(response_times)
    
    # Get unique proxy IPs
    proxy_ips = logs.filter(proxy_used=True, proxy_ip__isnull=False).values_list('proxy_ip', flat=True).distinct()
    stats['unique_ips'] = list(proxy_ips)
    
    # Count by crawl type
    from django.db import models
    crawl_types = logs.values('crawl_type').annotate(count=models.Count('id'))
    stats['crawl_types'] = {ct['crawl_type']: ct['count'] for ct in crawl_types}
    
    return stats


def get_recent_crawls(limit=50):
    """Get recent crawl logs with details"""
    return CrawlLog.objects.select_related().order_by('-started_at')[:limit]
