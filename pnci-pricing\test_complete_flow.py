"""
Test the complete upload and mapping flow
"""

import requests
import os
import re

def get_csrf_token():
    """Get CSRF token from the main page"""
    session = requests.Session()
    response = session.get('http://localhost:8000/')
    
    if response.status_code != 200:
        print(f"❌ Failed to get main page: {response.status_code}")
        return None, None
    
    # Extract CSRF token from response
    csrf_token = None
    for line in response.text.split('\n'):
        if 'csrfToken' in line and '=' in line:
            # Extract token from JavaScript
            start = line.find("'") + 1
            end = line.find("'", start)
            if start > 0 and end > start:
                csrf_token = line[start:end]
                break
    
    return session, csrf_token

def test_complete_flow():
    print("🚀 Testing complete upload and mapping flow...")
    
    # Check if test file exists
    test_file = 'test_parts_upload.xlsx'
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found")
        return
    
    print(f"📁 Using test file: {test_file}")
    
    # Step 1: Get CSRF token and session
    session, csrf_token = get_csrf_token()
    if not csrf_token:
        print("❌ Could not get CSRF token")
        return
    
    print(f"🔑 Got CSRF token: {csrf_token[:10]}...")
    
    # Step 2: Upload the file
    print("📤 Uploading file...")
    
    with open(test_file, 'rb') as f:
        files = {'file': f}
        data = {
            'selected_mfgcode': 'AIS',  # Test with AISIN
        }
        headers = {
            'X-CSRFToken': csrf_token
        }
        
        response = session.post('http://localhost:8000/api/upload-file/', 
                               files=files, data=data, headers=headers)
    
    print(f"📊 Upload Response Status: {response.status_code}")
    
    if response.status_code != 200:
        print(f"❌ Upload failed: {response.text}")
        return
    
    try:
        upload_result = response.json()
        if not upload_result.get('success'):
            print(f"❌ Upload failed: {upload_result.get('error')}")
            return
        
        file_id = upload_result.get('file_id')
        print(f"✅ Upload successful! File ID: {file_id}")
        
    except Exception as e:
        print(f"❌ Failed to parse upload response: {e}")
        return
    
    # Step 3: Test mapping API
    print("🔍 Testing mapping API...")
    
    mapping_url = f'http://localhost:8000/api/data-mapping/{file_id}/'
    response = session.get(mapping_url)
    
    print(f"📊 Mapping API Status: {response.status_code}")
    
    if response.status_code == 200:
        try:
            mapping_data = response.json()
            print("✅ Mapping API working!")
            print(f"📋 Source columns: {mapping_data.get('source_columns', [])}")
            print(f"🎯 Target fields: {list(mapping_data.get('target_fields', {}).keys())}")
            print(f"🔗 Current mappings: {mapping_data.get('current_mappings', {})}")
            
        except Exception as e:
            print(f"❌ Failed to parse mapping response: {e}")
            print(f"📄 Raw response: {response.text[:500]}...")
    else:
        print(f"❌ Mapping API failed: {response.text[:200]}...")
    
    # Step 4: Test mapping page
    print("🌐 Testing mapping page...")
    
    mapping_page_url = f'http://localhost:8000/mapping/{file_id}/'
    response = session.get(mapping_page_url)
    
    print(f"📊 Mapping Page Status: {response.status_code}")
    
    if response.status_code == 200:
        if 'Data Mapping' in response.text:
            print("✅ Mapping page loads successfully!")
        else:
            print("⚠️ Mapping page loads but doesn't contain expected content")
    else:
        print(f"❌ Mapping page failed: {response.status_code}")
    
    print(f"\n🎯 Complete test finished! File ID for manual testing: {file_id}")
    print(f"🌐 Manual mapping URL: http://**************:8000/mapping/{file_id}/")

if __name__ == '__main__':
    test_complete_flow()
