# Generated by Django 4.2.7 on 2025-06-09 19:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0010_auto_20250603_1745'),
    ]

    operations = [
        migrations.CreateModel(
            name='AcesManufacturer',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('mfr_id', models.IntegerField(db_column='MfrID', unique=True)),
                ('mfr_name', models.CharField(db_column='MfrName', max_length=255)),
            ],
            options={
                'db_table': 'manufacturers',
                'ordering': ['mfr_name'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='CrawlLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('url', models.URLField(max_length=500)),
                ('method', models.CharField(default='GET', max_length=10)),
                ('proxy_used', models.BooleanField(default=False)),
                ('proxy_ip', models.GenericIPAddressField(blank=True, null=True)),
                ('proxy_port', models.IntegerField(blank=True, null=True)),
                ('proxy_provider', models.CharField(blank=True, max_length=50, null=True)),
                ('status_code', models.IntegerField(blank=True, null=True)),
                ('response_time_ms', models.IntegerField(blank=True, null=True)),
                ('content_length', models.IntegerField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('retry_count', models.IntegerField(default=0)),
                ('crawl_type', models.CharField(default='interchange', max_length=50)),
                ('part_number', models.CharField(blank=True, max_length=100, null=True)),
                ('manufacturer_code', models.CharField(blank=True, max_length=10, null=True)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'core_crawllog',
                'ordering': ['-started_at'],
                'indexes': [models.Index(fields=['started_at'], name='core_crawll_started_04fbd4_idx'), models.Index(fields=['proxy_used', 'proxy_ip'], name='core_crawll_proxy_u_74d48f_idx'), models.Index(fields=['crawl_type', 'status_code'], name='core_crawll_crawl_t_13be98_idx'), models.Index(fields=['part_number', 'manufacturer_code'], name='core_crawll_part_nu_01a721_idx')],
            },
        ),
    ]
