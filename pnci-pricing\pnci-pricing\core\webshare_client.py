"""
Webshare.io API client for proxy management
"""
import requests
import random
import logging
from typing import List, Dict, Optional
from django.conf import settings
from django.core.cache import cache

logger = logging.getLogger(__name__)


class WebshareClient:
    """Client for interacting with webshare.io API"""
    
    BASE_URL = "https://proxy.webshare.io/api/v2"
    
    def __init__(self, api_token: str):
        self.api_token = api_token
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Token {api_token}',
            'Content-Type': 'application/json'
        })
    
    def test_connection(self) -> Dict:
        """Test the API connection by fetching user profile"""
        try:
            response = self.session.get(f"{self.BASE_URL}/profile/")
            response.raise_for_status()
            return {
                'success': True,
                'data': response.json()
            }
        except Exception as e:
            logger.error(f"Webshare API connection test failed: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_proxy_list(self, mode: str = "direct", page_size: int = 100, 
                      country_codes: Optional[List[str]] = None) -> Dict:
        """
        Get list of available proxies
        
        Args:
            mode: 'direct' or 'backbone'
            page_size: Number of proxies per page
            country_codes: List of country codes to filter by (e.g., ['US', 'CA'])
        """
        try:
            params = {
                'mode': mode,
                'page_size': page_size
            }
            
            if country_codes:
                params['country_code__in'] = ','.join(country_codes)
            
            response = self.session.get(f"{self.BASE_URL}/proxy/list/", params=params)
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"Retrieved {len(data.get('results', []))} proxies from webshare.io")
            
            return {
                'success': True,
                'data': data
            }
            
        except Exception as e:
            logger.error(f"Failed to get proxy list: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_all_proxies(self, mode: str = "direct", 
                       country_codes: Optional[List[str]] = None) -> List[Dict]:
        """Get all available proxies by paginating through all pages"""
        all_proxies = []
        page = 1
        
        while True:
            try:
                params = {
                    'mode': mode,
                    'page': page,
                    'page_size': 100
                }
                
                if country_codes:
                    params['country_code__in'] = ','.join(country_codes)
                
                response = self.session.get(f"{self.BASE_URL}/proxy/list/", params=params)
                response.raise_for_status()
                
                data = response.json()
                results = data.get('results', [])
                
                if not results:
                    break
                
                all_proxies.extend(results)
                
                # Check if there's a next page
                if not data.get('next'):
                    break
                
                page += 1
                
            except Exception as e:
                logger.error(f"Error fetching proxies page {page}: {str(e)}")
                break
        
        logger.info(f"Retrieved total of {len(all_proxies)} proxies")
        return all_proxies
    
    def refresh_proxy_list(self) -> Dict:
        """Request on-demand refresh of proxy list"""
        try:
            response = self.session.post(f"{self.BASE_URL}/proxy/list/refresh/")
            response.raise_for_status()
            
            return {
                'success': True,
                'data': response.json()
            }
            
        except Exception as e:
            logger.error(f"Failed to refresh proxy list: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }


class ProxyRotator:
    """Manages proxy rotation for web scraping"""
    
    def __init__(self, webshare_client: WebshareClient):
        self.client = webshare_client
        self.proxy_pool = []
        self.current_index = 0
        self.cache_key = "webshare_proxy_pool"
        self.cache_timeout = 3600  # 1 hour
    
    def initialize_proxy_pool(self, force_refresh: bool = False) -> bool:
        """Initialize the proxy pool from webshare.io"""
        if not force_refresh:
            # Try to get from cache first
            cached_proxies = cache.get(self.cache_key)
            if cached_proxies:
                self.proxy_pool = cached_proxies
                logger.info(f"Loaded {len(self.proxy_pool)} proxies from cache")
                return True
        
        # Fetch fresh proxies from API
        result = self.client.get_all_proxies()
        if result:
            # Filter only valid proxies
            valid_proxies = [proxy for proxy in result if proxy.get('valid', False)]
            self.proxy_pool = valid_proxies
            
            # Cache the proxies
            cache.set(self.cache_key, self.proxy_pool, self.cache_timeout)
            
            logger.info(f"Initialized proxy pool with {len(self.proxy_pool)} valid proxies")
            return True
        
        logger.error("Failed to initialize proxy pool")
        return False
    
    def get_next_proxy(self) -> Optional[Dict]:
        """Get the next proxy in rotation"""
        if not self.proxy_pool:
            if not self.initialize_proxy_pool():
                return None
        
        if not self.proxy_pool:
            return None
        
        proxy = self.proxy_pool[self.current_index]
        self.current_index = (self.current_index + 1) % len(self.proxy_pool)
        
        return {
            'http': f"http://{proxy['username']}:{proxy['password']}@{proxy['proxy_address']}:{proxy['port']}",
            'https': f"http://{proxy['username']}:{proxy['password']}@{proxy['proxy_address']}:{proxy['port']}"
        }
    
    def get_random_proxy(self) -> Optional[Dict]:
        """Get a random proxy from the pool"""
        if not self.proxy_pool:
            if not self.initialize_proxy_pool():
                return None
        
        if not self.proxy_pool:
            return None
        
        proxy = random.choice(self.proxy_pool)
        
        return {
            'http': f"http://{proxy['username']}:{proxy['password']}@{proxy['proxy_address']}:{proxy['port']}",
            'https': f"http://{proxy['username']}:{proxy['password']}@{proxy['proxy_address']}:{proxy['port']}"
        }
    
    def get_proxy_info(self) -> Dict:
        """Get information about the current proxy pool"""
        return {
            'total_proxies': len(self.proxy_pool),
            'current_index': self.current_index,
            'has_proxies': len(self.proxy_pool) > 0
        }
    
    def refresh_pool(self) -> bool:
        """Force refresh the proxy pool"""
        # Clear cache
        cache.delete(self.cache_key)
        
        # Refresh proxies on webshare.io
        refresh_result = self.client.refresh_proxy_list()
        if refresh_result.get('success'):
            logger.info("Requested proxy list refresh from webshare.io")
        
        # Reinitialize pool
        return self.initialize_proxy_pool(force_refresh=True)


def get_webshare_client() -> Optional[WebshareClient]:
    """Get configured webshare client from settings"""
    api_token = getattr(settings, 'WEBSHARE_API_TOKEN', None)
    
    if not api_token:
        logger.warning("WEBSHARE_API_TOKEN not configured in settings")
        return None
    
    return WebshareClient(api_token)


def get_proxy_rotator() -> Optional[ProxyRotator]:
    """Get configured proxy rotator"""
    client = get_webshare_client()
    if not client:
        return None
    
    return ProxyRotator(client)
