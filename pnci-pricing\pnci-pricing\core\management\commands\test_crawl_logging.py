"""
Test command to demonstrate crawl logging functionality
"""

from django.core.management.base import BaseCommand
from core.crawl_logger import log_crawl_request
import requests
import time


class Command(BaseCommand):
    help = 'Test crawl logging functionality with sample requests'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=5,
            help='Number of test requests to make',
        )
        parser.add_argument(
            '--with-proxy',
            action='store_true',
            help='Simulate proxy usage in logs',
        )

    def handle(self, *args, **options):
        count = options['count']
        with_proxy = options['with_proxy']
        
        self.stdout.write(self.style.SUCCESS(f'Starting crawl logging test with {count} requests...'))
        
        # Sample URLs to test (using httpbin.org for testing)
        test_urls = [
            'https://httpbin.org/status/200',
            'https://httpbin.org/delay/1',
            'https://httpbin.org/json',
            'https://httpbin.org/user-agent',
            'https://httpbin.org/headers',
        ]
        
        # Sample part numbers and manufacturer codes for testing
        test_parts = ['ABC123', 'DEF456', 'GHI789', 'JKL012', 'MNO345']
        test_mfg_codes = ['AIS', 'BOS', 'MOT', 'CAR', 'VAL']
        
        for i in range(count):
            url = test_urls[i % len(test_urls)]
            part_number = test_parts[i % len(test_parts)]
            mfg_code = test_mfg_codes[i % len(test_mfg_codes)]
            
            self.stdout.write(f'Request {i+1}/{count}: {url}')
            
            try:
                # Use the crawl logger
                with log_crawl_request(
                    url=url,
                    crawl_type='test_interchange',
                    part_number=part_number,
                    manufacturer_code=mfg_code
                ) as crawl_log:
                    
                    # Simulate proxy usage if requested
                    if with_proxy:
                        # Create a fake proxy dict for testing
                        fake_proxy = {
                            'http': f'**************************.{100 + i}:8080',
                            'https': f'***************************.{100 + i}:8080'
                        }
                        crawl_log.set_proxy_info(fake_proxy, 'webshare.io')
                    
                    # Make the actual request
                    response = requests.get(url, timeout=10)
                    crawl_log.set_response_info(response)
                    
                    self.stdout.write(f'  Response: {response.status_code} ({len(response.content)} bytes)')
                    
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'  Error: {e}'))
            
            # Small delay between requests
            if i < count - 1:
                time.sleep(1)
        
        self.stdout.write(self.style.SUCCESS('Test completed! Check the crawl logs API or admin interface.'))
        
        # Show some stats
        from core.crawl_logger import get_crawl_stats
        stats = get_crawl_stats(1)  # Last 1 hour
        
        self.stdout.write('\nCrawl Statistics (last hour):')
        self.stdout.write(f'  Total requests: {stats["total_requests"]}')
        self.stdout.write(f'  Successful requests: {stats["successful_requests"]}')
        self.stdout.write(f'  Failed requests: {stats["failed_requests"]}')
        self.stdout.write(f'  Proxy requests: {stats["proxy_requests"]}')
        self.stdout.write(f'  Direct requests: {stats["direct_requests"]}')
        self.stdout.write(f'  Average response time: {stats["avg_response_time"]:.2f}ms')
        self.stdout.write(f'  Unique proxy IPs: {len(stats["unique_ips"])}')
        
        if stats["crawl_types"]:
            self.stdout.write('  Crawl types:')
            for crawl_type, count in stats["crawl_types"].items():
                self.stdout.write(f'    {crawl_type}: {count}')
        
        self.stdout.write('\nTo view detailed logs, visit:')
        self.stdout.write('  API: http://localhost:8000/api/crawl-logs/')
        self.stdout.write('  Admin: http://localhost:8000/admin/core/crawllog/')
