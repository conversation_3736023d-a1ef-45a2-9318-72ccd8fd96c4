from django.core.management.base import BaseCommand
from core.models import ProxyConfiguration


class Command(BaseCommand):
    help = 'Setup proxy configuration for WHI scraping'

    def add_arguments(self, parser):
        parser.add_argument('--name', type=str, default='Webshare.io', help='Proxy name')
        parser.add_argument('--api-key', type=str, required=True, help='Webshare.io API key')
        parser.add_argument('--enable', action='store_true', help='Enable this proxy')
        # Legacy arguments for backward compatibility
        parser.add_argument('--endpoint', type=str, help='Legacy: Proxy endpoint (host:port)')
        parser.add_argument('--username', type=str, help='Legacy: Proxy username')
        parser.add_argument('--password', type=str, help='Legacy: Proxy password')

    def handle(self, *args, **options):
        name = options['name']
        api_key = options['api_key']
        enable = options.get('enable', False)

        # Legacy support
        endpoint = options.get('endpoint', '')
        username = options.get('username', '')
        password = options.get('password', '')

        # Disable all existing proxies if we're enabling this one
        if enable:
            ProxyConfiguration.objects.all().update(is_enabled=False)

        # Create or update proxy configuration
        proxy, created = ProxyConfiguration.objects.get_or_create(
            name=name,
            defaults={
                'api_key': api_key,
                'endpoint': endpoint,  # Keep for legacy
                'username': username,  # Keep for legacy
                'password': password,  # Keep for legacy
                'is_enabled': enable
            }
        )

        if not created:
            proxy.api_key = api_key
            proxy.endpoint = endpoint
            proxy.username = username
            proxy.password = password
            proxy.is_enabled = enable
            proxy.save()

        action = "Created" if created else "Updated"
        status = "enabled" if enable else "disabled"

        self.stdout.write(
            self.style.SUCCESS(
                f'{action} proxy "{name}" (API key: {api_key[:8]}...) - {status}'
            )
        )
        
        # Show current proxy status
        enabled_proxy = ProxyConfiguration.objects.filter(is_enabled=True).first()
        if enabled_proxy:
            self.stdout.write(f"Active proxy: {enabled_proxy.name} ({enabled_proxy.endpoint})")
        else:
            self.stdout.write(self.style.WARNING("No proxy is currently enabled"))
