from django.db import models
from django.contrib.auth.models import User
import uuid


class ManufacturerCode(models.Model):
    """WHI Manufacturer codes and brand names (Legacy SQLite table)"""
    mfgcode = models.CharField(max_length=10, unique=True, db_index=True)
    brand = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'whi_mfgcodes'
        ordering = ['mfgcode']

    def __str__(self):
        return f"{self.mfgcode} - {self.brand}"


class AcesManufacturer(models.Model):
    """
    ACES Manufacturers table from whi_aces.manufacturers (MySQL)
    """
    id = models.AutoField(primary_key=True)
    mfr_id = models.IntegerField(unique=True, db_column='MfrID')
    mfr_name = models.CharField(max_length=255, db_column='MfrName')

    class Meta:
        db_table = 'manufacturers'
        managed = False  # Don't let Django manage this table
        ordering = ['mfr_name']

    def __str__(self):
        return f"{self.mfr_name} (ID: {self.mfr_id})"

    @property
    def brand(self):
        """Alias for compatibility with existing code"""
        return self.mfr_name

    @property
    def mfgcode(self):
        """Use MfrID as the manufacturer code"""
        return str(self.mfr_id)


class UploadedFile(models.Model):
    """Track uploaded Excel files"""
    STATUS_CHOICES = [
        ('uploaded', 'Uploaded'),
        ('processing', 'Processing'),
        ('processed', 'Processed'),
        ('error', 'Error'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    filename = models.CharField(max_length=255)
    file_path = models.FileField(upload_to='uploads/')
    file_size = models.PositiveIntegerField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='uploaded')
    selected_mfgcode = models.ForeignKey(ManufacturerCode, on_delete=models.SET_NULL, null=True, blank=True)
    total_parts = models.PositiveIntegerField(default=0)
    processed_parts = models.PositiveIntegerField(default=0)
    error_message = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'uploaded_files'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.filename} ({self.status})"


class PartNumber(models.Model):
    """Store part numbers from uploaded files"""
    uploaded_file = models.ForeignKey(UploadedFile, on_delete=models.CASCADE, related_name='part_numbers')
    part_number = models.CharField(max_length=100, db_index=True)
    manufacturer = models.CharField(max_length=100, blank=True)
    description = models.TextField(blank=True)
    row_data = models.JSONField(default=dict)  # Store additional Excel row data
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'part_numbers'
        unique_together = ['uploaded_file', 'part_number']
        indexes = [
            models.Index(fields=['part_number']),
            models.Index(fields=['uploaded_file', 'part_number']),
        ]
    
    def __str__(self):
        return f"{self.part_number} ({self.manufacturer})"


class InterchangeData(models.Model):
    """Store interchange part numbers from WHI smartpages"""
    # Link to our product data
    product_data = models.ForeignKey('ProductData', on_delete=models.CASCADE, related_name='interchanges')

    # Original part number from the smartpage URL (e.g., TKH-002)
    part_number = models.CharField(max_length=100, db_index=True)

    # Original part type ID from the smartpage URL (e.g., 16088)
    part_type_id = models.CharField(max_length=50, db_index=True)

    # Interchange type: AM (Aftermarket) or OE (Original Equipment)
    interchange_type = models.CharField(max_length=2, choices=[('AM', 'Aftermarket'), ('OE', 'Original Equipment')])

    # WHI data for the original part
    whi_mfgcode = models.CharField(max_length=10, db_index=True)
    whi_brand_name = models.CharField(max_length=255)

    # Interchange part data
    interchange_brand_name = models.CharField(max_length=255)
    interchange_part_number = models.CharField(max_length=100, db_index=True)
    interchange_mfgcode = models.CharField(max_length=10, blank=True, null=True, db_index=True)

    # Source information
    smartpage_url = models.URLField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'interchange_data'
        unique_together = ['part_number', 'part_type_id', 'whi_mfgcode', 'interchange_type', 'interchange_part_number', 'interchange_brand_name']
        indexes = [
            models.Index(fields=['part_number']),
            models.Index(fields=['part_type_id']),
            models.Index(fields=['part_number', 'part_type_id', 'whi_mfgcode']),
            models.Index(fields=['interchange_part_number']),
            models.Index(fields=['whi_mfgcode']),
            models.Index(fields=['interchange_mfgcode']),
            models.Index(fields=['interchange_type']),
        ]

    def __str__(self):
        return f"{self.product_data.PartNumber} -> {self.interchange_part_number} ({self.interchange_type})"


class Retailer(models.Model):
    """Supported retailers for crawling"""
    name = models.CharField(max_length=100, unique=True)
    website = models.URLField()
    base_search_url = models.URLField()
    is_active = models.BooleanField(default=True)
    crawl_delay = models.PositiveIntegerField(default=1)  # seconds between requests
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'retailers'
        ordering = ['name']
    
    def __str__(self):
        return self.name


class CrawlJob(models.Model):
    """Track crawling operations"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    uploaded_file = models.ForeignKey(UploadedFile, on_delete=models.CASCADE, related_name='crawl_jobs')
    retailers = models.ManyToManyField(Retailer)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    total_parts = models.PositiveIntegerField(default=0)
    processed_parts = models.PositiveIntegerField(default=0)
    successful_crawls = models.PositiveIntegerField(default=0)
    failed_crawls = models.PositiveIntegerField(default=0)
    use_proxy = models.BooleanField(default=False)
    error_message = models.TextField(blank=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'crawl_jobs'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Crawl Job {self.id} ({self.status})"
    
    @property
    def progress_percentage(self):
        if self.total_parts == 0:
            return 0
        return round((self.processed_parts / self.total_parts) * 100, 1)


class PricingData(models.Model):
    """Store scraped pricing information"""
    crawl_job = models.ForeignKey(CrawlJob, on_delete=models.CASCADE, related_name='pricing_data')
    part_number = models.ForeignKey(PartNumber, on_delete=models.CASCADE, related_name='pricing_data')
    retailer = models.ForeignKey(Retailer, on_delete=models.CASCADE)
    searched_part = models.CharField(max_length=100)  # The actual part number searched (original or interchange)
    found_part = models.CharField(max_length=100, blank=True)  # The part number found on retailer site
    price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    availability = models.CharField(max_length=100, blank=True)
    product_url = models.URLField(blank=True)
    product_name = models.CharField(max_length=255, blank=True)
    brand = models.CharField(max_length=100, blank=True)
    additional_data = models.JSONField(default=dict)  # Store any additional scraped data
    crawled_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'pricing_data'
        unique_together = ['crawl_job', 'part_number', 'retailer', 'searched_part']
        indexes = [
            models.Index(fields=['part_number', 'retailer']),
            models.Index(fields=['crawl_job']),
            models.Index(fields=['price']),
        ]
    
    def __str__(self):
        return f"{self.part_number.part_number} @ {self.retailer.name}: ${self.price}"


class ProxyConfiguration(models.Model):
    """Store proxy configuration settings"""
    name = models.CharField(max_length=100, unique=True)
    is_enabled = models.BooleanField(default=False)
    proxy_type = models.CharField(max_length=20, default='http')
    endpoint = models.CharField(max_length=255, blank=True)  # Optional for API-based proxies
    username = models.CharField(max_length=100, blank=True)  # Legacy field
    password = models.CharField(max_length=100, blank=True)  # Legacy field
    api_key = models.CharField(max_length=255, blank=True)  # For webshare.io API
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'proxy_configurations'
    
    def __str__(self):
        return f"{self.name} ({'Enabled' if self.is_enabled else 'Disabled'})"


class ImportedDataTemp(models.Model):
    """Temporary table for imported data before validation and mapping"""
    uploaded_file = models.ForeignKey(UploadedFile, on_delete=models.CASCADE)
    row_number = models.IntegerField()

    # Raw data fields - store as JSON to handle any column structure
    raw_data = models.JSONField()

    # Validation status
    is_valid = models.BooleanField(default=False)
    validation_errors = models.JSONField(default=list, blank=True)

    # Mapping status
    is_mapped = models.BooleanField(default=False)
    mapped_data = models.JSONField(default=dict, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['uploaded_file', 'row_number']
        ordering = ['row_number']

    def __str__(self):
        return f"Row {self.row_number} from {self.uploaded_file.filename}"


class ProductData(models.Model):
    """Permanent table for imported product data matching template structure"""
    uploaded_file = models.ForeignKey(UploadedFile, on_delete=models.CASCADE, related_name='product_data')

    # Template fields matching your exact structure
    AAIABrandID = models.CharField(max_length=50, blank=True, null=True)
    CompanyName = models.CharField(max_length=255, blank=True, null=True)
    AAIABrandLabel = models.CharField(max_length=255, blank=True, null=True)
    LineCode = models.CharField(max_length=50, blank=True, null=True)
    LineName = models.CharField(max_length=255, blank=True, null=True)
    PartNumber = models.CharField(max_length=100, db_index=True)  # Required
    EpicorStdMfgPartNumber = models.CharField(max_length=100, blank=True, null=True)
    PartTypeID = models.CharField(max_length=50, db_index=True)  # Required
    PartType = models.CharField(max_length=100, blank=True, null=True)
    LifeCycleCode = models.CharField(max_length=50, blank=True, null=True)
    NetworkItemDemand = models.CharField(max_length=100, blank=True, null=True)
    ItemAffiliateEffectiveDate = models.DateField(blank=True, null=True)
    cost = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)

    # WHI manufacturer code from step 1
    whi_mfgcode = models.CharField(max_length=10, blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'product_data'
        unique_together = ['PartNumber', 'PartTypeID', 'AAIABrandID', 'LineCode']
        indexes = [
            models.Index(fields=['PartNumber']),
            models.Index(fields=['PartTypeID']),
            models.Index(fields=['AAIABrandID']),
            models.Index(fields=['LineCode']),
            models.Index(fields=['PartNumber', 'PartTypeID', 'AAIABrandID', 'LineCode']),
        ]

    def __str__(self):
        return f"{self.PartNumber} ({self.AAIABrandLabel or self.CompanyName})"

    @classmethod
    def get_products_needing_interchange(cls, uploaded_file, whi_mfgcode):
        """
        Get products that still need interchange lookup.
        Returns products where no real interchange data exists (excluding debug records).
        """
        from django.db.models import Exists, OuterRef

        # Get all products for this upload
        all_products = cls.objects.filter(uploaded_file=uploaded_file)

        # Create subquery to check for real interchange data
        real_interchanges_subquery = InterchangeData.objects.filter(
            product_data=OuterRef('pk'),
            part_number=OuterRef('PartNumber'),
            part_type_id=OuterRef('PartTypeID'),
            whi_mfgcode=whi_mfgcode
        ).exclude(
            interchange_brand_name='DEBUG_URL'
        )

        # Return products that DON'T have real interchange data
        return all_products.exclude(
            Exists(real_interchanges_subquery)
        ).order_by('id')


class DataMappingConfig(models.Model):
    """Store column mapping configuration for imported files"""
    uploaded_file = models.OneToOneField(UploadedFile, on_delete=models.CASCADE)

    # Source columns from imported file
    source_columns = models.JSONField()

    # Mapping configuration: source_column -> target_field
    column_mappings = models.JSONField(default=dict)

    # Target table structure
    target_fields = models.JSONField(default=dict)

    # Validation rules
    validation_rules = models.JSONField(default=dict)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Mapping config for {self.uploaded_file.filename}"
