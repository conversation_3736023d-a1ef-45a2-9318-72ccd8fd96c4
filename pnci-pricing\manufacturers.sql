/*
 Navicat Premium Data Transfer

 Source Server         : pnci
 Source Server Type    : MySQL
 Source Server Version : 100618 (10.6.18-MariaDB-ubu2004-log)
 Source Host           : mysql.sophio.com:3306
 Source Schema         : whi_aces

 Target Server Type    : MySQL
 Target Server Version : 100618 (10.6.18-MariaDB-ubu2004-log)
 File Encoding         : 65001

 Date: 09/06/2025 19:04:16
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for manufacturers
-- ----------------------------
DROP TABLE IF EXISTS `manufacturers`;
CREATE TABLE `manufacturers`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `mfg_code` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `mfg_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  `mfg_name_slug` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `mfg_code`(`mfg_code` ASC) USING BTREE,
  INDEX `mfg_name`(`mfg_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2646 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of manufacturers
-- ----------------------------
INSERT INTO `manufacturers` VALUES (1, 'A1C', 'CARDONE REMAN', 'cardone-reman');
INSERT INTO `manufacturers` VALUES (2, 'A1S', 'CARDONE NEW', 'cardone-new');
INSERT INTO `manufacturers` VALUES (3, 'AAE', 'ATLANTIC AUTOMOTIVE ENTERPRISES', 'atlantic-automotive-enterprises');
INSERT INTO `manufacturers` VALUES (4, 'ABF', 'ABS PRIVATE BRAND', 'abs-private-brand');
INSERT INTO `manufacturers` VALUES (5, 'ABI', 'R&Y AC COMPRESSORS (DELETE V23A08R1)', 'r-y-ac-compressors-delete-v23a08r1');
INSERT INTO `manufacturers` VALUES (6, 'ABS', 'ABSCO', 'absco');
INSERT INTO `manufacturers` VALUES (7, 'ACC', 'ACCEL', 'accel');
INSERT INTO `manufacturers` VALUES (8, 'ACI', 'ACI/MAXAIR', 'aci-maxair');
INSERT INTO `manufacturers` VALUES (9, 'ACV', 'ACCU-DRIVE', 'accu-drive');
INSERT INTO `manufacturers` VALUES (10, 'AD8', 'GSP NORTH AMERICA INC.', 'gsp-north-america-inc');
INSERT INTO `manufacturers` VALUES (11, 'ADU', 'ACDELCO GOLD/PROFESSIONAL BRAKES', 'acdelco-gold-professional-brakes');
INSERT INTO `manufacturers` VALUES (12, 'AEM', 'AIRTEX ENG MGMT SYSTEMS(DELETE V24A01R1)', 'airtex-eng-mgmt-systems-delete-v24a01r1');
INSERT INTO `manufacturers` VALUES (13, 'AEP', 'ATE/PREMIUMONE', 'ate-premiumone');
INSERT INTO `manufacturers` VALUES (14, 'AGB', 'AGNA BRAKES', 'agna-brakes');
INSERT INTO `manufacturers` VALUES (15, 'AGR', 'ARMATURE G ROY INC.', 'armature-g-roy-inc');
INSERT INTO `manufacturers` VALUES (16, 'AIJ', 'AUS INJECTION INC.', 'aus-injection-inc');
INSERT INTO `manufacturers` VALUES (17, 'AIM', 'AIMCO', 'aimco');
INSERT INTO `manufacturers` VALUES (18, 'AIN', 'ASC INDUSTRIES', 'asc-industries');
INSERT INTO `manufacturers` VALUES (19, 'AIS', 'AISIN WORLD CORP. OF AMERICA', 'aisin-world-corp-of-america');
INSERT INTO `manufacturers` VALUES (20, 'AKB', 'AKEBONO', 'akebono');
INSERT INTO `manufacturers` VALUES (21, 'AKM', 'AUTO-KOOL', 'auto-kool');
INSERT INTO `manufacturers` VALUES (22, 'ALB', 'ALCO BRAKES', 'alco-brakes');
INSERT INTO `manufacturers` VALUES (23, 'AMN', 'K SOURCE', 'k-source');
INSERT INTO `manufacturers` VALUES (24, 'AMS', 'AMS AUTOMOTIVE', 'ams-automotive');
INSERT INTO `manufacturers` VALUES (25, 'ANC', 'ANCO WIPER PRODUCTS', 'anco-wiper-products');
INSERT INTO `manufacturers` VALUES (26, 'ANE', 'ANSA/SILVERLINE PRODUCTS', 'ansa-silverline-products');
INSERT INTO `manufacturers` VALUES (27, 'ANH', 'ANCHOR', 'anchor');
INSERT INTO `manufacturers` VALUES (28, 'APN', 'AUTOPART INTERNATIONAL', 'autopart-international');
INSERT INTO `manufacturers` VALUES (29, 'APS', 'AUTOPARTSOURCE', 'autopartsource');
INSERT INTO `manufacturers` VALUES (30, 'APW', 'APW INTERNATIONAL INC.', 'apw-international-inc');
INSERT INTO `manufacturers` VALUES (31, 'AQL', 'AIRQUALITEE', 'airqualitee');
INSERT INTO `manufacturers` VALUES (32, 'ARC', 'ARC REMANUFACTURING INC.', 'arc-remanufacturing-inc');
INSERT INTO `manufacturers` VALUES (33, 'ASB', 'PARTS PLUS/EXIDE', 'parts-plus-exide');
INSERT INTO `manufacturers` VALUES (34, 'ATK', 'ATK', 'atk');
INSERT INTO `manufacturers` VALUES (35, 'ATL', 'AUTOLITE', 'autolite');
INSERT INTO `manufacturers` VALUES (36, 'ATN', 'AIRTEX AUTOMOTIVE DIVISION', 'airtex-automotive-division');
INSERT INTO `manufacturers` VALUES (37, 'ATP', 'ATP', 'atp');
INSERT INTO `manufacturers` VALUES (38, 'ATS', 'ATSCO REMANUFACTURING, INC.', 'atsco-remanufacturing-inc');
INSERT INTO `manufacturers` VALUES (39, 'ATW', 'ADVAN-TECH', 'advan-tech');
INSERT INTO `manufacturers` VALUES (40, 'AUN', 'AUTOLINE PRODUCTS LTD', 'autoline-products-ltd');
INSERT INTO `manufacturers` VALUES (41, 'AVC', 'ADVICS', 'advics');
INSERT INTO `manufacturers` VALUES (42, 'AXD', 'UNI-SELECT/PRO SERIES WIRE SETS', 'uni-select-pro-series-wire-sets');
INSERT INTO `manufacturers` VALUES (43, 'AXE', 'AUTO EXTRA FILTERS/CANADA', 'auto-extra-filters-canada');
INSERT INTO `manufacturers` VALUES (44, 'AXF', 'AUTO EXTRA FRICTION US', 'auto-extra-friction-us');
INSERT INTO `manufacturers` VALUES (45, 'AXG', 'AUTO EXTRA DRUMS-ROTORS/NEW SEQ', 'auto-extra-drums-rotors-new-seq');
INSERT INTO `manufacturers` VALUES (46, 'AXM', 'AUTO EXTRA/MEVOTECH US', 'auto-extra-mevotech-us');
INSERT INTO `manufacturers` VALUES (47, 'AXU', 'AUTO EXTRA OIL-AIR FILTERS/US', 'auto-extra-oil-air-filters-us');
INSERT INTO `manufacturers` VALUES (48, 'BAR', 'BECK/ARNLEY', 'beck-arnley');
INSERT INTO `manufacturers` VALUES (49, 'BAY', 'EMPIRE', 'empire');
INSERT INTO `manufacturers` VALUES (50, 'BBA', 'BBB INDUSTRIES', 'bbb-industries');
INSERT INTO `manufacturers` VALUES (51, 'BBM', 'B&B MANUFACTURING', 'b-b-manufacturing');
INSERT INTO `manufacturers` VALUES (52, 'BCA', 'NATIONAL SEAL/BEARING', 'national-seal-bearing');
INSERT INTO `manufacturers` VALUES (53, 'BCB', 'NATIONAL BEARING', 'national-bearing');
INSERT INTO `manufacturers` VALUES (54, 'BDW', 'BALDWIN', 'baldwin');
INSERT INTO `manufacturers` VALUES (55, 'BEB', 'BETTER BRAKE PARTS', 'better-brake-parts');
INSERT INTO `manufacturers` VALUES (56, 'BEH', 'BEHR THERMOT-TRONIK THERMOSTATS', 'behr-thermot-tronik-thermostats');
INSERT INTO `manufacturers` VALUES (57, 'BEN', 'BENDIX', 'bendix');
INSERT INTO `manufacturers` VALUES (58, 'BHS', 'BEHR HELLA SERVICE', 'behr-hella-service');
INSERT INTO `manufacturers` VALUES (59, 'BIL', 'BILSTEIN', 'bilstein');
INSERT INTO `manufacturers` VALUES (60, 'BKW', 'FMI WATER PUMPS', 'fmi-water-pumps');
INSERT INTO `manufacturers` VALUES (61, 'BMO', 'BREMBO NORTH AMERICA', 'brembo-north-america');
INSERT INTO `manufacturers` VALUES (62, 'BOR', 'BWD AUTOMOTIVE', 'bwd-automotive');
INSERT INTO `manufacturers` VALUES (63, 'BOS', 'BOSCH', 'bosch');
INSERT INTO `manufacturers` VALUES (64, 'BPS', 'BWD P SERIES', 'bwd-p-series');
INSERT INTO `manufacturers` VALUES (65, 'BRU', 'BRUTE POWER', 'brute-power');
INSERT INTO `manufacturers` VALUES (66, 'BSL', 'BREXHAUST EXHAUST', 'brexhaust-exhaust');
INSERT INTO `manufacturers` VALUES (67, 'BTC', 'BOSTECH (OLD)', 'bostech-old');
INSERT INTO `manufacturers` VALUES (68, 'BTD', 'CANADIAN ENERGY', 'canadian-energy');
INSERT INTO `manufacturers` VALUES (69, 'BUS', 'BUSSMANN BY EATON', 'bussmann-by-eaton');
INSERT INTO `manufacturers` VALUES (70, 'BWO', 'BANDO', 'bando');
INSERT INTO `manufacturers` VALUES (71, 'BXG', 'BENDIX GLOBAL', 'bendix-global');
INSERT INTO `manufacturers` VALUES (72, 'C86', 'COOLING DEPOT CANADA', 'cooling-depot-canada');
INSERT INTO `manufacturers` VALUES (73, 'CAD', 'CADNA ARMORMARK', 'cadna-armormark');
INSERT INTO `manufacturers` VALUES (74, 'CAJ', 'CROWN AUTOMOTIVE SALES CO.', 'crown-automotive-sales-co');
INSERT INTO `manufacturers` VALUES (75, 'CAR', 'GENERAL CABLE', 'general-cable');
INSERT INTO `manufacturers` VALUES (76, 'CBP', 'CHERRY BOMB PERFORMANCE', 'cherry-bomb-performance');
INSERT INTO `manufacturers` VALUES (77, 'CBT', 'CONTINENTAL BATTERIES', 'continental-batteries');
INSERT INTO `manufacturers` VALUES (78, 'CDP', 'CADNA PRESTONE', 'cadna-prestone');
INSERT INTO `manufacturers` VALUES (79, 'CEC', 'CENTRIC PARTS', 'centric-parts');
INSERT INTO `manufacturers` VALUES (80, 'CER', 'CERTIFIED BY CONI-SEAL', 'certified-by-coni-seal');
INSERT INTO `manufacturers` VALUES (81, 'CEU', 'CLEVITE ENGINE ALL SIZES', 'clevite-engine-all-sizes');
INSERT INTO `manufacturers` VALUES (82, 'CHA', 'CHAMPION SPARK PLUGS', 'champion-spark-plugs');
INSERT INTO `manufacturers` VALUES (83, 'CHI', 'CHILTON BOOK COMPANY', 'chilton-book-company');
INSERT INTO `manufacturers` VALUES (84, 'CHP', 'CHAMPION LABORATORIES INC.', 'champion-laboratories-inc');
INSERT INTO `manufacturers` VALUES (85, 'CID', 'CSD INTERNATIONAL INC.', 'csd-international-inc');
INSERT INTO `manufacturers` VALUES (86, 'CIT', 'CASITE', 'casite');
INSERT INTO `manufacturers` VALUES (87, 'CLE', 'CLEVITE ENGINE STANDARD SIZES', 'clevite-engine-standard-sizes');
INSERT INTO `manufacturers` VALUES (88, 'CLO', 'CLOYES', 'cloyes');
INSERT INTO `manufacturers` VALUES (89, 'CMB', 'CAMBRO PRODUCTS INC.', 'cambro-products-inc');
INSERT INTO `manufacturers` VALUES (90, 'CMN', 'CHAMPION PREM REMAN ALTS/STARTERS', 'champion-prem-reman-alts-starters');
INSERT INTO `manufacturers` VALUES (91, 'CMP', 'CHAMP SERVICE LINE', 'champ-service-line');
INSERT INTO `manufacturers` VALUES (92, 'CNA', 'CONTINENTAL', 'continental');
INSERT INTO `manufacturers` VALUES (93, 'CNS', 'CONI-SEAL', 'coni-seal');
INSERT INTO `manufacturers` VALUES (94, 'CPL', 'CLEAR PLUS WINDSHIELD PRODUCTS', 'clear-plus-windshield-products');
INSERT INTO `manufacturers` VALUES (95, 'CPW', 'COMPRESSOR WORKS', 'compressor-works');
INSERT INTO `manufacturers` VALUES (96, 'CRI', 'CRANKSHAFT REBUILDERS', 'crankshaft-rebuilders');
INSERT INTO `manufacturers` VALUES (97, 'CRL', 'CARLSON QUALITY BRAKE PARTS', 'carlson-quality-brake-parts');
INSERT INTO `manufacturers` VALUES (98, 'CRM', 'CHASSIS RITE BY MEVOTECH', 'chassis-rite-by-mevotech');
INSERT INTO `manufacturers` VALUES (99, 'CRS', 'CRS', 'crs');
INSERT INTO `manufacturers` VALUES (100, 'CSF', 'CSF RADIATOR', 'csf-radiator');
INSERT INTO `manufacturers` VALUES (101, 'CSL', 'CHASSIS SELECT', 'chassis-select');
INSERT INTO `manufacturers` VALUES (102, 'CSN', 'CST, INC.', 'cst-inc');
INSERT INTO `manufacturers` VALUES (103, 'CTK', 'C-TEK BY CENTRIC', 'c-tek-by-centric');
INSERT INTO `manufacturers` VALUES (104, 'CTO', 'CATCO CONVERTERS/49 STATE ONLY', 'catco-converters-49-state-only');
INSERT INTO `manufacturers` VALUES (105, 'CTR', 'CARTER', 'carter');
INSERT INTO `manufacturers` VALUES (106, 'CUR', 'CURT MFG INC.', 'curt-mfg-inc');
INSERT INTO `manufacturers` VALUES (107, 'CVU', 'BOSTECH', 'bostech');
INSERT INTO `manufacturers` VALUES (108, 'CWS', 'CHAMPION WIRE SETS (CANADA)', 'champion-wire-sets-canada');
INSERT INTO `manufacturers` VALUES (109, 'D48', 'DURAGO', 'durago');
INSERT INTO `manufacturers` VALUES (110, 'DAK', 'EXEDY', 'exedy');
INSERT INTO `manufacturers` VALUES (111, 'DAP', 'DAYCO PRIVATE LABEL', 'dayco-private-label');
INSERT INTO `manufacturers` VALUES (112, 'DAS', 'DASHMAT', 'dashmat');
INSERT INTO `manufacturers` VALUES (113, 'DAY', 'DAYCO PRODUCTS LLC', 'dayco-products-llc');
INSERT INTO `manufacturers` VALUES (114, 'DBP', 'DORMAN - FIRST STOP', 'dorman-first-stop');
INSERT INTO `manufacturers` VALUES (115, 'DCT', 'DORMAN - CONDUCT-TITE', 'dorman-conduct-tite');
INSERT INTO `manufacturers` VALUES (116, 'DEA', 'DEA PRODUCTS', 'dea-products');
INSERT INTO `manufacturers` VALUES (117, 'DEF', 'DEFLECTA-SHIELD ACCESO (DELETE V23A09R1)', 'deflecta-shield-acceso-delete-v23a09r1');
INSERT INTO `manufacturers` VALUES (118, 'DEK', 'DEKA', 'deka');
INSERT INTO `manufacturers` VALUES (119, 'DFB', 'DASH 4 BRAKES', 'dash-4-brakes');
INSERT INTO `manufacturers` VALUES (120, 'DFN', 'DEFENSE FILTERS (FRAM)', 'defense-filters-fram');
INSERT INTO `manufacturers` VALUES (121, 'DMA', 'DIRECT MARKET ACCESS', 'direct-market-access');
INSERT INTO `manufacturers` VALUES (122, 'DMX', 'DYNOMAX', 'dynomax');
INSERT INTO `manufacturers` VALUES (123, 'DNS', 'DNS ARMATURES', 'dns-armatures');
INSERT INTO `manufacturers` VALUES (124, 'DOC', 'DORMAN - AUTOGRADE', 'dorman-autograde');
INSERT INTO `manufacturers` VALUES (125, 'DPH', 'DELPHI', 'delphi');
INSERT INTO `manufacturers` VALUES (126, 'DRE', 'DORMAN OE SOLUTIONS', 'dorman-oe-solutions');
INSERT INTO `manufacturers` VALUES (127, 'DSS', 'DIVERSIFIED SHAFTS SOLUTIONS', 'diversified-shafts-solutions');
INSERT INTO `manufacturers` VALUES (128, 'DTA', 'DRIVE TECH AMERICA', 'drive-tech-america');
INSERT INTO `manufacturers` VALUES (129, 'DTC', 'DORMAN - TECHOICE', 'dorman-techoice');
INSERT INTO `manufacturers` VALUES (130, 'DVM', 'DAVICO MFG EPA', 'davico-mfg-epa');
INSERT INTO `manufacturers` VALUES (131, 'DVT', 'DRIVERITE', 'driverite');
INSERT INTO `manufacturers` VALUES (132, 'DXE', 'DIXIE ELECTRIC', 'dixie-electric');
INSERT INTO `manufacturers` VALUES (133, 'DYM', 'DAYCO IMPORTS', 'dayco-imports');
INSERT INTO `manufacturers` VALUES (134, 'DZA', 'DEEZA', 'deeza');
INSERT INTO `manufacturers` VALUES (135, 'E29', 'EIKO LTD', 'eiko-ltd');
INSERT INTO `manufacturers` VALUES (136, 'E3S', 'E3 SPARK PLUGS', 'e3-spark-plugs');
INSERT INTO `manufacturers` VALUES (137, 'EAS', 'EASTERN', 'eastern');
INSERT INTO `manufacturers` VALUES (138, 'EDB', 'EDELBROCK', 'edelbrock');
INSERT INTO `manufacturers` VALUES (139, 'EDE', 'EDELMANN', 'edelmann');
INSERT INTO `manufacturers` VALUES (140, 'EMI', 'EASTERN CATALYTIC EPA CONVERTER', 'eastern-catalytic-epa-converter');
INSERT INTO `manufacturers` VALUES (141, 'EPF', 'FISHER', 'fisher');
INSERT INTO `manufacturers` VALUES (142, 'EXE', 'EXCEL', 'excel');
INSERT INTO `manufacturers` VALUES (143, 'FAA', 'FACTORY AIR', 'factory-air');
INSERT INTO `manufacturers` VALUES (144, 'FDA', 'FEDERATED DRUMS & ROTORS', 'federated-drums-rotors');
INSERT INTO `manufacturers` VALUES (145, 'FDY', 'FRAM DISPLAY PACK', 'fram-display-pack');
INSERT INTO `manufacturers` VALUES (146, 'FEB', 'FEDERATED BRAKES', 'federated-brakes');
INSERT INTO `manufacturers` VALUES (147, 'FEF', 'FEDERATED FILTERS', 'federated-filters');
INSERT INTO `manufacturers` VALUES (148, 'FEL', 'FELPRO', 'felpro');
INSERT INTO `manufacturers` VALUES (149, 'FEN', 'FENWICK AUTOMOTIVE PRODUCTS (FENCO)', 'fenwick-automotive-products-fenco');
INSERT INTO `manufacturers` VALUES (150, 'FGU', 'FAG CANADA', 'fag-canada');
INSERT INTO `manufacturers` VALUES (151, 'FHM', 'FRAM HIGH MILEAGE', 'fram-high-mileage');
INSERT INTO `manufacturers` VALUES (152, 'FHP', 'FELPRO HIGH PERF.', 'felpro-high-perf');
INSERT INTO `manufacturers` VALUES (153, 'FIG', 'FEDERATED IGNITION', 'federated-ignition');
INSERT INTO `manufacturers` VALUES (154, 'FJC', 'FJC, INC.', 'fjc-inc');
INSERT INTO `manufacturers` VALUES (155, 'FLE', 'FLEX A LITE', 'flex-a-lite');
INSERT INTO `manufacturers` VALUES (156, 'FLS', 'FALCON STEERING SYSTEMS', 'falcon-steering-systems');
INSERT INTO `manufacturers` VALUES (157, 'FLT', 'FLOWTECH PERFORMANCE EXHAUST', 'flowtech-performance-exhaust');
INSERT INTO `manufacturers` VALUES (158, 'FPC', 'FEDERAL PARTS CORP.', 'federal-parts-corp');
INSERT INTO `manufacturers` VALUES (159, 'FPI', 'WINHERE BRAKE PARTS INC.', 'winhere-brake-parts-inc');
INSERT INTO `manufacturers` VALUES (160, 'FPS', 'FEDERATED POWER STEERING', 'federated-power-steering');
INSERT INTO `manufacturers` VALUES (161, 'FRA', 'FRAM', 'fram');
INSERT INTO `manufacturers` VALUES (162, 'FRM', 'FRICTION MASTER', 'friction-master');
INSERT INTO `manufacturers` VALUES (163, 'FRU', 'MICRONAIR', 'micronair');
INSERT INTO `manufacturers` VALUES (164, 'FRX', 'FREMAX BRAKE ROTORS & DRUMS', 'fremax-brake-rotors-drums');
INSERT INTO `manufacturers` VALUES (165, 'FSD', 'FERODO', 'ferodo');
INSERT INTO `manufacturers` VALUES (166, 'FSE', 'FOUR SEASONS', 'four-seasons');
INSERT INTO `manufacturers` VALUES (167, 'FTG', 'FRAM TOUGH GUARD FILTERS', 'fram-tough-guard-filters');
INSERT INTO `manufacturers` VALUES (168, 'FTR', 'FIRESTONE', 'firestone');
INSERT INTO `manufacturers` VALUES (169, 'FVP', 'FVP', 'fvp');
INSERT INTO `manufacturers` VALUES (170, 'FWC', 'FEDERATED WIRE AND CABLE', 'federated-wire-and-cable');
INSERT INTO `manufacturers` VALUES (171, 'FXG', 'FRAM EXTENDED GUARD FILTERS', 'fram-extended-guard-filters');
INSERT INTO `manufacturers` VALUES (172, 'GAB', 'GABRIEL', 'gabriel');
INSERT INTO `manufacturers` VALUES (173, 'GAT', 'GATES', 'gates');
INSERT INTO `manufacturers` VALUES (174, 'GBP', 'GLOBAL PARTS', 'global-parts');
INSERT INTO `manufacturers` VALUES (175, 'GBR', 'GB REMANUFACTURING INC.', 'gb-remanufacturing-inc');
INSERT INTO `manufacturers` VALUES (176, 'GCN', 'GATES CANADA', 'gates-canada');
INSERT INTO `manufacturers` VALUES (177, 'GEL', 'GE LIGHTING', 'ge-lighting');
INSERT INTO `manufacturers` VALUES (178, 'GKI', 'GK INDUSTRIES', 'gk-industries');
INSERT INTO `manufacturers` VALUES (179, 'GMB', 'GMB', 'gmb');
INSERT INTO `manufacturers` VALUES (180, 'GNO', 'PROLIANCE READY-RAD', 'proliance-ready-rad');
INSERT INTO `manufacturers` VALUES (181, 'GOO', 'CONTINENTAL', 'continental');
INSERT INTO `manufacturers` VALUES (182, 'GP7', 'GROUP 7', 'group-7');
INSERT INTO `manufacturers` VALUES (183, 'GPL', 'FRESHSTART', 'freshstart');
INSERT INTO `manufacturers` VALUES (184, 'GPS', 'GP SORENSEN', 'gp-sorensen');
INSERT INTO `manufacturers` VALUES (185, 'HAD', 'HAYDEN', 'hayden');
INSERT INTO `manufacturers` VALUES (186, 'HAN', 'HAYNES', 'haynes');
INSERT INTO `manufacturers` VALUES (187, 'HAS', 'HASTINGS FILTERS', 'hastings-filters');
INSERT INTO `manufacturers` VALUES (188, 'HAV', 'HAVOLINE', 'havoline');
INSERT INTO `manufacturers` VALUES (189, 'HAY', 'HAYS', 'hays');
INSERT INTO `manufacturers` VALUES (190, 'HIT', 'CHAMPION NEW ALTERNATORS/STARTERS', 'champion-new-alternators-starters');
INSERT INTO `manufacturers` VALUES (191, 'HLA', 'HELLA', 'hella');
INSERT INTO `manufacturers` VALUES (192, 'HUR', 'HURST', 'hurst');
INSERT INTO `manufacturers` VALUES (193, 'HYA', 'HYTEC AUTOMOTIVE', 'hytec-automotive');
INSERT INTO `manufacturers` VALUES (194, 'IDB', 'IDEAL BRAKE PARTS', 'ideal-brake-parts');
INSERT INTO `manufacturers` VALUES (195, 'IGS', 'INGALLS ENGINEERING CO. INC.', 'ingalls-engineering-co-inc');
INSERT INTO `manufacturers` VALUES (196, 'IMO', 'INTERAMERICAN MOTOR CORPORATION', 'interamerican-motor-corporation');
INSERT INTO `manufacturers` VALUES (197, 'INT', 'INTERSTATE', 'interstate');
INSERT INTO `manufacturers` VALUES (198, 'IPP', 'INSTALLER PREFERRED AUTO PRODUCTS', 'installer-preferred-auto-products');
INSERT INTO `manufacturers` VALUES (199, 'ITM', 'ITM', 'itm');
INSERT INTO `manufacturers` VALUES (200, 'KIK', 'KING KALIPERS', 'king-kalipers');
INSERT INTO `manufacturers` VALUES (201, 'KLY', 'KARLYN/STI', 'karlyn-sti');
INSERT INTO `manufacturers` VALUES (202, 'KNN', 'K&N FILTER', 'k-n-filter');
INSERT INTO `manufacturers` VALUES (203, 'KOM', 'KING-O-MATIC', 'king-o-matic');
INSERT INTO `manufacturers` VALUES (204, 'KRI', 'KANG RICH INTERNATIONAL', 'kang-rich-international');
INSERT INTO `manufacturers` VALUES (205, 'KYB', 'KYB', 'kyb');
INSERT INTO `manufacturers` VALUES (206, 'L36', 'LUBER-FINER', 'luber-finer');
INSERT INTO `manufacturers` VALUES (207, 'LAK', 'LAKEWOOD', 'lakewood');
INSERT INTO `manufacturers` VALUES (208, 'LEB', 'LE BRA', 'le-bra');
INSERT INTO `manufacturers` VALUES (209, 'LIT', 'LITTELFUSE', 'littelfuse');
INSERT INTO `manufacturers` VALUES (210, 'LOC', 'LOCKSMART', 'locksmart');
INSERT INTO `manufacturers` VALUES (211, 'LUK', 'LUK AUTOMOTIVE SYSTEMS', 'luk-automotive-systems');
INSERT INTO `manufacturers` VALUES (212, 'LUN', 'LUND', 'lund');
INSERT INTO `manufacturers` VALUES (213, 'LYN', 'LYNX', 'lynx');
INSERT INTO `manufacturers` VALUES (214, 'M38', 'MICROGARD', 'microgard');
INSERT INTO `manufacturers` VALUES (215, 'M43', 'MIGHTY LIFT', 'mighty-lift');
INSERT INTO `manufacturers` VALUES (216, 'M45', 'MILEGUARD', 'mileguard');
INSERT INTO `manufacturers` VALUES (217, 'MAL', 'MALLORY', 'mallory');
INSERT INTO `manufacturers` VALUES (218, 'MBI', 'MOBIL', 'mobil');
INSERT INTO `manufacturers` VALUES (219, 'MBO', 'MOBIL 1', 'mobil-1');
INSERT INTO `manufacturers` VALUES (220, 'MCQ', 'MCQUAY NORRIS', 'mcquay-norris');
INSERT INTO `manufacturers` VALUES (221, 'MCY', 'MOTOR CITY BEARINGS & SEALS', 'motor-city-bearings-seals');
INSERT INTO `manufacturers` VALUES (222, 'MEL', 'MELLING', 'melling');
INSERT INTO `manufacturers` VALUES (223, 'MEV', 'MEVOTECH LP', 'mevotech-lp');
INSERT INTO `manufacturers` VALUES (224, 'MGA', 'MEGA AUTOMOTIVE', 'mega-automotive');
INSERT INTO `manufacturers` VALUES (225, 'MGF', 'MAGNAFLOW PERF. EXHAUST', 'magnaflow-perf-exhaust');
INSERT INTO `manufacturers` VALUES (226, 'MHH', 'MOORES CYLINDER HEADS', 'moores-cylinder-heads');
INSERT INTO `manufacturers` VALUES (227, 'MHL', 'MAHLE ORIGINAL', 'mahle-original');
INSERT INTO `manufacturers` VALUES (228, 'MMK', 'MILEAGE MAKER', 'mileage-maker');
INSERT INTO `manufacturers` VALUES (229, 'MNH', 'MANN-FILTER', 'mann-filter');
INSERT INTO `manufacturers` VALUES (230, 'MNP', 'PRIVATE BRAND-MONROE', 'private-brand-monroe');
INSERT INTO `manufacturers` VALUES (231, 'MOE', 'MONROE SHOCKS/STRUTS', 'monroe-shocks-struts');
INSERT INTO `manufacturers` VALUES (232, 'MOO', 'MOOG', 'moog');
INSERT INTO `manufacturers` VALUES (233, 'MOP', 'MOPAR PARTS', 'mopar-parts');
INSERT INTO `manufacturers` VALUES (234, 'MOT', 'MOTORCRAFT', 'motorcraft');
INSERT INTO `manufacturers` VALUES (235, 'MOV', 'MOPAR VALUE LINE', 'mopar-value-line');
INSERT INTO `manufacturers` VALUES (236, 'MPA', 'QUALITY-BUILT', 'quality-built');
INSERT INTO `manufacturers` VALUES (237, 'MRG', 'MR. GASKET', 'mr-gasket');
INSERT INTO `manufacturers` VALUES (238, 'MSD', 'MSD IGNIT.', 'msd-ignit');
INSERT INTO `manufacturers` VALUES (239, 'MSI', 'MAS INDUSTRIES', 'mas-industries');
INSERT INTO `manufacturers` VALUES (240, 'MTN', 'METRON', 'metron');
INSERT INTO `manufacturers` VALUES (241, 'MTO', 'MOTORAD', 'motorad');
INSERT INTO `manufacturers` VALUES (242, 'MVC', 'MEVOTECH CONTROL ARMS', 'mevotech-control-arms');
INSERT INTO `manufacturers` VALUES (243, 'N29', 'NGK CANADA BASE NUMBERS', 'ngk-canada-base-numbers');
INSERT INTO `manufacturers` VALUES (244, 'N30', 'NGK CANADA STOCK NUMBERS', 'ngk-canada-stock-numbers');
INSERT INTO `manufacturers` VALUES (245, 'NAI', 'NASTRA AUTOMOTIVE IND, INC.', 'nastra-automotive-ind-inc');
INSERT INTO `manufacturers` VALUES (246, 'NAT', 'NATIONAL SEALS', 'national-seals');
INSERT INTO `manufacturers` VALUES (247, 'NDE', 'DENSO', 'denso');
INSERT INTO `manufacturers` VALUES (248, 'NEP', 'NEAPCO', 'neapco');
INSERT INTO `manufacturers` VALUES (249, 'NGB', 'NGK USA BASE NUMBERS', 'ngk-usa-base-numbers');
INSERT INTO `manufacturers` VALUES (250, 'NGK', 'NGK USA STOCK NUMBERS', 'ngk-usa-stock-numbers');
INSERT INTO `manufacturers` VALUES (251, 'NGN', 'NEW GENERATION ELECTRIC', 'new-generation-electric');
INSERT INTO `manufacturers` VALUES (252, 'NOR', 'NORTHSTAR', 'northstar');
INSERT INTO `manufacturers` VALUES (253, 'NPM', 'NEEDA PARTS MANUFACTURING', 'needa-parts-manufacturing');
INSERT INTO `manufacturers` VALUES (254, 'NSA', 'NSA', 'nsa');
INSERT INTO `manufacturers` VALUES (255, 'NSE', 'NISSENS NORTH AMERICA INC.', 'nissens-north-america-inc');
INSERT INTO `manufacturers` VALUES (256, 'NTK', 'NGK CANADA/NTK SENSORS', 'ngk-canada-ntk-sensors');
INSERT INTO `manufacturers` VALUES (257, 'NUG', 'NUGEON (OLD)', 'nugeon-old');
INSERT INTO `manufacturers` VALUES (258, 'NWB', 'NATIONWIDE BATTERIES', 'nationwide-batteries');
INSERT INTO `manufacturers` VALUES (259, 'NWC', 'NEW GENERATION/AMS', 'new-generation-ams');
INSERT INTO `manufacturers` VALUES (260, 'NWT', 'NEWTEK AUTOMOTIVE', 'newtek-automotive');
INSERT INTO `manufacturers` VALUES (261, 'O19', 'OSC', 'osc');
INSERT INTO `manufacturers` VALUES (262, 'OEM', 'ORIGINAL ENGINE MANAGEMENT', 'original-engine-management');
INSERT INTO `manufacturers` VALUES (263, 'OET', 'OMEGA ENVIRONMENTAL TECHNOLOGIES', 'omega-environmental-technologies');
INSERT INTO `manufacturers` VALUES (264, 'OME', 'OMEGA', 'omega');
INSERT INTO `manufacturers` VALUES (265, 'OMN', 'OMNISPARK', 'omnispark');
INSERT INTO `manufacturers` VALUES (266, 'P58', 'PARTS PLUS THERMOSTATS & CAPS', 'parts-plus-thermostats-caps');
INSERT INTO `manufacturers` VALUES (267, 'P61', 'PARTS PLUS CHASSIS', 'parts-plus-chassis');
INSERT INTO `manufacturers` VALUES (268, 'P64', 'PARTS PLUS BRAKE HARDWARE', 'parts-plus-brake-hardware');
INSERT INTO `manufacturers` VALUES (269, 'P65', 'PARTS PLUS NEW WATER PUMPS', 'parts-plus-new-water-pumps');
INSERT INTO `manufacturers` VALUES (270, 'P66', 'PARTS MASTER/ CARDONE', 'parts-master-cardone');
INSERT INTO `manufacturers` VALUES (271, 'P67', 'PARTS MASTER/ACI', 'parts-master-aci');
INSERT INTO `manufacturers` VALUES (272, 'P68', 'PARTS MASTER/ANCHOR', 'parts-master-anchor');
INSERT INTO `manufacturers` VALUES (273, 'P75', 'PARTS MASTER/EAST PENN', 'parts-master-east-penn');
INSERT INTO `manufacturers` VALUES (274, 'P77', 'PARTS MASTER/FOUR SEASONS', 'parts-master-four-seasons');
INSERT INTO `manufacturers` VALUES (275, 'P80', 'PARTS MASTER/HAYDEN', 'parts-master-hayden');
INSERT INTO `manufacturers` VALUES (276, 'P81', 'PARTS MASTER CHASSIS', 'parts-master-chassis');
INSERT INTO `manufacturers` VALUES (277, 'P83', 'PARTS MASTER/POWERTRAIN COMPONENTS', 'parts-master-powertrain-components');
INSERT INTO `manufacturers` VALUES (278, 'P91', 'PARTS MASTER/WIX', 'parts-master-wix');
INSERT INTO `manufacturers` VALUES (279, 'P92', 'PARTS PLUS/ANCHOR', 'parts-plus-anchor');
INSERT INTO `manufacturers` VALUES (280, 'P93', 'PARTS PLUS/WESTAR', 'parts-plus-westar');
INSERT INTO `manufacturers` VALUES (281, 'P94', 'PARTS PLUS BEARINGS & SEALS', 'parts-plus-bearings-seals');
INSERT INTO `manufacturers` VALUES (282, 'P95', 'PARTS MASTER/MOTORAD', 'parts-master-motorad');
INSERT INTO `manufacturers` VALUES (283, 'P97', 'PARTS MASTER/GKI', 'parts-master-gki');
INSERT INTO `manufacturers` VALUES (284, 'PAN', 'PIK-A-NUT', 'pik-a-nut');
INSERT INTO `manufacturers` VALUES (285, 'PAU', 'COAST TO COAST AUTOMOTIVE PRODUCTS', 'coast-to-coast-automotive-products');
INSERT INTO `manufacturers` VALUES (286, 'PBX', 'POWER BRAKE EXCHANGE', 'power-brake-exchange');
INSERT INTO `manufacturers` VALUES (287, 'PCO', 'PROFESSIONALS\' CHOICE', 'professionals-choice');
INSERT INTO `manufacturers` VALUES (288, 'PCR', 'PERFORMANCE RADIATOR', 'performance-radiator');
INSERT INTO `manufacturers` VALUES (289, 'PCW', 'PRO-START WIRE', 'pro-start-wire');
INSERT INTO `manufacturers` VALUES (290, 'PDB', 'PRONTO DASH 4', 'pronto-dash-4');
INSERT INTO `manufacturers` VALUES (291, 'PEI', 'PRECISION REMANUFACTURING INC.', 'precision-remanufacturing-inc');
INSERT INTO `manufacturers` VALUES (292, 'PER', 'PERFECT CIRCLE', 'perfect-circle');
INSERT INTO `manufacturers` VALUES (293, 'PFC', 'PRONTO/FALCON', 'pronto-falcon');
INSERT INTO `manufacturers` VALUES (294, 'PFN', 'PERFORMANCE FRICTION CORP.', 'performance-friction-corp');
INSERT INTO `manufacturers` VALUES (295, 'PFX', 'PERFORMAX', 'performax');
INSERT INTO `manufacturers` VALUES (296, 'PHI', 'ZEROSTART (PHILLIPS & TEMRO)', 'zerostart-phillips-temro');
INSERT INTO `manufacturers` VALUES (297, 'PHT', 'PERFECTION CLUTCH', 'perfection-clutch');
INSERT INTO `manufacturers` VALUES (298, 'PIB', 'PROLIANCE INTERCOOLERS', 'proliance-intercoolers');
INSERT INTO `manufacturers` VALUES (299, 'PID', 'PRONTO/DURAGO', 'pronto-durago');
INSERT INTO `manufacturers` VALUES (300, 'PIO', 'PIONEER INC.', 'pioneer-inc');
INSERT INTO `manufacturers` VALUES (301, 'PKI', 'PRO-KING AUTOMOTIVE PRODUCTS', 'pro-king-automotive-products');
INSERT INTO `manufacturers` VALUES (302, 'PLF', 'PARTS PLUS FILTERS BY PREMIUM GUARD', 'parts-plus-filters-by-premium-guard');
INSERT INTO `manufacturers` VALUES (303, 'PLP', 'PHILIPS LIGHTING COMPANY', 'philips-lighting-company');
INSERT INTO `manufacturers` VALUES (304, 'PME', 'PROMOTIVE', 'promotive');
INSERT INTO `manufacturers` VALUES (305, 'PMG', 'PARTS MASTER/GABRIEL', 'parts-master-gabriel');
INSERT INTO `manufacturers` VALUES (306, 'PMS', 'PARTS MASTER/STANDARD', 'parts-master-standard');
INSERT INTO `manufacturers` VALUES (307, 'PMT', 'PARTS MASTER/CONI-SEAL', 'parts-master-coni-seal');
INSERT INTO `manufacturers` VALUES (308, 'PNA', 'PENTIUS AUTOMOTIVE PARTS', 'pentius-automotive-parts');
INSERT INTO `manufacturers` VALUES (309, 'PNC', 'PRENCO', 'prenco');
INSERT INTO `manufacturers` VALUES (310, 'PNF', 'PRONTO/PREMIUM VISION', 'pronto-premium-vision');
INSERT INTO `manufacturers` VALUES (311, 'PNI', 'PRONTO/WESTAR', 'pronto-westar');
INSERT INTO `manufacturers` VALUES (312, 'PNK', 'PRONTO/PTC', 'pronto-ptc');
INSERT INTO `manufacturers` VALUES (313, 'PNM', 'PRONTO/MOTORAD', 'pronto-motorad');
INSERT INTO `manufacturers` VALUES (314, 'PNP', 'PRONTO/ID USA', 'pronto-id-usa');
INSERT INTO `manufacturers` VALUES (315, 'PNQ', 'PRONTO/EASTERN', 'pronto-eastern');
INSERT INTO `manufacturers` VALUES (316, 'PNU', 'PRONTO/DORMAN', 'pronto-dorman');
INSERT INTO `manufacturers` VALUES (317, 'PNV', 'PRONTO/COMPRESSOR WORKS', 'pronto-compressor-works');
INSERT INTO `manufacturers` VALUES (318, 'PNW', 'PRONTO/CARLSON', 'pronto-carlson');
INSERT INTO `manufacturers` VALUES (319, 'PNX', 'PRONTO/CARDONE', 'pronto-cardone');
INSERT INTO `manufacturers` VALUES (320, 'PNY', 'PRONTO/CADNA', 'pronto-cadna');
INSERT INTO `manufacturers` VALUES (321, 'PNZ', 'PRONTO/AIMCO', 'pronto-aimco');
INSERT INTO `manufacturers` VALUES (322, 'POA', 'PROAMP', 'proamp');
INSERT INTO `manufacturers` VALUES (323, 'POI', 'PARTS MASTER/OLD WORLD INDUSTRIES', 'parts-master-old-world-industries');
INSERT INTO `manufacturers` VALUES (324, 'POR', 'POWERITE AUTOMOTIVE INC.', 'powerite-automotive-inc');
INSERT INTO `manufacturers` VALUES (325, 'POX', 'PROMAX', 'promax');
INSERT INTO `manufacturers` VALUES (326, 'PPA', 'POWERMAX WIRE & CABLE', 'powermax-wire-cable');
INSERT INTO `manufacturers` VALUES (327, 'PPD', 'PARTS PLUS BRAKE/CENTRIC', 'parts-plus-brake-centric');
INSERT INTO `manufacturers` VALUES (328, 'PRB', 'POWERBOND', 'powerbond');
INSERT INTO `manufacturers` VALUES (329, 'PRE', 'PRECISION U-JOINTS', 'precision-u-joints');
INSERT INTO `manufacturers` VALUES (330, 'PRG', 'PREMIUM GUARD', 'premium-guard');
INSERT INTO `manufacturers` VALUES (331, 'PRI', 'PRIOR', 'prior');
INSERT INTO `manufacturers` VALUES (332, 'PRV', 'PREMIUM VISION', 'premium-vision');
INSERT INTO `manufacturers` VALUES (333, 'PRX', 'PROMECANIX', 'promecanix');
INSERT INTO `manufacturers` VALUES (334, 'PSA', 'PARTS MASTER/EDELMANN', 'parts-master-edelmann');
INSERT INTO `manufacturers` VALUES (335, 'PSB', 'PARTS MASTER/TRIDON FLASHERS', 'parts-master-tridon-flashers');
INSERT INTO `manufacturers` VALUES (336, 'PSE', 'PARTS MASTER/CADNA', 'parts-master-cadna');
INSERT INTO `manufacturers` VALUES (337, 'PSF', 'PARTS MASTER/FENWICK (FENCO)', 'parts-master-fenwick-fenco');
INSERT INTO `manufacturers` VALUES (338, 'PSG', 'PARTS MASTER/FALCON STEERING SYST', 'parts-master-falcon-steering-syst');
INSERT INTO `manufacturers` VALUES (339, 'PSH', 'PRECISION SHIFT', 'precision-shift');
INSERT INTO `manufacturers` VALUES (340, 'PSJ', 'PARTS MASTER/EIKO LIGHTING', 'parts-master-eiko-lighting');
INSERT INTO `manufacturers` VALUES (341, 'PSK', 'PARTS MASTER/BRAKE ROTOR & DRUM', 'parts-master-brake-rotor-drum');
INSERT INTO `manufacturers` VALUES (342, 'PSL', 'PRESTOLITE PROCONNECT', 'prestolite-proconnect');
INSERT INTO `manufacturers` VALUES (343, 'PSM', 'PARTS MASTER/EXIDE', 'parts-master-exide');
INSERT INTO `manufacturers` VALUES (344, 'PST', 'PRESTONE', 'prestone');
INSERT INTO `manufacturers` VALUES (345, 'PSW', 'PARTS MASTER/ CARDONE SELECT', 'parts-master-cardone-select');
INSERT INTO `manufacturers` VALUES (346, 'PTC', 'POWERTRAIN COMPONENTS (PTC)', 'powertrain-components-ptc');
INSERT INTO `manufacturers` VALUES (347, 'PTD', 'PARTS PLUS DRUMS AND ROTORS', 'parts-plus-drums-and-rotors');
INSERT INTO `manufacturers` VALUES (348, 'PTE', 'PRO TEC FILTERS', 'pro-tec-filters');
INSERT INTO `manufacturers` VALUES (349, 'PTF', 'PRONTO/FEDERAL MOGUL', 'pronto-federal-mogul');
INSERT INTO `manufacturers` VALUES (350, 'PUR', 'PUROLATOR', 'purolator');
INSERT INTO `manufacturers` VALUES (351, 'PWR', 'POWERCRAFT', 'powercraft');
INSERT INTO `manufacturers` VALUES (352, 'PWS', 'POWER STOP', 'power-stop');
INSERT INTO `manufacturers` VALUES (353, 'PYL', 'PYLON', 'pylon');
INSERT INTO `manufacturers` VALUES (354, 'PZL', 'PENNZOIL', 'pennzoil');
INSERT INTO `manufacturers` VALUES (355, 'Q02', 'QUALIS', 'qualis');
INSERT INTO `manufacturers` VALUES (356, 'Q18', 'CARQUEST/GATES', 'carquest-gates');
INSERT INTO `manufacturers` VALUES (357, 'Q20', 'CARQUEST/CARDONE', 'carquest-cardone');
INSERT INTO `manufacturers` VALUES (358, 'Q35', 'CARQUEST FUEL PUMPS', 'carquest-fuel-pumps');
INSERT INTO `manufacturers` VALUES (359, 'Q39', 'CARQUEST/MELLING', 'carquest-melling');
INSERT INTO `manufacturers` VALUES (360, 'Q44', 'CARQUEST/DAYCO', 'carquest-dayco');
INSERT INTO `manufacturers` VALUES (361, 'Q45', 'CARQUEST CAPS & STATS', 'carquest-caps-stats');
INSERT INTO `manufacturers` VALUES (362, 'Q49', 'CARQUEST/TRICO', 'carquest-trico');
INSERT INTO `manufacturers` VALUES (363, 'Q51', 'CARQUEST/IGNITION WIRES', 'carquest-ignition-wires');
INSERT INTO `manufacturers` VALUES (364, 'Q57', 'CARQUEST POWER STEERING', 'carquest-power-steering');
INSERT INTO `manufacturers` VALUES (365, 'Q59', 'CARQUEST/SPX FILTRAN', 'carquest-spx-filtran');
INSERT INTO `manufacturers` VALUES (366, 'Q60', 'CARQUEST/EAST PENN MANUFACTURING', 'carquest-east-penn-manufacturing');
INSERT INTO `manufacturers` VALUES (367, 'Q68', 'CARQUEST RED/GK INDUSTRIES US', 'carquest-red-gk-industries-us');
INSERT INTO `manufacturers` VALUES (368, 'Q74', 'CARQUEST/DELPHI', 'carquest-delphi');
INSERT INTO `manufacturers` VALUES (369, 'Q82', 'CARQUEST/KYB', 'carquest-kyb');
INSERT INTO `manufacturers` VALUES (370, 'Q98', 'CARQUEST ELECTRICAL', 'carquest-electrical');
INSERT INTO `manufacturers` VALUES (371, 'QRB', 'QUALITY REMANUFACTURING', 'quality-remanufacturing');
INSERT INTO `manufacturers` VALUES (372, 'QU8', 'CARQUEST/AKEBONO', 'carquest-akebono');
INSERT INTO `manufacturers` VALUES (373, 'QUA', 'FDP BRAKES', 'fdp-brakes');
INSERT INTO `manufacturers` VALUES (374, 'QUS', 'QUAKER STATE', 'quaker-state');
INSERT INTO `manufacturers` VALUES (375, 'QV5', 'CARQUEST RED/CHASSIS CANADA', 'carquest-red-chassis-canada');
INSERT INTO `manufacturers` VALUES (376, 'QW0', 'CARQUEST/MEVOTECH', 'carquest-mevotech');
INSERT INTO `manufacturers` VALUES (377, 'QW1', 'CARQUEST RED/BEARING', 'carquest-red-bearing');
INSERT INTO `manufacturers` VALUES (378, 'QX3', 'CARQUEST RED/SIEMENS CPI', 'carquest-red-siemens-cpi');
INSERT INTO `manufacturers` VALUES (379, 'QY4', 'CARQUEST/WALKER', 'carquest-walker');
INSERT INTO `manufacturers` VALUES (380, 'RAN', 'RANCHO', 'rancho');
INSERT INTO `manufacturers` VALUES (381, 'RAY', 'RAYBESTOS', 'raybestos');
INSERT INTO `manufacturers` VALUES (382, 'RCH', 'REACH COOLING', 'reach-cooling');
INSERT INTO `manufacturers` VALUES (383, 'REE', 'REESE TOWPOWER', 'reese-towpower');
INSERT INTO `manufacturers` VALUES (384, 'REL', 'RELIANCE AUTOMOTIVE', 'reliance-automotive');
INSERT INTO `manufacturers` VALUES (385, 'REY', 'RETECH', 'retech');
INSERT INTO `manufacturers` VALUES (386, 'RHO', 'RHINOPAC/AMS', 'rhinopac-ams');
INSERT INTO `manufacturers` VALUES (387, 'RKP', 'DNJ ENGINE COMPONENTS', 'dnj-engine-components');
INSERT INTO `manufacturers` VALUES (388, 'RMX', 'ROADMAX', 'roadmax');
INSERT INTO `manufacturers` VALUES (389, 'RMY', 'REMY', 'remy');
INSERT INTO `manufacturers` VALUES (390, 'RNB', 'DORMAN - HELP', 'dorman-help');
INSERT INTO `manufacturers` VALUES (391, 'RNV', 'RENOVATOR', 'renovator');
INSERT INTO `manufacturers` VALUES (392, 'RNX', 'RAIN X', 'rain-x');
INSERT INTO `manufacturers` VALUES (393, 'ROB', 'ROBERK', 'roberk');
INSERT INTO `manufacturers` VALUES (394, 'RPR', 'RICH PORTER TECH', 'rich-porter-tech');
INSERT INTO `manufacturers` VALUES (395, 'S62', 'STEMPF AUTOMOTIVE INDUSTRIES', 'stempf-automotive-industries');
INSERT INTO `manufacturers` VALUES (396, 'SAC', 'SACHS', 'sachs');
INSERT INTO `manufacturers` VALUES (397, 'SAN', 'SANTECH INDUSTRIES', 'santech-industries');
INSERT INTO `manufacturers` VALUES (398, 'SAP', 'SCHRADER ELECTRONICS', 'schrader-electronics');
INSERT INTO `manufacturers` VALUES (399, 'SCD', 'STANT SMALL CARD RETAIL', 'stant-small-card-retail');
INSERT INTO `manufacturers` VALUES (400, 'SCM', 'SERVICE CHAMP', 'service-champ');
INSERT INTO `manufacturers` VALUES (401, 'SEA', 'SEALED POWER', 'sealed-power');
INSERT INTO `manufacturers` VALUES (402, 'SEC', 'AUTO EXTRA CANADA', 'auto-extra-canada');
INSERT INTO `manufacturers` VALUES (403, 'SFW', 'STANDARD FLYWHEEL', 'standard-flywheel');
INSERT INTO `manufacturers` VALUES (404, 'SHM', 'SHEE-MAR', 'shee-mar');
INSERT INTO `manufacturers` VALUES (405, 'SIE', 'VDO', 'vdo');
INSERT INTO `manufacturers` VALUES (406, 'SKF', 'SKF (CHICAGO RAWHIDE)', 'skf-chicago-rawhide');
INSERT INTO `manufacturers` VALUES (407, 'SOH', 'STOPTECH', 'stoptech');
INSERT INTO `manufacturers` VALUES (408, 'SOS', 'PROSPARK WIRE SETS', 'prospark-wire-sets');
INSERT INTO `manufacturers` VALUES (409, 'SPC', 'SPECTRA PREMIUM IND, INC.', 'spectra-premium-ind-inc');
INSERT INTO `manufacturers` VALUES (410, 'SPE', 'SPECIALTY PRODUCTS', 'specialty-products');
INSERT INTO `manufacturers` VALUES (411, 'SPF', 'SPECTRE PERFORMANCE', 'spectre-performance');
INSERT INTO `manufacturers` VALUES (412, 'SPI', 'RAYBESTOS CHASSIS', 'raybestos-chassis');
INSERT INTO `manufacturers` VALUES (413, 'SPV', 'STANT PRIVATE LABEL', 'stant-private-label');
INSERT INTO `manufacturers` VALUES (414, 'STA', 'STANDARD MOTOR PRODUCTS', 'standard-motor-products');
INSERT INTO `manufacturers` VALUES (415, 'STB', 'STANT - BLISTER PACK', 'stant-blister-pack');
INSERT INTO `manufacturers` VALUES (416, 'STN', 'STANT', 'stant');
INSERT INTO `manufacturers` VALUES (417, 'STP', 'STP', 'stp');
INSERT INTO `manufacturers` VALUES (418, 'STR', 'STRONG ARM', 'strong-arm');
INSERT INTO `manufacturers` VALUES (419, 'SWR', 'STEWART RIDDLE', 'stewart-riddle');
INSERT INTO `manufacturers` VALUES (420, 'SYL', 'SYLVANIA', 'sylvania');
INSERT INTO `manufacturers` VALUES (421, 'SYR', 'SYLVANIA RETAIL PACKS', 'sylvania-retail-packs');
INSERT INTO `manufacturers` VALUES (422, 'TBW', 'TBC WHEELS', 'tbc-wheels');
INSERT INTO `manufacturers` VALUES (423, 'TCO', 'TOMCO CARBURETOR', 'tomco-carburetor');
INSERT INTO `manufacturers` VALUES (424, 'TIM', 'TIMKEN', 'timken');
INSERT INTO `manufacturers` VALUES (425, 'TOM', 'TOMCO EMISSIONS/FUEL INJECTION', 'tomco-emissions-fuel-injection');
INSERT INTO `manufacturers` VALUES (426, 'TOP', 'XTREME STOP', 'xtreme-stop');
INSERT INTO `manufacturers` VALUES (427, 'TQF', 'TORQFLO BY COMPRESSORWORKS, INC.', 'torqflo-by-compressorworks-inc');
INSERT INTO `manufacturers` VALUES (428, 'TRD', 'NOVITA FLASHERS', 'novita-flashers');
INSERT INTO `manufacturers` VALUES (429, 'TRH', 'TRIDON WIPERS', 'tridon-wipers');
INSERT INTO `manufacturers` VALUES (430, 'TRI', 'TRICO', 'trico');
INSERT INTO `manufacturers` VALUES (431, 'TRT', 'TRU TORQUE', 'tru-torque');
INSERT INTO `manufacturers` VALUES (432, 'TSR', 'TRUSTAR', 'trustar');
INSERT INTO `manufacturers` VALUES (433, 'TWC', 'TRICO CANADA', 'trico-canada');
INSERT INTO `manufacturers` VALUES (434, 'TYC', 'TYC', 'tyc');
INSERT INTO `manufacturers` VALUES (435, 'UAC', 'UNIVERSAL AIR CONDITIONER, INC.', 'universal-air-conditioner-inc');
INSERT INTO `manufacturers` VALUES (436, 'UFE', 'ULTRA FIT EXHAUST', 'ultra-fit-exhaust');
INSERT INTO `manufacturers` VALUES (437, 'UIE', 'USA INDUSTRIES INC.', 'usa-industries-inc');
INSERT INTO `manufacturers` VALUES (438, 'UIW', 'UNITED MOTOR PRODUCTS', 'united-motor-products');
INSERT INTO `manufacturers` VALUES (439, 'UNA', 'UNI-SELECT CAN/PRO SERIES WATER PUMPS', 'uni-select-can-pro-series-water-pumps');
INSERT INTO `manufacturers` VALUES (440, 'UND', 'UNDERCAR EXPRESS', 'undercar-express');
INSERT INTO `manufacturers` VALUES (441, 'UNM', 'UNI-SELECT/PRO SERIES MOUNTS', 'uni-select-pro-series-mounts');
INSERT INTO `manufacturers` VALUES (442, 'UPI', 'UNIPOINT', 'unipoint');
INSERT INTO `manufacturers` VALUES (443, 'URC', 'UNITED REMANUFACTURING CO', 'united-remanufacturing-co');
INSERT INTO `manufacturers` VALUES (444, 'VAL', 'VALLEY', 'valley');
INSERT INTO `manufacturers` VALUES (445, 'VEN', 'VENTSHADE', 'ventshade');
INSERT INTO `manufacturers` VALUES (446, 'VEO', 'VALEO', 'valeo');
INSERT INTO `manufacturers` VALUES (447, 'VST', 'VISTEON', 'visteon');
INSERT INTO `manufacturers` VALUES (448, 'W08', 'WARNER', 'warner');
INSERT INTO `manufacturers` VALUES (449, 'WAI', 'WAI WORLD POWER SYSTEMS', 'wai-world-power-systems');
INSERT INTO `manufacturers` VALUES (450, 'WAL', 'WALKER', 'walker');
INSERT INTO `manufacturers` VALUES (451, 'WBI', 'WHEEL BEARINGS INC.', 'wheel-bearings-inc');
INSERT INTO `manufacturers` VALUES (452, 'WCP', 'WORLD CAR/AMS', 'world-car-ams');
INSERT INTO `manufacturers` VALUES (453, 'WEL', 'WELLS', 'wells');
INSERT INTO `manufacturers` VALUES (454, 'WGC', 'WAGNER BRAKE', 'wagner-brake');
INSERT INTO `manufacturers` VALUES (455, 'WIR', 'WIRETEC IGNITION WIRES', 'wiretec-ignition-wires');
INSERT INTO `manufacturers` VALUES (456, 'WIX', 'WIX', 'wix');
INSERT INTO `manufacturers` VALUES (457, 'WLF', 'WOLF READY FIT COVERS', 'wolf-ready-fit-covers');
INSERT INTO `manufacturers` VALUES (458, 'WLP', 'WAGNER LIGHTING', 'wagner-lighting');
INSERT INTO `manufacturers` VALUES (459, 'WNH', 'PARTS MASTER/WINHERE', 'parts-master-winhere');
INSERT INTO `manufacturers` VALUES (460, 'WOH', 'TRAKMOTIVE', 'trakmotive');
INSERT INTO `manufacturers` VALUES (461, 'WPI', 'WALKER PRODUCTS, INC.', 'walker-products-inc');
INSERT INTO `manufacturers` VALUES (462, 'WSR', 'WESTAR', 'westar');
INSERT INTO `manufacturers` VALUES (463, 'WXC', 'WIX CANADA', 'wix-canada');
INSERT INTO `manufacturers` VALUES (464, 'X06', 'EUROROTOR', 'eurorotor');
INSERT INTO `manufacturers` VALUES (465, 'X28', 'XPRESS AUTOMOTIVE', 'xpress-automotive');
INSERT INTO `manufacturers` VALUES (466, 'LFD', 'LEMFORDER STEERING & SUSPENSION', 'lemforder-steering-suspension');
INSERT INTO `manufacturers` VALUES (467, 'OEP', 'ORIG EQUIP PLUS', 'orig-equip-plus');
INSERT INTO `manufacturers` VALUES (468, 'PMW', 'PARTS MASTER/DORMAN', 'parts-master-dorman');
INSERT INTO `manufacturers` VALUES (469, 'PMY', 'PARTS MASTER/TRICO', 'parts-master-trico');
INSERT INTO `manufacturers` VALUES (470, 'PPE', 'PARTS PLUS/EAST PENN', 'parts-plus-east-penn');
INSERT INTO `manufacturers` VALUES (471, 'UNB', 'UNI-SELECT/ROTATING ELECTRICAL', 'uni-select-rotating-electrical');
INSERT INTO `manufacturers` VALUES (472, 'GWP', 'GOODYEAR WIPER PRODUCTS', 'goodyear-wiper-products');
INSERT INTO `manufacturers` VALUES (473, 'UCC', 'UNI-SELECT/CLUTCHES CANADA', 'uni-select-clutches-canada');
INSERT INTO `manufacturers` VALUES (474, 'VSP', 'VISTA-PRO', 'vista-pro');
INSERT INTO `manufacturers` VALUES (475, 'LCR', 'LEACREE', 'leacree');
INSERT INTO `manufacturers` VALUES (476, 'DSB', 'DU-SO BRAKES', 'du-so-brakes');
INSERT INTO `manufacturers` VALUES (477, 'BQC', 'BOSCH BRAKE', 'bosch-brake');
INSERT INTO `manufacturers` VALUES (478, 'ANB', 'AMERISTART-NORTHERN BATTERY', 'ameristart-northern-battery');
INSERT INTO `manufacturers` VALUES (479, 'APF', 'AP EXHAUST W/FEDERAL CONVERTER', 'ap-exhaust-w-federal-converter');
INSERT INTO `manufacturers` VALUES (480, 'APK', 'AP EXHAUST W/O FEDERAL CONVERTER', 'ap-exhaust-w-o-federal-converter');
INSERT INTO `manufacturers` VALUES (481, 'ASN', 'AUTO 7', 'auto-7');
INSERT INTO `manufacturers` VALUES (482, 'BMT', 'BLUE MOUNTAIN', 'blue-mountain');
INSERT INTO `manufacturers` VALUES (483, 'CTV', 'CATCO CONVERTERS/CALIF ONLY', 'catco-converters-calif-only');
INSERT INTO `manufacturers` VALUES (484, 'DOR', 'DORMAN', 'dorman');
INSERT INTO `manufacturers` VALUES (485, 'F69', 'FOUR SEASONS PAC-KITS I', 'four-seasons-pac-kits-i');
INSERT INTO `manufacturers` VALUES (486, 'F70', 'FOUR SEASONS PAC-KITS III', 'four-seasons-pac-kits-iii');
INSERT INTO `manufacturers` VALUES (487, 'F71', 'FOUR SEASONS PAC-KITS V', 'four-seasons-pac-kits-v');
INSERT INTO `manufacturers` VALUES (488, 'FDW', 'SST PREMIUM BY WAGNER', 'sst-premium-by-wagner');
INSERT INTO `manufacturers` VALUES (489, 'MFC', 'MAGNAFLOW CARB COMPLIANT CONVERTER', 'magnaflow-carb-compliant-converter');
INSERT INTO `manufacturers` VALUES (490, 'MFS', 'MAGNAFLOW FEDERAL CONVERTER', 'magnaflow-federal-converter');
INSERT INTO `manufacturers` VALUES (491, 'PCM', 'PREFERRED COMPONENTS INC.', 'preferred-components-inc');
INSERT INTO `manufacturers` VALUES (492, 'PPH', 'PARTS PLUS/CLEAR PLUS WIPER PRODS', 'parts-plus-clear-plus-wiper-prods');
INSERT INTO `manufacturers` VALUES (493, 'PPK', 'PARTS PLUS BRAKE SHOES/FDP', 'parts-plus-brake-shoes-fdp');
INSERT INTO `manufacturers` VALUES (494, 'QT8', 'CARQUEST/AP EXHAUST 49 STATE ONLY', 'carquest-ap-exhaust-49-state-only');
INSERT INTO `manufacturers` VALUES (495, 'QU4', 'CARQUEST/AP EXHAUST CALIF ONLY', 'carquest-ap-exhaust-calif-only');
INSERT INTO `manufacturers` VALUES (496, 'UPE', 'UNI-SELECT/ PRO-SELECT ELECTRICAL-PSB', 'uni-select-pro-select-electrical-psb');
INSERT INTO `manufacturers` VALUES (497, 'W34', 'WALMART BATTERIES NORTH', 'walmart-batteries-north');
INSERT INTO `manufacturers` VALUES (498, 'W35', 'WALMART BATTERIES SOUTH', 'walmart-batteries-south');
INSERT INTO `manufacturers` VALUES (499, 'W36', 'WALMART RAIN X', 'walmart-rain-x');
INSERT INTO `manufacturers` VALUES (500, 'W41', 'WALMART TLE BLADES', 'walmart-tle-blades');
INSERT INTO `manufacturers` VALUES (501, 'WKC', 'WALKER CARB CONVERTER', 'walker-carb-converter');
INSERT INTO `manufacturers` VALUES (502, 'WKS', 'WALKER EPA CONVERTER', 'walker-epa-converter');
INSERT INTO `manufacturers` VALUES (503, 'WQC', 'CARQUEST/WALKER CARB CONV', 'carquest-walker-carb-conv');
INSERT INTO `manufacturers` VALUES (504, 'WQS', 'CARQUEST/WALKER EPA CONV', 'carquest-walker-epa-conv');
INSERT INTO `manufacturers` VALUES (505, 'ABO', 'APEX AUTOMOBILE PARTS', 'apex-automobile-parts');
INSERT INTO `manufacturers` VALUES (506, 'BBU', 'BEST BRAKES USA', 'best-brakes-usa');
INSERT INTO `manufacturers` VALUES (507, 'BSC', 'BREXHAUST CALIF CONVERTERS', 'brexhaust-calif-converters');
INSERT INTO `manufacturers` VALUES (508, 'BSF', 'BREXHAUST 49 STATE CONVERTERS', 'brexhaust-49-state-converters');
INSERT INTO `manufacturers` VALUES (509, 'CBK', 'STOPTECH BIG BRAKE KITS', 'stoptech-big-brake-kits');
INSERT INTO `manufacturers` VALUES (510, 'DCP', 'DURACELL BATTERIES', 'duracell-batteries');
INSERT INTO `manufacturers` VALUES (511, 'FFW', 'FEDERATED F-SERIES WIRE', 'federated-f-series-wire');
INSERT INTO `manufacturers` VALUES (512, 'GAM', 'GATES MEXICO', 'gates-mexico');
INSERT INTO `manufacturers` VALUES (513, 'KOV', 'KOOL-VUE REPLACEMENT MIRRORS', 'kool-vue-replacement-mirrors');
INSERT INTO `manufacturers` VALUES (514, 'P9A', 'PARTS MASTER/PULLEYS', 'parts-master-pulleys');
INSERT INTO `manufacturers` VALUES (515, 'P9B', 'PARTS MASTER/FMI', 'parts-master-fmi');
INSERT INTO `manufacturers` VALUES (516, 'PHR', 'PRO SOURCE', 'pro-source');
INSERT INTO `manufacturers` VALUES (517, 'STT', 'STANDARD T-SERIES', 'standard-t-series');
INSERT INTO `manufacturers` VALUES (518, 'UCD', 'UNI-SELECT/COOLING DEPOT', 'uni-select-cooling-depot');
INSERT INTO `manufacturers` VALUES (519, 'WBM', 'WAGNER BRAKES MEXICO', 'wagner-brakes-mexico');
INSERT INTO `manufacturers` VALUES (520, 'WUT', 'WILSON AUTO ELECTRIC', 'wilson-auto-electric');
INSERT INTO `manufacturers` VALUES (521, 'Z03', 'ZF', 'zf');
INSERT INTO `manufacturers` VALUES (522, 'ABK', 'ALL PRO/FMI', 'all-pro-fmi');
INSERT INTO `manufacturers` VALUES (523, 'ULB', 'ULTRA BRAKE SYSTEMS', 'ultra-brake-systems');
INSERT INTO `manufacturers` VALUES (524, 'ULC', 'ULTRA CHASSIS AND STEERING', 'ultra-chassis-and-steering');
INSERT INTO `manufacturers` VALUES (525, 'ABE', 'AUTOBEST', 'autobest');
INSERT INTO `manufacturers` VALUES (526, 'ADZ', 'APDI', 'apdi');
INSERT INTO `manufacturers` VALUES (527, 'APG', 'AP EXHAUST FEDERAL CONVERTER', 'ap-exhaust-federal-converter');
INSERT INTO `manufacturers` VALUES (528, 'APJ', 'AP EXHAUST CALIF COMPLIANT CONVERTERS', 'ap-exhaust-calif-compliant-converters');
INSERT INTO `manufacturers` VALUES (529, 'ATQ', 'ALLTRANS', 'alltrans');
INSERT INTO `manufacturers` VALUES (530, 'BWN', 'BWD INTERMOTOR', 'bwd-intermotor');
INSERT INTO `manufacturers` VALUES (531, 'CCH', 'CERTIFIED CHASSIS', 'certified-chassis');
INSERT INTO `manufacturers` VALUES (532, 'CEF', 'CENTRIC FLEET PERFORMANCE', 'centric-fleet-performance');
INSERT INTO `manufacturers` VALUES (533, 'DCA', 'ACDELCO', 'acdelco');
INSERT INTO `manufacturers` VALUES (534, 'DCB', 'ACDELCO GM ORIGINAL EQUIPMENT', 'acdelco-gm-original-equipment');
INSERT INTO `manufacturers` VALUES (535, 'DCC', 'ACDELCO GOLD/PROFESSIONAL', 'acdelco-gold-professional');
INSERT INTO `manufacturers` VALUES (536, 'DCD', 'ACDELCO SILVER/ADVANTAGE', 'acdelco-silver-advantage');
INSERT INTO `manufacturers` VALUES (537, 'DCE', 'ACDELCO SPECIALTY', 'acdelco-specialty');
INSERT INTO `manufacturers` VALUES (538, 'DCG', 'ACDELCO GM ORIGINAL EQUIPMENT CANADA', 'acdelco-gm-original-equipment-canada');
INSERT INTO `manufacturers` VALUES (539, 'DCH', 'ACDELCO GOLD/PROFESSIONAL CANADA', 'acdelco-gold-professional-canada');
INSERT INTO `manufacturers` VALUES (540, 'DCI', 'ACDELCO SILVER/ADVANTAGE CANADA', 'acdelco-silver-advantage-canada');
INSERT INTO `manufacturers` VALUES (541, 'DCJ', 'ACDELCO SPECIALTY CANADA', 'acdelco-specialty-canada');
INSERT INTO `manufacturers` VALUES (542, 'DCM', 'ACDELCO CANADA (NEW) (DELETE V24A01R1)', 'acdelco-canada-new-delete-v24a01r1');
INSERT INTO `manufacturers` VALUES (543, 'DCO', 'ACDELCO GOLD/PROFESSIONAL BRAKES CANADA', 'acdelco-gold-professional-brakes-canada');
INSERT INTO `manufacturers` VALUES (544, 'FAW', 'FRAM WASHABLE AIR', 'fram-washable-air');
INSERT INTO `manufacturers` VALUES (545, 'FDN', 'FEDERATED/NEWTEK AUTOMOTIVE', 'federated-newtek-automotive');
INSERT INTO `manufacturers` VALUES (546, 'FLN', 'FLENNOR AUTOMOTIVE', 'flennor-automotive');
INSERT INTO `manufacturers` VALUES (547, 'FSC', 'FEDERATED SRT CHASSIS', 'federated-srt-chassis');
INSERT INTO `manufacturers` VALUES (548, 'HDY', 'HANDY PACK', 'handy-pack');
INSERT INTO `manufacturers` VALUES (549, 'IMP', 'IMPERIAL', 'imperial');
INSERT INTO `manufacturers` VALUES (550, 'KAZ', 'KATS ENGINE HEATERS', 'kats-engine-heaters');
INSERT INTO `manufacturers` VALUES (551, 'MEA', 'MITSUBISHI ELECTRIC AUTOMOTIVE', 'mitsubishi-electric-automotive');
INSERT INTO `manufacturers` VALUES (552, 'MZL', 'MAXZONE VEHICLE LIGHTING', 'maxzone-vehicle-lighting');
INSERT INTO `manufacturers` VALUES (553, 'NAD', 'NASCAR ADVANTAGE', 'nascar-advantage');
INSERT INTO `manufacturers` VALUES (554, 'NDR', 'NETWORK/DURAGO', 'network-durago');
INSERT INTO `manufacturers` VALUES (555, 'QS3', 'CARQUEST/FIVE STAR', 'carquest-five-star');
INSERT INTO `manufacturers` VALUES (556, 'QS7', 'CARQUEST/MAXZONE LIGHTING', 'carquest-maxzone-lighting');
INSERT INTO `manufacturers` VALUES (557, 'QS8', 'CARQUEST/K SOURCE', 'carquest-k-source');
INSERT INTO `manufacturers` VALUES (558, 'QS9', 'CARQUEST/CARLSON', 'carquest-carlson');
INSERT INTO `manufacturers` VALUES (559, 'QT0', 'CARQUEST/CABIN AIR FILTERS', 'carquest-cabin-air-filters');
INSERT INTO `manufacturers` VALUES (560, 'QT1', 'CARQUEST/TPMS', 'carquest-tpms');
INSERT INTO `manufacturers` VALUES (561, 'RBS', 'RAYBESTOS STRUT & SPRING ASSYS', 'raybestos-strut-spring-assys');
INSERT INTO `manufacturers` VALUES (562, 'RMV', 'REMY VALU', 'remy-valu');
INSERT INTO `manufacturers` VALUES (563, 'RPT', 'RARE PARTS INC.', 'rare-parts-inc');
INSERT INTO `manufacturers` VALUES (564, 'SIB', 'SILBLADE', 'silblade');
INSERT INTO `manufacturers` VALUES (565, 'T66', 'TAAP WASHER PUMPS', 'taap-washer-pumps');
INSERT INTO `manufacturers` VALUES (566, 'TBE', 'TRANSBEC', 'transbec');
INSERT INTO `manufacturers` VALUES (567, 'TCS', 'TECHSMART', 'techsmart');
INSERT INTO `manufacturers` VALUES (568, 'UTY', 'UPARTS GROUP', 'uparts-group');
INSERT INTO `manufacturers` VALUES (569, 'V22', 'ADVANCE/DRIVEWORKS', 'advance-driveworks');
INSERT INTO `manufacturers` VALUES (570, 'V24', 'ADVANCE TIMING', 'advance-timing');
INSERT INTO `manufacturers` VALUES (571, 'V28', 'ADVANCE/WEAREVER GOLD', 'advance-wearever-gold');
INSERT INTO `manufacturers` VALUES (572, 'V29', 'ADVANCE/WEAREVER SILVER', 'advance-wearever-silver');
INSERT INTO `manufacturers` VALUES (573, 'V30', 'ADVANCE/WEAREVER', 'advance-wearever');
INSERT INTO `manufacturers` VALUES (574, 'V31', 'ADVANCE/PRO KING', 'advance-pro-king');
INSERT INTO `manufacturers` VALUES (575, 'V32', 'ADVANCE/OPTIMA', 'advance-optima');
INSERT INTO `manufacturers` VALUES (576, 'V33', 'ADVANCE/DIEHARD GOLD', 'advance-diehard-gold');
INSERT INTO `manufacturers` VALUES (577, 'V34', 'ADVANCE/DIEHARD SILVER', 'advance-diehard-silver');
INSERT INTO `manufacturers` VALUES (578, 'V36', 'ADVANCE/AUTOCRAFT FARM & TRUCK', 'advance-autocraft-farm-truck');
INSERT INTO `manufacturers` VALUES (579, 'V38', 'ADVANCE/MOTIVE GEAR', 'advance-motive-gear');
INSERT INTO `manufacturers` VALUES (580, 'V42', 'ADVANCE/ZOOM', 'advance-zoom');
INSERT INTO `manufacturers` VALUES (581, 'V43', 'ADVANCE/WAGNER BRAKES', 'advance-wagner-brakes');
INSERT INTO `manufacturers` VALUES (582, 'V44', 'ADVANCE/STRONGARM', 'advance-strongarm');
INSERT INTO `manufacturers` VALUES (583, 'VLN', 'VALVOLINE FILTERS', 'valvoline-filters');
INSERT INTO `manufacturers` VALUES (584, 'VOE', 'VISION-OE', 'vision-oe');
INSERT INTO `manufacturers` VALUES (585, 'WAR', 'WARN WINCH', 'warn-winch');
INSERT INTO `manufacturers` VALUES (586, 'WJB', 'WJB', 'wjb');
INSERT INTO `manufacturers` VALUES (587, 'UBG', 'ULTRA BEARINGS', 'ultra-bearings');
INSERT INTO `manufacturers` VALUES (588, 'ULW', 'ULTRA WIPER LINKAGES', 'ultra-wiper-linkages');
INSERT INTO `manufacturers` VALUES (589, 'VLP', 'VICTORY LAP', 'victory-lap');
INSERT INTO `manufacturers` VALUES (590, 'ELG', 'ELGIN INDUSTRIES', 'elgin-industries');
INSERT INTO `manufacturers` VALUES (591, 'FCL', 'FEDERATED CALIPERS', 'federated-calipers');
INSERT INTO `manufacturers` VALUES (592, 'MGH', 'MOOG HUB ASSEMBLIES', 'moog-hub-assemblies');
INSERT INTO `manufacturers` VALUES (593, 'PFA', 'PERFECT STOP FRICTION BY WAGNER', 'perfect-stop-friction-by-wagner');
INSERT INTO `manufacturers` VALUES (594, 'PFD', 'PERFECT STOP CERAMIC FRICTION', 'perfect-stop-ceramic-friction');
INSERT INTO `manufacturers` VALUES (595, 'PGF', 'PRIME GUARD FILTERS', 'prime-guard-filters');
INSERT INTO `manufacturers` VALUES (596, 'SCW', 'SERVICE CENTRAL WIPERS', 'service-central-wipers');
INSERT INTO `manufacturers` VALUES (597, 'UPM', 'UNI-SELECT/ PRO-SELECT ELECTRICAL-PSM', 'uni-select-pro-select-electrical-psm');
INSERT INTO `manufacturers` VALUES (598, 'UPU', 'UNI-SELECT/ PRO-SELECT ELECTRICAL-PSU', 'uni-select-pro-select-electrical-psu');
INSERT INTO `manufacturers` VALUES (599, 'V46', 'ADVANCE/ PREMIUM AC', 'advance-premium-ac');
INSERT INTO `manufacturers` VALUES (600, 'CFL', 'CHAMPION FILTERS', 'champion-filters');
INSERT INTO `manufacturers` VALUES (601, 'GBC', 'TRANSGLOBE AUTOMOTIVE', 'transglobe-automotive');
INSERT INTO `manufacturers` VALUES (602, 'HRI', 'HERI AUTOMOTIVE', 'heri-automotive');
INSERT INTO `manufacturers` VALUES (603, 'MQS', 'QUICKSTEER', 'quicksteer');
INSERT INTO `manufacturers` VALUES (604, 'SRX', 'SPIREX BEARINGS & SEALS', 'spirex-bearings-seals');
INSERT INTO `manufacturers` VALUES (605, 'X25', 'XRF - PLATINUM', 'xrf-platinum');
INSERT INTO `manufacturers` VALUES (606, 'FAC', 'FACET', 'facet');
INSERT INTO `manufacturers` VALUES (607, 'FCS', 'FCS AUTOMOTIVE', 'fcs-automotive');
INSERT INTO `manufacturers` VALUES (608, 'P9D', 'STOPMASTER', 'stopmaster');
INSERT INTO `manufacturers` VALUES (609, 'P9E', 'ULTRA STOP', 'ultra-stop');
INSERT INTO `manufacturers` VALUES (610, 'AIP', 'APE', 'ape');
INSERT INTO `manufacturers` VALUES (611, 'AUS', 'AUTOTECH ENGINEERING', 'autotech-engineering');
INSERT INTO `manufacturers` VALUES (612, 'CCK', 'CERTIFIED BRAKE ROTORS', 'certified-brake-rotors');
INSERT INTO `manufacturers` VALUES (613, 'DPG', 'DELPHI ORDER NUMBER', 'delphi-order-number');
INSERT INTO `manufacturers` VALUES (614, 'FWX', 'FEDERAL MAG XTS WIRE', 'federal-mag-xts-wire');
INSERT INTO `manufacturers` VALUES (615, 'ONX', 'ONIX AUTOMOTIVE', 'onix-automotive');
INSERT INTO `manufacturers` VALUES (616, 'OTP', 'ORO-TEK TPMS SENSORS', 'oro-tek-tpms-sensors');
INSERT INTO `manufacturers` VALUES (617, 'QLV', 'QUALIS/VIP', 'qualis-vip');
INSERT INTO `manufacturers` VALUES (618, 'RWP', 'ROCKLAND WORLD PARTS', 'rockland-world-parts');
INSERT INTO `manufacturers` VALUES (619, 'SGP', 'STEERING GEAR SUPER STORE', 'steering-gear-super-store');
INSERT INTO `manufacturers` VALUES (620, 'TTY', 'XTRA SEAL', 'xtra-seal');
INSERT INTO `manufacturers` VALUES (621, 'CEI', 'CEC INDUSTRIES', 'cec-industries');
INSERT INTO `manufacturers` VALUES (622, 'DTP', 'DILL TPMS', 'dill-tpms');
INSERT INTO `manufacturers` VALUES (623, 'LTN', 'LITENS', 'litens');
INSERT INTO `manufacturers` VALUES (624, 'NTA', 'NITOMA', 'nitoma');
INSERT INTO `manufacturers` VALUES (625, 'PKO', 'PEAK/OLD WORLD INDUSTRIES', 'peak-old-world-industries');
INSERT INTO `manufacturers` VALUES (626, 'V37', 'ADVANCE/DIEHARD RED', 'advance-diehard-red');
INSERT INTO `manufacturers` VALUES (627, 'V45', 'ADVANCE/KLEENVIEW', 'advance-kleenview');
INSERT INTO `manufacturers` VALUES (628, 'D56', 'DEC CARB CONVERTER', 'dec-carb-converter');
INSERT INTO `manufacturers` VALUES (629, 'D57', 'DEC EPA CONVERTER', 'dec-epa-converter');
INSERT INTO `manufacturers` VALUES (630, 'FLO', 'FLOWMASTER', 'flowmaster');
INSERT INTO `manufacturers` VALUES (631, 'QC1', 'CARQUEST/CEC INDUSTRIES', 'carquest-cec-industries');
INSERT INTO `manufacturers` VALUES (632, 'TWK', 'TRAILER WORKS', 'trailer-works');
INSERT INTO `manufacturers` VALUES (633, 'V47', 'ADVANCE/MONROE SHOCKS-STRUTS', 'advance-monroe-shocks-struts');
INSERT INTO `manufacturers` VALUES (634, 'XSJ', 'ROYAL PURPLE', 'royal-purple');
INSERT INTO `manufacturers` VALUES (635, 'Z2J', 'PRECISE', 'precise');
INSERT INTO `manufacturers` VALUES (636, 'AXH', 'AUTO EXTRA BATTERIES/EXIDE US', 'auto-extra-batteries-exide-us');
INSERT INTO `manufacturers` VALUES (637, 'SPK', 'SPLASH PRODUCTS', 'splash-products');
INSERT INTO `manufacturers` VALUES (638, 'AXI', 'USTART BATTERIES (DELETE V23A09R1)', 'ustart-batteries-delete-v23a09r1');
INSERT INTO `manufacturers` VALUES (639, 'EGD', 'ECOGARD', 'ecogard');
INSERT INTO `manufacturers` VALUES (640, 'EHR', '1800RADIATOR OEQ', '1800radiator-oeq');
INSERT INTO `manufacturers` VALUES (641, 'HTH', 'HITACHI', 'hitachi');
INSERT INTO `manufacturers` VALUES (642, 'MPG', 'MASTER PRO BELT/HOSE', 'master-pro-belt-hose');
INSERT INTO `manufacturers` VALUES (643, 'MUC', 'MURRAY CAPS & STATS', 'murray-caps-stats');
INSERT INTO `manufacturers` VALUES (644, 'OBB', 'BRAKEBEST', 'brakebest');
INSERT INTO `manufacturers` VALUES (645, 'OBI', 'BRAKEBEST SELECT IMPORT DIRECT', 'brakebest-select-import-direct');
INSERT INTO `manufacturers` VALUES (646, 'OBP', 'BESTEST FUEL PUMP', 'bestest-fuel-pump');
INSERT INTO `manufacturers` VALUES (647, 'OBR', 'BRAKEBEST SELECT', 'brakebest-select');
INSERT INTO `manufacturers` VALUES (648, 'OID', 'IMPORT DIRECT IGNITION', 'import-direct-ignition');
INSERT INTO `manufacturers` VALUES (649, 'OMW', 'O\'REILLY/MASTER PRO WATER PUMPS', 'o-reilly-master-pro-water-pumps');
INSERT INTO `manufacturers` VALUES (650, 'OPC', 'MASTER PRO CHASSIS', 'master-pro-chassis');
INSERT INTO `manufacturers` VALUES (651, 'OPD', 'O\'REILLY/MASTER PRO CONTROL ARMS', 'o-reilly-master-pro-control-arms');
INSERT INTO `manufacturers` VALUES (652, 'OPF', 'PRECISION FUEL PUMPS', 'precision-fuel-pumps');
INSERT INTO `manufacturers` VALUES (653, 'OPS', 'MASTER PRO STRUT MOUNTS', 'master-pro-strut-mounts');
INSERT INTO `manufacturers` VALUES (654, 'OSS', 'SUPER START', 'super-start');
INSERT INTO `manufacturers` VALUES (655, 'QR7', 'CARQUEST/WARN WINCH', 'carquest-warn-winch');
INSERT INTO `manufacturers` VALUES (656, 'STI', 'STANDARD INTERMOTOR WIRE', 'standard-intermotor-wire');
INSERT INTO `manufacturers` VALUES (657, 'TLU', 'TOTAL LUBRICANTS USA', 'total-lubricants-usa');
INSERT INTO `manufacturers` VALUES (658, 'UNY', 'UNITY AUTOMOTIVE', 'unity-automotive');
INSERT INTO `manufacturers` VALUES (659, 'VTB', 'VORTEX', 'vortex');
INSERT INTO `manufacturers` VALUES (660, 'BSB', 'STANDARD BRAKE', 'standard-brake');
INSERT INTO `manufacturers` VALUES (661, 'EHS', '1800RADIATOR PREMIUM', '1800radiator-premium');
INSERT INTO `manufacturers` VALUES (662, 'IDF', 'IMPORT DIRECT FUEL PUMP', 'import-direct-fuel-pump');
INSERT INTO `manufacturers` VALUES (663, 'MDP', 'MOOG DRIVELINE PRODUCTS', 'moog-driveline-products');
INSERT INTO `manufacturers` VALUES (664, 'MUR', 'MURRAY HEAT TRANSFER', 'murray-heat-transfer');
INSERT INTO `manufacturers` VALUES (665, 'OBA', 'BRAKEBEST SELECT CERAMIC', 'brakebest-select-ceramic');
INSERT INTO `manufacturers` VALUES (666, 'OIG', 'MASTER PRO IGNITION', 'master-pro-ignition');
INSERT INTO `manufacturers` VALUES (667, 'OMB', 'MASTER PRO BEARINGS/SEALS', 'master-pro-bearings-seals');
INSERT INTO `manufacturers` VALUES (668, 'OMD', 'MASTER PRO CV', 'master-pro-cv');
INSERT INTO `manufacturers` VALUES (669, 'OMF', 'MASTER PRO SELECT CV', 'master-pro-select-cv');
INSERT INTO `manufacturers` VALUES (670, 'OPW', 'MASTER PRO POWER STEERING', 'master-pro-power-steering');
INSERT INTO `manufacturers` VALUES (671, 'OSK', 'SUPER START KC', 'super-start-kc');
INSERT INTO `manufacturers` VALUES (672, 'OUJ', 'MASTER PRO U-JOINT', 'master-pro-u-joint');
INSERT INTO `manufacturers` VALUES (673, 'POW', 'POWER TORQUE', 'power-torque');
INSERT INTO `manufacturers` VALUES (674, 'SPZ', 'SPECTRA RADIATORS/SIZE CODES', 'spectra-radiators-size-codes');
INSERT INTO `manufacturers` VALUES (675, 'CTN', 'CATERAN', 'cateran');
INSERT INTO `manufacturers` VALUES (676, 'MUA', 'MURRAY A/C', 'murray-a-c');
INSERT INTO `manufacturers` VALUES (677, 'QBE', 'CARQUEST/BWP-EAST PENN', 'carquest-bwp-east-penn');
INSERT INTO `manufacturers` VALUES (678, 'AE1', 'AEM', 'aem');
INSERT INTO `manufacturers` VALUES (679, 'AGS', 'AGS COMPANY', 'ags-company');
INSERT INTO `manufacturers` VALUES (680, 'ASF', 'AUTOSAVER/FDP BRAKE PADS', 'autosaver-fdp-brake-pads');
INSERT INTO `manufacturers` VALUES (681, 'BSW', 'BLUE STREAK WIRE', 'blue-streak-wire');
INSERT INTO `manufacturers` VALUES (682, 'CPD', 'CRP/REIN', 'crp-rein');
INSERT INTO `manufacturers` VALUES (683, 'CPE', 'CRP/CONTITECH (INCHES)', 'crp-contitech-inches');
INSERT INTO `manufacturers` VALUES (684, 'CPF', 'CRP/CONTITECH (METRIC-IMPORT)', 'crp-contitech-metric-import');
INSERT INTO `manufacturers` VALUES (685, 'CPG', 'CRP/PENTOSIN', 'crp-pentosin');
INSERT INTO `manufacturers` VALUES (686, 'CPH', 'CRP/AJUSA', 'crp-ajusa');
INSERT INTO `manufacturers` VALUES (687, 'CTF', 'OE-SPEC BY WAGNER BRAKE', 'oe-spec-by-wagner-brake');
INSERT INTO `manufacturers` VALUES (688, 'DER', 'US MOTOR WORKS', 'us-motor-works');
INSERT INTO `manufacturers` VALUES (689, 'DHD', 'DORMAN - HD SOLUTIONS', 'dorman-hd-solutions');
INSERT INTO `manufacturers` VALUES (690, 'EMC', 'EASTERN CATALYTIC CARB CONVERTERS', 'eastern-catalytic-carb-converters');
INSERT INTO `manufacturers` VALUES (691, 'RTA', 'RB-TECH AUTOMOTIVE', 'rb-tech-automotive');
INSERT INTO `manufacturers` VALUES (692, 'RXL', 'RAIN-X CANADA LATITUDE 8IN1', 'rain-x-canada-latitude-8in1');
INSERT INTO `manufacturers` VALUES (693, 'Z3O', 'SA GEAR', 'sa-gear');
INSERT INTO `manufacturers` VALUES (694, 'GLM', 'GILMORE PRODUCTS', 'gilmore-products');
INSERT INTO `manufacturers` VALUES (695, 'MNG', 'MAGNUM GASKETS', 'magnum-gaskets');
INSERT INTO `manufacturers` VALUES (696, 'MPM', 'MONROE PREMIUM BRAKES CANADA', 'monroe-premium-brakes-canada');
INSERT INTO `manufacturers` VALUES (697, 'PCA', 'PROFORCE', 'proforce');
INSERT INTO `manufacturers` VALUES (698, 'PFW', 'PRO TEC FILTERS BY WIX', 'pro-tec-filters-by-wix');
INSERT INTO `manufacturers` VALUES (699, 'PWX', 'POWER STOP XPR', 'power-stop-xpr');
INSERT INTO `manufacturers` VALUES (700, 'RGR', 'RAINGUARD (R)', 'rainguard-r');
INSERT INTO `manufacturers` VALUES (701, 'TWA', 'TRW AUTOMOTIVE', 'trw-automotive');
INSERT INTO `manufacturers` VALUES (702, 'WDX', 'WD EXPRESS', 'wd-express');
INSERT INTO `manufacturers` VALUES (703, 'BMC', 'BENCHMARK CARB CONVERTER', 'benchmark-carb-converter');
INSERT INTO `manufacturers` VALUES (704, 'BMK', 'BENCHMARK EPA CONVERTER', 'benchmark-epa-converter');
INSERT INTO `manufacturers` VALUES (705, 'PNE', 'PRONTO/MAS', 'pronto-mas');
INSERT INTO `manufacturers` VALUES (706, 'V49', 'ADVANCE/CARQUEST WEAREVER FRONTLINE', 'advance-carquest-wearever-frontline');
INSERT INTO `manufacturers` VALUES (707, 'APY', 'APEX BY PILOT', 'apex-by-pilot');
INSERT INTO `manufacturers` VALUES (708, 'EBC', 'EXIDE BATTERIES CANADA', 'exide-batteries-canada');
INSERT INTO `manufacturers` VALUES (709, 'EXB', 'EXIDE BATTERIES OLD', 'exide-batteries-old');
INSERT INTO `manufacturers` VALUES (710, 'RTB', 'ROBERT THIBERT INC.', 'robert-thibert-inc');
INSERT INTO `manufacturers` VALUES (711, 'BLD', 'BULLDOG', 'bulldog');
INSERT INTO `manufacturers` VALUES (712, 'BRG', 'BARGMAN', 'bargman');
INSERT INTO `manufacturers` VALUES (713, 'C87', 'CEQUENT UNBRANDED', 'cequent-unbranded');
INSERT INTO `manufacturers` VALUES (714, 'CQP', 'PRO SERIES', 'pro-series');
INSERT INTO `manufacturers` VALUES (715, 'DRA', 'DRAW-TITE', 'draw-tite');
INSERT INTO `manufacturers` VALUES (716, 'HH9', 'HIDDEN HITCH', 'hidden-hitch');
INSERT INTO `manufacturers` VALUES (717, 'HIG', 'HIGHLAND', 'highland');
INSERT INTO `manufacturers` VALUES (718, 'RLA', 'ROLA', 'rola');
INSERT INTO `manufacturers` VALUES (719, 'SEN', 'SENSEN', 'sensen');
INSERT INTO `manufacturers` VALUES (720, 'SUF', 'SURE FLO', 'sure-flo');
INSERT INTO `manufacturers` VALUES (721, 'TKN', 'TEKONSHA', 'tekonsha');
INSERT INTO `manufacturers` VALUES (722, 'TSA', 'TRI-STAR', 'tri-star');
INSERT INTO `manufacturers` VALUES (723, 'TWY', 'TOW READY', 'tow-ready');
INSERT INTO `manufacturers` VALUES (724, 'UQP', 'UQUALITY AUTOMOTIVE PRODUCTS', 'uquality-automotive-products');
INSERT INTO `manufacturers` VALUES (725, 'WPU', 'WORLDPARTS BRAKE PADS & ROTORS-CANADA', 'worldparts-brake-pads-rotors-canada');
INSERT INTO `manufacturers` VALUES (726, 'XSA', 'REESE', 'reese');
INSERT INTO `manufacturers` VALUES (727, 'AAS', 'ARNOTT AIR SUSPENSION', 'arnott-air-suspension');
INSERT INTO `manufacturers` VALUES (728, 'AXJ', 'AUTO EXTRA/BEARING-SEALS-HUB ASSEMBLIES', 'auto-extra-bearing-seals-hub-assemblies');
INSERT INTO `manufacturers` VALUES (729, 'CBZ', 'CERTIFIED (JASPER)', 'certified-jasper');
INSERT INTO `manufacturers` VALUES (730, 'CPJ', 'CRP/CONTITECH (METRIC-FULL)', 'crp-contitech-metric-full');
INSERT INTO `manufacturers` VALUES (731, 'OIW', 'IMPORT DIRECT WATER PUMP', 'import-direct-water-pump');
INSERT INTO `manufacturers` VALUES (732, 'OSJ', 'SUPER START J', 'super-start-j');
INSERT INTO `manufacturers` VALUES (733, 'MVX', 'MEVOTECH TTX', 'mevotech-ttx');
INSERT INTO `manufacturers` VALUES (734, 'PEX', 'PEX GERMAN O.E. PARTS LLC', 'pex-german-o-e-parts-llc');
INSERT INTO `manufacturers` VALUES (735, 'PTM', 'PARTS MASTER/MEVOTECH CANADA', 'parts-master-mevotech-canada');
INSERT INTO `manufacturers` VALUES (736, 'CCJ', 'CERTIFIED BRAKE DRUMS & ROTORS', 'certified-brake-drums-rotors');
INSERT INTO `manufacturers` VALUES (737, 'V51', 'ADVANCE/NEEDA PARTS MANUFACTURING', 'advance-needa-parts-manufacturing');
INSERT INTO `manufacturers` VALUES (738, 'AXV', 'AUTO EXTRA SERP AND V BELTS', 'auto-extra-serp-and-v-belts');
INSERT INTO `manufacturers` VALUES (739, 'BPW', 'BWD PRO SERIES', 'bwd-pro-series');
INSERT INTO `manufacturers` VALUES (740, 'OMP', 'MURRAY TEMPERATURE CONTROL', 'murray-temperature-control');
INSERT INTO `manufacturers` VALUES (741, 'PFJ', 'PERFECT STOP HIGH CARBON', 'perfect-stop-high-carbon');
INSERT INTO `manufacturers` VALUES (742, 'PFS', 'PERFECT STOP CALIPERS', 'perfect-stop-calipers');
INSERT INTO `manufacturers` VALUES (743, 'QSA', 'CARQUEST/STRONGARM', 'carquest-strongarm');
INSERT INTO `manufacturers` VALUES (744, 'SCZ', 'SCHULTZ', 'schultz');
INSERT INTO `manufacturers` VALUES (745, 'STH', 'STANDARD PRO SERIES', 'standard-pro-series');
INSERT INTO `manufacturers` VALUES (746, 'TCW', 'TCW', 'tcw');
INSERT INTO `manufacturers` VALUES (747, 'USS', 'UNI-SELECT/SPECTRA PREMIUM INDUSTRIES', 'uni-select-spectra-premium-industries');
INSERT INTO `manufacturers` VALUES (748, 'WXP', 'WIX XP', 'wix-xp');
INSERT INTO `manufacturers` VALUES (749, '9ER', 'BULLY DOG', 'bully-dog');
INSERT INTO `manufacturers` VALUES (750, 'EHA', '1800RADIATOR PREMIUM A/C SERVICE KIT', '1800radiator-premium-a-c-service-kit');
INSERT INTO `manufacturers` VALUES (751, 'F80', 'FOUR SEASONS TECH SELECT KITS', 'four-seasons-tech-select-kits');
INSERT INTO `manufacturers` VALUES (752, 'NP0', 'NPW', 'npw');
INSERT INTO `manufacturers` VALUES (753, 'PKC', 'PEAK/SAMS CLUB', 'peak-sams-club');
INSERT INTO `manufacturers` VALUES (754, 'DSP', 'DANA SPICER', 'dana-spicer');
INSERT INTO `manufacturers` VALUES (755, 'JAP', 'JLC FUEL PRODUCTS', 'jlc-fuel-products');
INSERT INTO `manufacturers` VALUES (756, 'ADY', 'APDTY', 'apdty');
INSERT INTO `manufacturers` VALUES (757, 'D72', 'ACDELCO AIR COND KITS (DELETE V24A01R1)', 'acdelco-air-cond-kits-delete-v24a01r1');
INSERT INTO `manufacturers` VALUES (758, 'MOG', 'MEVOTECH ORIGINAL GRADE', 'mevotech-original-grade');
INSERT INTO `manufacturers` VALUES (759, 'MRB', 'MR BELTS', 'mr-belts');
INSERT INTO `manufacturers` VALUES (760, 'MSB', 'MITSUBOSHI', 'mitsuboshi');
INSERT INTO `manufacturers` VALUES (761, 'OMX', 'OMIX', 'omix');
INSERT INTO `manufacturers` VALUES (762, 'PSU', 'PERFECT STOP/UCX', 'perfect-stop-ucx');
INSERT INTO `manufacturers` VALUES (763, 'ZX8', 'GREN AUTOMOTIVE', 'gren-automotive');
INSERT INTO `manufacturers` VALUES (764, 'CBX', 'CERTIFIED BELTS', 'certified-belts');
INSERT INTO `manufacturers` VALUES (765, 'FP1', 'FRAM PRO CONVENTIONAL', 'fram-pro-conventional');
INSERT INTO `manufacturers` VALUES (766, 'FP2', 'FRAM PRO PLUS', 'fram-pro-plus');
INSERT INTO `manufacturers` VALUES (767, 'FP3', 'FRAM PRO SYNTHETIC', 'fram-pro-synthetic');
INSERT INTO `manufacturers` VALUES (768, 'FP4', 'FRAM ULTRA', 'fram-ultra');
INSERT INTO `manufacturers` VALUES (769, 'FXE', 'FX EXHAUST', 'fx-exhaust');
INSERT INTO `manufacturers` VALUES (770, 'KNX', 'KNX O2 SENSORS', 'knx-o2-sensors');
INSERT INTO `manufacturers` VALUES (771, 'MTD', 'MOTORAD PRESTONE', 'motorad-prestone');
INSERT INTO `manufacturers` VALUES (772, 'OIA', 'IMPORT DIRECT CONTROL ARMS', 'import-direct-control-arms');
INSERT INTO `manufacturers` VALUES (773, 'TOL', 'TOTAL AUTOMOTIVE', 'total-automotive');
INSERT INTO `manufacturers` VALUES (774, 'UNL', 'UNDERCAR EXPRESS LOADED', 'undercar-express-loaded');
INSERT INTO `manufacturers` VALUES (775, 'UNX', 'UNDERCAR EXPRESS FRICTION READY', 'undercar-express-friction-ready');
INSERT INTO `manufacturers` VALUES (776, '7D8', 'CIPA USA', 'cipa-usa');
INSERT INTO `manufacturers` VALUES (777, '7EI', 'COMETIC GASKET', 'cometic-gasket');
INSERT INTO `manufacturers` VALUES (778, '7LN', 'JETWORKS PERFORMANCE', 'jetworks-performance');
INSERT INTO `manufacturers` VALUES (779, '7OT', 'MOTUL', 'motul');
INSERT INTO `manufacturers` VALUES (780, '7QS', 'PIAA', 'piaa');
INSERT INTO `manufacturers` VALUES (781, '7S6', 'RIGID INDUSTRIES', 'rigid-industries');
INSERT INTO `manufacturers` VALUES (782, '7TR', 'RUSSELL', 'russell');
INSERT INTO `manufacturers` VALUES (783, '7YQ', 'VERTEX', 'vertex');
INSERT INTO `manufacturers` VALUES (784, 'BNM', 'B & M', 'b-m');
INSERT INTO `manufacturers` VALUES (785, 'CWL', 'CERTIFIED WIPER LINKAGES', 'certified-wiper-linkages');
INSERT INTO `manufacturers` VALUES (786, 'DPL', 'DUPLICOLOR PAINT', 'duplicolor-paint');
INSERT INTO `manufacturers` VALUES (787, 'EP5', 'EDGE PRODUCTS', 'edge-products');
INSERT INTO `manufacturers` VALUES (788, 'FMI', 'FMSI', 'fmsi');
INSERT INTO `manufacturers` VALUES (789, 'NOS', 'NOS', 'nos');
INSERT INTO `manufacturers` VALUES (790, 'PCD', 'PROFESSIONALS\' CHOICE/AMS', 'professionals-choice-ams');
INSERT INTO `manufacturers` VALUES (791, 'PCE', 'PROFESSIONALS\' CHOICE/BELTS', 'professionals-choice-belts');
INSERT INTO `manufacturers` VALUES (792, 'TFF', 'TUFF SUPPORT', 'tuff-support');
INSERT INTO `manufacturers` VALUES (793, 'V54', 'ADVANCE/CARQUEST DISTRIBUTORS', 'advance-carquest-distributors');
INSERT INTO `manufacturers` VALUES (794, 'V55', 'ADVANCE/CARQUEST WATER PUMPS', 'advance-carquest-water-pumps');
INSERT INTO `manufacturers` VALUES (795, 'WPW', 'WORLDPARTS WIRE', 'worldparts-wire');
INSERT INTO `manufacturers` VALUES (796, '71E', 'RECOCHEM INC.', 'recochem-inc');
INSERT INTO `manufacturers` VALUES (797, 'A97', 'AUTOVALUE', 'autovalue');
INSERT INTO `manufacturers` VALUES (798, 'AUM', 'AUTOMETER', 'autometer');
INSERT INTO `manufacturers` VALUES (799, 'COV', 'COVERCRAFT', 'covercraft');
INSERT INTO `manufacturers` VALUES (800, 'DNY', 'DEC NY CONVERTERS', 'dec-ny-converters');
INSERT INTO `manufacturers` VALUES (801, 'HYK', 'HY-KO PRODUCTS', 'hy-ko-products');
INSERT INTO `manufacturers` VALUES (802, 'PGD', 'PROFORGED', 'proforged');
INSERT INTO `manufacturers` VALUES (803, 'XIQ', 'GIBSON PERFORMANCE EXHAUST', 'gibson-performance-exhaust');
INSERT INTO `manufacturers` VALUES (804, 'AV1', 'AV/BTB OE BOSCH', 'av-btb-oe-bosch');
INSERT INTO `manufacturers` VALUES (805, 'AV2', 'AV/BTB OE DENSO', 'av-btb-oe-denso');
INSERT INTO `manufacturers` VALUES (806, 'AV3', 'AV/BTB OE NTK', 'av-btb-oe-ntk');
INSERT INTO `manufacturers` VALUES (807, 'BAA', 'BCA', 'bca');
INSERT INTO `manufacturers` VALUES (808, 'EHJ', '1800RADIATOR OEQ A/C SERVICE KIT', '1800radiator-oeq-a-c-service-kit');
INSERT INTO `manufacturers` VALUES (809, 'FBT', 'FEBEST', 'febest');
INSERT INTO `manufacturers` VALUES (810, 'HUF', 'HUF', 'huf');
INSERT INTO `manufacturers` VALUES (811, 'MOR', 'MOROSO WIRE TECHNOLOGIES', 'moroso-wire-technologies');
INSERT INTO `manufacturers` VALUES (812, 'OIE', 'IDI WIRE', 'idi-wire');
INSERT INTO `manufacturers` VALUES (813, 'PR4', 'PRO SHIFT', 'pro-shift');
INSERT INTO `manufacturers` VALUES (814, 'S28', 'SERVICE PRO', 'service-pro');
INSERT INTO `manufacturers` VALUES (815, 'TGO', 'TGO4USA', 'tgo4usa');
INSERT INTO `manufacturers` VALUES (816, 'ATX', 'AUTOTEX', 'autotex');
INSERT INTO `manufacturers` VALUES (817, 'AXS', 'AUTO EXTRA CABIN-FUEL-TRANS FILTERS/US', 'auto-extra-cabin-fuel-trans-filters-us');
INSERT INTO `manufacturers` VALUES (818, 'CTS', 'CALTREND CUSTOM TAILORED SEAT COVERS', 'caltrend-custom-tailored-seat-covers');
INSERT INTO `manufacturers` VALUES (819, 'EPG', 'EARLS PLUMBING', 'earls-plumbing');
INSERT INTO `manufacturers` VALUES (820, 'FHB', 'FEDERATED SRT HUB BEARINGS', 'federated-srt-hub-bearings');
INSERT INTO `manufacturers` VALUES (821, 'HOL', 'HOLLEY', 'holley');
INSERT INTO `manufacturers` VALUES (822, 'HOO', 'HOOKER HEADERS', 'hooker-headers');
INSERT INTO `manufacturers` VALUES (823, 'HPD', 'HELLA-PAGID', 'hella-pagid');
INSERT INTO `manufacturers` VALUES (824, 'PXD', 'PARTSOURCE/EXIDE', 'partsource-exide');
INSERT INTO `manufacturers` VALUES (825, 'SCP', 'SPICER', 'spicer');
INSERT INTO `manufacturers` VALUES (826, 'SVL', 'SVL BY DANA', 'svl-by-dana');
INSERT INTO `manufacturers` VALUES (827, 'WEI', 'WEIAND', 'weiand');
INSERT INTO `manufacturers` VALUES (828, 'FPY', 'FLEET PHYSICS', 'fleet-physics');
INSERT INTO `manufacturers` VALUES (829, 'IRB', 'INROBLE INTERNATIONAL', 'inroble-international');
INSERT INTO `manufacturers` VALUES (830, 'OBS', 'O\'REILLY/BRAKEBEST SELECT', 'o-reilly-brakebest-select');
INSERT INTO `manufacturers` VALUES (831, 'OWT', 'IMPORT DIRECT WATER PUMPS/TIMING KITS', 'import-direct-water-pumps-timing-kits');
INSERT INTO `manufacturers` VALUES (832, 'XPB', 'OPTIMA BATTERIES', 'optima-batteries');
INSERT INTO `manufacturers` VALUES (833, 'A5F', 'AFE FILTERS', 'afe-filters');
INSERT INTO `manufacturers` VALUES (834, 'A62', 'AMSOIL', 'amsoil');
INSERT INTO `manufacturers` VALUES (835, 'BYA', 'BODY ARMOR', 'body-armor');
INSERT INTO `manufacturers` VALUES (836, 'DLG', 'DELTA LIGHT', 'delta-light');
INSERT INTO `manufacturers` VALUES (837, 'KAH', 'KLEINN AUTOMOTIVE AIR HORNS', 'kleinn-automotive-air-horns');
INSERT INTO `manufacturers` VALUES (838, 'OIP', 'IMPORT DIRECT CV', 'import-direct-cv');
INSERT INTO `manufacturers` VALUES (839, 'OMS', 'MASTER PRO STEERING', 'master-pro-steering');
INSERT INTO `manufacturers` VALUES (840, 'OPR', 'PRECISION NEW CV', 'precision-new-cv');
INSERT INTO `manufacturers` VALUES (841, 'PTV', 'PARTS MASTER/MEVOTECH US', 'parts-master-mevotech-us');
INSERT INTO `manufacturers` VALUES (842, 'RSD', 'THE RIGHT STUFF', 'the-right-stuff');
INSERT INTO `manufacturers` VALUES (843, 'SP2', 'SP PERFORMANCE', 'sp-performance');
INSERT INTO `manufacturers` VALUES (844, '3DC', '3D CARBON', '3d-carbon');
INSERT INTO `manufacturers` VALUES (845, '8D3', 'AMERICAN CONDENSER', 'american-condenser');
INSERT INTO `manufacturers` VALUES (846, 'ANO', 'ANZO', 'anzo');
INSERT INTO `manufacturers` VALUES (847, 'AT2', 'ADVANTAGE TRUCK ACCESSORIES', 'advantage-truck-accessories');
INSERT INTO `manufacturers` VALUES (848, 'BED', 'BEDSLIDE', 'bedslide');
INSERT INTO `manufacturers` VALUES (849, 'BR2', 'BLACK RHINO', 'black-rhino');
INSERT INTO `manufacturers` VALUES (850, 'CW2', 'CRAY WHEELS', 'cray-wheels');
INSERT INTO `manufacturers` VALUES (851, 'CYW', 'COVENTRY WHEELS', 'coventry-wheels');
INSERT INTO `manufacturers` VALUES (852, 'DHS', 'DOUG\'S HEADERS', 'doug-s-headers');
INSERT INTO `manufacturers` VALUES (853, 'DMT', 'DOMORE TRUCK', 'domore-truck');
INSERT INTO `manufacturers` VALUES (854, 'FGI', 'FAG IMPORT', 'fag-import');
INSERT INTO `manufacturers` VALUES (855, 'FID', 'FIDANZA PERFORMANCE', 'fidanza-performance');
INSERT INTO `manufacturers` VALUES (856, 'GWS', 'GENIUS WHEELS', 'genius-wheels');
INSERT INTO `manufacturers` VALUES (857, 'HMT', 'HUSHMAT', 'hushmat');
INSERT INTO `manufacturers` VALUES (858, 'HPS', 'HEATSHIELD PRODUCTS', 'heatshield-products');
INSERT INTO `manufacturers` VALUES (859, 'JYT', 'JIFFYTITE', 'jiffytite');
INSERT INTO `manufacturers` VALUES (860, 'KCH', 'KC HILITES DRIVING LIGHT SYSTEMS', 'kc-hilites-driving-light-systems');
INSERT INTO `manufacturers` VALUES (861, 'KOD', 'KEVINS OFFROAD', 'kevins-offroad');
INSERT INTO `manufacturers` VALUES (862, 'LAM', 'LAMCO', 'lamco');
INSERT INTO `manufacturers` VALUES (863, 'LWS', 'LUMARAI WHEELS', 'lumarai-wheels');
INSERT INTO `manufacturers` VALUES (864, 'MG2', 'MGP CALIPER COVERS', 'mgp-caliper-covers');
INSERT INTO `manufacturers` VALUES (865, 'MNY', 'MAGNAFLOW NEW YORK CONVERTER', 'magnaflow-new-york-converter');
INSERT INTO `manufacturers` VALUES (866, 'MWS', 'MANDRUS WHEELS', 'mandrus-wheels');
INSERT INTO `manufacturers` VALUES (867, 'NFB', 'N-FAB', 'n-fab');
INSERT INTO `manufacturers` VALUES (868, 'OSW', 'O\'REILLY/MASTER PRO WATER PUMP', 'o-reilly-master-pro-water-pump');
INSERT INTO `manufacturers` VALUES (869, 'OUD', 'OUTLAND', 'outland');
INSERT INTO `manufacturers` VALUES (870, 'PEV', 'PERFECT VIEW', 'perfect-view');
INSERT INTO `manufacturers` VALUES (871, 'PTX', 'PERTRONIX', 'pertronix');
INSERT INTO `manufacturers` VALUES (872, 'PXT', 'PATRIOT EXHAUST', 'patriot-exhaust');
INSERT INTO `manufacturers` VALUES (873, 'RBP', 'RBP PERFORMANCE', 'rbp-performance');
INSERT INTO `manufacturers` VALUES (874, 'REX', 'T-REX', 't-rex');
INSERT INTO `manufacturers` VALUES (875, 'RMP', 'RAMPAGE PRODUCTS', 'rampage-products');
INSERT INTO `manufacturers` VALUES (876, 'RUG', 'RUGGED RIDGE', 'rugged-ridge');
INSERT INTO `manufacturers` VALUES (877, 'RWS', 'REDBOURNE WHEELS', 'redbourne-wheels');
INSERT INTO `manufacturers` VALUES (878, 'SCE', 'SCT PERFORMANCE', 'sct-performance');
INSERT INTO `manufacturers` VALUES (879, 'SCS', 'SUPERCHIPS', 'superchips');
INSERT INTO `manufacturers` VALUES (880, 'SDG', 'USA STANDARD GEAR', 'usa-standard-gear');
INSERT INTO `manufacturers` VALUES (881, 'V56', 'ADVANCE/SYLVANIA LIGHTING', 'advance-sylvania-lighting');
INSERT INTO `manufacturers` VALUES (882, 'V57', 'ADVANCE/TOUGHONE LIGHT', 'advance-toughone-light');
INSERT INTO `manufacturers` VALUES (883, 'V58', 'ADVANCE/DRIVE WORKS LIGHTING', 'advance-drive-works-lighting');
INSERT INTO `manufacturers` VALUES (884, 'V59', 'ADVANCE/MELLING', 'advance-melling');
INSERT INTO `manufacturers` VALUES (885, 'VET', 'VICTOR EQUIPMENT', 'victor-equipment');
INSERT INTO `manufacturers` VALUES (886, 'XC0', 'AIRAID', 'airaid');
INSERT INTO `manufacturers` VALUES (887, 'XDB', 'BEDRUG', 'bedrug');
INSERT INTO `manufacturers` VALUES (888, 'XKG', 'HOTCHKIS PERFORMANCE', 'hotchkis-performance');
INSERT INTO `manufacturers` VALUES (889, 'YND', 'YUKON DIFFERENTIAL', 'yukon-differential');
INSERT INTO `manufacturers` VALUES (890, 'YNG', 'YUKON GEAR', 'yukon-gear');
INSERT INTO `manufacturers` VALUES (891, 'ZEE', 'ZEX', 'zex');
INSERT INTO `manufacturers` VALUES (892, 'AIR', 'AIR LIFT', 'air-lift');
INSERT INTO `manufacturers` VALUES (893, 'ASG', 'ASIMCO GLOBAL', 'asimco-global');
INSERT INTO `manufacturers` VALUES (894, 'CHU', 'CERTIFIED HUB BEARINGS', 'certified-hub-bearings');
INSERT INTO `manufacturers` VALUES (895, 'DOS', 'DIABLO SPORT', 'diablo-sport');
INSERT INTO `manufacturers` VALUES (896, 'DPC', 'DELPHI CHASSIS', 'delphi-chassis');
INSERT INTO `manufacturers` VALUES (897, 'EHB', '1800RADIATOR OEQ RADIATOR & HOSE KIT', '1800radiator-oeq-radiator-hose-kit');
INSERT INTO `manufacturers` VALUES (898, 'EHD', '1800RADIATOR PREMIUM RADIATOR & HOSE KIT', '1800radiator-premium-radiator-hose-kit');
INSERT INTO `manufacturers` VALUES (899, 'HHS', 'HUSLER HEDDERS', 'husler-hedders');
INSERT INTO `manufacturers` VALUES (900, 'LRE', 'LARES CORPORATION', 'lares-corporation');
INSERT INTO `manufacturers` VALUES (901, 'P3W', 'PRW', 'prw');
INSERT INTO `manufacturers` VALUES (902, 'PF2', 'PROFESSIONAL PRODUCTS', 'professional-products');
INSERT INTO `manufacturers` VALUES (903, 'PUL', 'PULSTAR', 'pulstar');
INSERT INTO `manufacturers` VALUES (904, 'PUT', 'PUTCO', 'putco');
INSERT INTO `manufacturers` VALUES (905, 'RLT', 'READY LIFT', 'ready-lift');
INSERT INTO `manufacturers` VALUES (906, 'SSB', 'STAINLESS STEEL BRAKES', 'stainless-steel-brakes');
INSERT INTO `manufacturers` VALUES (907, 'TSF', 'TUFF STUFF PERFORMANCE', 'tuff-stuff-performance');
INSERT INTO `manufacturers` VALUES (908, 'URO', 'URO PARTS', 'uro-parts');
INSERT INTO `manufacturers` VALUES (909, 'WTN', 'WESTIN', 'westin');
INSERT INTO `manufacturers` VALUES (910, 'XBP', 'ACCESS COVER', 'access-cover');
INSERT INTO `manufacturers` VALUES (911, 'XEO', 'COMP CAMS', 'comp-cams');
INSERT INTO `manufacturers` VALUES (912, 'XI6', 'FAST', 'fast');
INSERT INTO `manufacturers` VALUES (913, 'XL8', 'JBA RACING HEADERS', 'jba-racing-headers');
INSERT INTO `manufacturers` VALUES (914, 'XP6', 'NITROUS EXPRESS', 'nitrous-express');
INSERT INTO `manufacturers` VALUES (915, 'XQW', 'PERCY\'S HIGH PERFORMANCE', 'percy-s-high-performance');
INSERT INTO `manufacturers` VALUES (916, 'XQX', 'PACER PERFORMANCE', 'pacer-performance');
INSERT INTO `manufacturers` VALUES (917, 'XS4', 'RACING HEAD SERVICE', 'racing-head-service');
INSERT INTO `manufacturers` VALUES (918, 'XWO', 'TAYLOR BILLET SPECIALTIES', 'taylor-billet-specialties');
INSERT INTO `manufacturers` VALUES (919, 'DAL', 'DERALE', 'derale');
INSERT INTO `manufacturers` VALUES (920, 'XGJ', 'DESIGN ENGINEERING', 'design-engineering');
INSERT INTO `manufacturers` VALUES (921, 'XJG', 'GRANATELLI MOTORSPORTS', 'granatelli-motorsports');
INSERT INTO `manufacturers` VALUES (922, 'A43', 'ALLOY USA', 'alloy-usa');
INSERT INTO `manufacturers` VALUES (923, 'CH2', 'CHAMPION OIL', 'champion-oil');
INSERT INTO `manufacturers` VALUES (924, 'FPR', 'FLEECE PERFORMANCE', 'fleece-performance');
INSERT INTO `manufacturers` VALUES (925, 'HED', 'HEDMAN HEDDERS', 'hedman-hedders');
INSERT INTO `manufacturers` VALUES (926, 'HSK', 'HUSKY LINERS', 'husky-liners');
INSERT INTO `manufacturers` VALUES (927, 'M91', 'MONROE TOTAL SOLUTION BRAKE PADS', 'monroe-total-solution-brake-pads');
INSERT INTO `manufacturers` VALUES (928, 'M92', 'MONROE PROSOLUTION BRAKE PADS', 'monroe-prosolution-brake-pads');
INSERT INTO `manufacturers` VALUES (929, 'M93', 'MONROE SEVERE SOLUTION BRAKE PADS', 'monroe-severe-solution-brake-pads');
INSERT INTO `manufacturers` VALUES (930, 'M94', 'MONROE BRAKE SHOES', 'monroe-brake-shoes');
INSERT INTO `manufacturers` VALUES (931, 'M95', 'MONROE WIRE WEAR SENSORS', 'monroe-wire-wear-sensors');
INSERT INTO `manufacturers` VALUES (932, 'PGR', 'PRECISION GEAR', 'precision-gear');
INSERT INTO `manufacturers` VALUES (933, 'PS5', 'POWER SELECT', 'power-select');
INSERT INTO `manufacturers` VALUES (934, 'SPO', 'US SPEEDO', 'us-speedo');
INSERT INTO `manufacturers` VALUES (935, 'SRO', 'SUPER PRO', 'super-pro');
INSERT INTO `manufacturers` VALUES (936, 'TAY', 'TAYLOR CABLE', 'taylor-cable');
INSERT INTO `manufacturers` VALUES (937, 'TRA', 'TRANS DAPT PERFORMANCE', 'trans-dapt-performance');
INSERT INTO `manufacturers` VALUES (938, 'USD', 'USA STANDARD DIFFERENTIAL', 'usa-standard-differential');
INSERT INTO `manufacturers` VALUES (939, 'UWP', 'WORLDPARTS WATER PUMPS', 'worldparts-water-pumps');
INSERT INTO `manufacturers` VALUES (940, 'WPC', 'WORLDPARTS CONTROL ARMS', 'worldparts-control-arms');
INSERT INTO `manufacturers` VALUES (941, 'XE7', 'CANTON', 'canton');
INSERT INTO `manufacturers` VALUES (942, 'XJO', 'HAMBURGER\'S PERFORMANCE', 'hamburger-s-performance');
INSERT INTO `manufacturers` VALUES (943, 'XOP', 'METRO MOULDED PRODUCTS', 'metro-moulded-products');
INSERT INTO `manufacturers` VALUES (944, 'NYT', 'NY-TREX', 'ny-trex');
INSERT INTO `manufacturers` VALUES (945, 'PGG', 'PROGAUGE', 'progauge');
INSERT INTO `manufacturers` VALUES (946, 'TIT', 'TITAN FUEL TANKS', 'titan-fuel-tanks');
INSERT INTO `manufacturers` VALUES (947, 'XH3', 'EGR', 'egr');
INSERT INTO `manufacturers` VALUES (948, 'A2A', 'AI/PREMIUM VISION', 'ai-premium-vision');
INSERT INTO `manufacturers` VALUES (949, 'A2B', 'AI/DORMAN OE SOLUTIONS', 'ai-dorman-oe-solutions');
INSERT INTO `manufacturers` VALUES (950, 'A2C', 'AI/GMB', 'ai-gmb');
INSERT INTO `manufacturers` VALUES (951, 'A2D', 'AI/DILL TPMS', 'ai-dill-tpms');
INSERT INTO `manufacturers` VALUES (952, 'A2E', 'AI/VDO', 'ai-vdo');
INSERT INTO `manufacturers` VALUES (953, 'A2F', 'AI/EDELMANN', 'ai-edelmann');
INSERT INTO `manufacturers` VALUES (954, 'A2G', 'AI/DAVICO MFG EPA', 'ai-davico-mfg-epa');
INSERT INTO `manufacturers` VALUES (955, 'A2H', 'AI/SACHS', 'ai-sachs');
INSERT INTO `manufacturers` VALUES (956, 'A2I', 'AI/OSC', 'ai-osc');
INSERT INTO `manufacturers` VALUES (957, 'A2J', 'AI/MOTORAD', 'ai-motorad');
INSERT INTO `manufacturers` VALUES (958, 'A2K', 'AI/BWD AUTOMOTIVE', 'ai-bwd-automotive');
INSERT INTO `manufacturers` VALUES (959, 'A2L', 'AI/CARDONE-CARDONE SELECT', 'ai-cardone-cardone-select');
INSERT INTO `manufacturers` VALUES (960, 'A2M', 'AI/FELPRO', 'ai-felpro');
INSERT INTO `manufacturers` VALUES (961, 'A2N', 'AI/CRP-AJUSA', 'ai-crp-ajusa');
INSERT INTO `manufacturers` VALUES (962, 'A2O', 'AI/DEA PRODUCTS', 'ai-dea-products');
INSERT INTO `manufacturers` VALUES (963, 'A2P', 'AI/BANDO', 'ai-bando');
INSERT INTO `manufacturers` VALUES (964, 'A2Q', 'AI/MELLING', 'ai-melling');
INSERT INTO `manufacturers` VALUES (965, 'A2R', 'AI/CRP-REIN', 'ai-crp-rein');
INSERT INTO `manufacturers` VALUES (966, 'ACH', 'ADVANCED CLUTCH', 'advanced-clutch');
INSERT INTO `manufacturers` VALUES (967, 'BCT', 'BIG COUNTRY TRUCK ACCESSORIES', 'big-country-truck-accessories');
INSERT INTO `manufacturers` VALUES (968, 'CR1', 'C&R PERFORMANCE', 'c-r-performance');
INSERT INTO `manufacturers` VALUES (969, 'D31', 'DIAMOND EYE PERFORMANCE', 'diamond-eye-performance');
INSERT INTO `manufacturers` VALUES (970, 'DLR', 'DUALLINER', 'dualliner');
INSERT INTO `manufacturers` VALUES (971, 'F90', 'FIA', 'fia');
INSERT INTO `manufacturers` VALUES (972, 'MC2', 'MOTORAD CARDED', 'motorad-carded');
INSERT INTO `manufacturers` VALUES (973, 'ORF', 'OR-FAB', 'or-fab');
INSERT INTO `manufacturers` VALUES (974, 'PE6', 'PERFORMANCE ACCESSORIES', 'performance-accessories');
INSERT INTO `manufacturers` VALUES (975, 'PRL', 'PRO LIFT SUSPENSION', 'pro-lift-suspension');
INSERT INTO `manufacturers` VALUES (976, 'PSS', 'PRORYDE SUSPENSION SYSTEMS', 'proryde-suspension-systems');
INSERT INTO `manufacturers` VALUES (977, 'QMA', 'CARQUEST/MASS AIR FLOW SENSORS', 'carquest-mass-air-flow-sensors');
INSERT INTO `manufacturers` VALUES (978, 'REV', 'REVTEK', 'revtek');
INSERT INTO `manufacturers` VALUES (979, 'SP5', 'SPC PERFORMANCE', 'spc-performance');
INSERT INTO `manufacturers` VALUES (980, 'TCV', 'TRUCK COVERS USA', 'truck-covers-usa');
INSERT INTO `manufacturers` VALUES (981, 'TSW', 'TSW WHEELS', 'tsw-wheels');
INSERT INTO `manufacturers` VALUES (982, 'TU3', 'TUFF COUNTRY', 'tuff-country');
INSERT INTO `manufacturers` VALUES (983, 'V60', 'ADVANCE/RICHMOND GEAR', 'advance-richmond-gear');
INSERT INTO `manufacturers` VALUES (984, 'XC5', 'BULLDOG WINCH', 'bulldog-winch');
INSERT INTO `manufacturers` VALUES (985, 'XI8', 'FABTECH', 'fabtech');
INSERT INTO `manufacturers` VALUES (986, 'XX4', 'UNDERCOVER', 'undercover');
INSERT INTO `manufacturers` VALUES (987, 'AA3', 'AUTOPRIDE/ANCHOR', 'autopride-anchor');
INSERT INTO `manufacturers` VALUES (988, 'AA4', 'AUTOPRIDE/ CARDONE', 'autopride-cardone');
INSERT INTO `manufacturers` VALUES (989, 'AA5', 'AUTOPRIDE/ CARDONE SELECT', 'autopride-cardone-select');
INSERT INTO `manufacturers` VALUES (990, 'DY1', 'DYNAMIC AUTOMOTIVE', 'dynamic-automotive');
INSERT INTO `manufacturers` VALUES (991, 'GPN', 'GOODYEAR FLOOR PROTECTION', 'goodyear-floor-protection');
INSERT INTO `manufacturers` VALUES (992, 'HWK', 'HAWK PERFORMANCE', 'hawk-performance');
INSERT INTO `manufacturers` VALUES (993, 'KMS', 'IAP/KUHLTEK MOTORWERKS', 'iap-kuhltek-motorwerks');
INSERT INTO `manufacturers` VALUES (994, 'KYR', 'KOYORAD', 'koyorad');
INSERT INTO `manufacturers` VALUES (995, 'OEB', 'OE BRAND', 'oe-brand');
INSERT INTO `manufacturers` VALUES (996, 'XE1', 'CHAMPION COOLING SYSTEMS', 'champion-cooling-systems');
INSERT INTO `manufacturers` VALUES (997, 'XIP', 'GO RHINO', 'go-rhino');
INSERT INTO `manufacturers` VALUES (998, 'XV9', 'SKYJACKER', 'skyjacker');
INSERT INTO `manufacturers` VALUES (999, 'ARH', 'AMP RESEARCH', 'amp-research');
INSERT INTO `manufacturers` VALUES (1000, 'C2F', 'CRAFTED2FIT CUSTOM CAR COVERS', 'crafted2fit-custom-car-covers');
INSERT INTO `manufacturers` VALUES (1001, 'CAB', 'CARBAND', 'carband');
INSERT INTO `manufacturers` VALUES (1002, 'CCV', 'CANINE COVERS', 'canine-covers');
INSERT INTO `manufacturers` VALUES (1003, 'CMK', 'CUSTOM FRONT MASK', 'custom-front-mask');
INSERT INTO `manufacturers` VALUES (1004, 'CMM', 'CUSTOM MINI MASK', 'custom-mini-mask');
INSERT INTO `manufacturers` VALUES (1005, 'CSM', 'CUSTOM HOOD PROTECTORS', 'custom-hood-protectors');
INSERT INTO `manufacturers` VALUES (1006, 'CT1', 'CTR (DELETE V24A01R1)', 'ctr-delete-v24a01r1');
INSERT INTO `manufacturers` VALUES (1007, 'CUI', 'CAPCO USA,INC.', 'capco-usa-inc');
INSERT INTO `manufacturers` VALUES (1008, 'DBA', 'DISC BRAKES AUSTRALIA', 'disc-brakes-australia');
INSERT INTO `manufacturers` VALUES (1009, 'DEC', 'DECELABRANDS', 'decelabrands');
INSERT INTO `manufacturers` VALUES (1010, 'ICI', 'INNOVATIVE CREATIONS INC.', 'innovative-creations-inc');
INSERT INTO `manufacturers` VALUES (1011, 'LED', 'LTD EDITION', 'ltd-edition');
INSERT INTO `manufacturers` VALUES (1012, 'MA2', 'MAXLINER', 'maxliner');
INSERT INTO `manufacturers` VALUES (1013, 'MUD', 'MURRAY CONDENSER', 'murray-condenser');
INSERT INTO `manufacturers` VALUES (1014, 'OPM', 'ONE PIECE FRONT MASK', 'one-piece-front-mask');
INSERT INTO `manufacturers` VALUES (1015, 'PF5', 'PREMIER FLOORMATS', 'premier-floormats');
INSERT INTO `manufacturers` VALUES (1016, 'PKP', 'POCKET PODS', 'pocket-pods');
INSERT INTO `manufacturers` VALUES (1017, 'RX1', 'RAIN-X CANADA WEATHERBEATER', 'rain-x-canada-weatherbeater');
INSERT INTO `manufacturers` VALUES (1018, 'RX2', 'RAIN X WEATHERBEATER PRO', 'rain-x-weatherbeater-pro');
INSERT INTO `manufacturers` VALUES (1019, 'RX3', 'RAIN X FUSION', 'rain-x-fusion');
INSERT INTO `manufacturers` VALUES (1020, 'SMT', 'SUEDEMAT', 'suedemat');
INSERT INTO `manufacturers` VALUES (1021, 'SSR', 'SEAT SAVER', 'seat-saver');
INSERT INTO `manufacturers` VALUES (1022, 'UMT', 'ULTIMAT', 'ultimat');
INSERT INTO `manufacturers` VALUES (1023, 'UVS', 'UVS100 HEAT SHIELD', 'uvs100-heat-shield');
INSERT INTO `manufacturers` VALUES (1024, 'VLM', 'VELOURMAT', 'velourmat');
INSERT INTO `manufacturers` VALUES (1025, 'WPA', 'WORLDPARTS CV AXLES', 'worldparts-cv-axles');
INSERT INTO `manufacturers` VALUES (1026, 'XGZ', 'ENERGY SUSPENSION', 'energy-suspension');
INSERT INTO `manufacturers` VALUES (1027, '8CK', 'PAPCO', 'papco');
INSERT INTO `manufacturers` VALUES (1028, 'BES', 'BENDIX STOP', 'bendix-stop');
INSERT INTO `manufacturers` VALUES (1029, 'CGS', 'CGS', 'cgs');
INSERT INTO `manufacturers` VALUES (1030, 'COG', 'COLGAN CUSTOM', 'colgan-custom');
INSERT INTO `manufacturers` VALUES (1031, 'DH1', 'DEFLECTOR PROTECTOR HOOD SHIELD', 'deflector-protector-hood-shield');
INSERT INTO `manufacturers` VALUES (1032, 'FL1', 'FLO PRO', 'flo-pro');
INSERT INTO `manufacturers` VALUES (1033, 'OLG', 'ORACLE LIGHTING', 'oracle-lighting');
INSERT INTO `manufacturers` VALUES (1034, 'OPP', 'O\'REILLY/MASTER PRO POWER STEERING', 'o-reilly-master-pro-power-steering');
INSERT INTO `manufacturers` VALUES (1035, 'ORP', 'OUTLAW RACING PRODUCTS', 'outlaw-racing-products');
INSERT INTO `manufacturers` VALUES (1036, 'PP7', 'PIT POSSE', 'pit-posse');
INSERT INTO `manufacturers` VALUES (1037, 'PT1', 'POWERTRAX', 'powertrax');
INSERT INTO `manufacturers` VALUES (1038, 'RUN', 'RUN SILENT', 'run-silent');
INSERT INTO `manufacturers` VALUES (1039, 'S20', 'SEAT GLOVES', 'seat-gloves');
INSERT INTO `manufacturers` VALUES (1040, 'SC2', 'SERVICE CHAMP BELTS AND TENSIONERS', 'service-champ-belts-and-tensioners');
INSERT INTO `manufacturers` VALUES (1041, 'STC', 'STEELCRAFT', 'steelcraft');
INSERT INTO `manufacturers` VALUES (1042, 'TEN', 'TEN FACTORY', 'ten-factory');
INSERT INTO `manufacturers` VALUES (1043, 'TXX', 'TRAXDA', 'traxda');
INSERT INTO `manufacturers` VALUES (1044, 'V61', 'ADVANCE/DIEHARD PLATINUM AGM', 'advance-diehard-platinum-agm');
INSERT INTO `manufacturers` VALUES (1045, 'VRA', 'VR ADVANTAGE', 'vr-advantage');
INSERT INTO `manufacturers` VALUES (1046, 'WYS', 'WYSETOW', 'wysetow');
INSERT INTO `manufacturers` VALUES (1047, 'XFG', 'CARGO AREA LINER', 'cargo-area-liner');
INSERT INTO `manufacturers` VALUES (1048, 'ZYN', 'INA CA', 'ina-ca');
INSERT INTO `manufacturers` VALUES (1049, '73D', 'ENGINETECH, INC.', 'enginetech-inc');
INSERT INTO `manufacturers` VALUES (1050, 'ADC', 'ADDCO', 'addco');
INSERT INTO `manufacturers` VALUES (1051, 'GRO', 'GROTE', 'grote');
INSERT INTO `manufacturers` VALUES (1052, 'H14', 'HENGST', 'hengst');
INSERT INTO `manufacturers` VALUES (1053, 'M09', 'MANDO', 'mando');
INSERT INTO `manufacturers` VALUES (1054, 'M13', 'MAPCO USA', 'mapco-usa');
INSERT INTO `manufacturers` VALUES (1055, 'OPA', 'PRECISION CONTROL ARMS', 'precision-control-arms');
INSERT INTO `manufacturers` VALUES (1056, 'OWE', 'OWENS PRODUCTS', 'owens-products');
INSERT INTO `manufacturers` VALUES (1057, 'PAI', 'PAINLESS WIRING', 'painless-wiring');
INSERT INTO `manufacturers` VALUES (1058, 'PNL', 'POP & LOCK', 'pop-lock');
INSERT INTO `manufacturers` VALUES (1059, 'POM', 'POWERMASTER BATTERY', 'powermaster-battery');
INSERT INTO `manufacturers` VALUES (1060, 'SPP', 'SPEED-PRO', 'speed-pro');
INSERT INTO `manufacturers` VALUES (1061, 'SSS', 'SUPER LIFT', 'super-lift');
INSERT INTO `manufacturers` VALUES (1062, 'STS', 'STREET SCENE', 'street-scene');
INSERT INTO `manufacturers` VALUES (1063, 'TS1', 'TRUSTAR/ AUTOPARTSOURCE', 'trustar-autopartsource');
INSERT INTO `manufacturers` VALUES (1064, 'TS2', 'TRUSTAR/UNDERCAR EXPRESS', 'trustar-undercar-express');
INSERT INTO `manufacturers` VALUES (1065, 'XCV', 'BBK PERFORMANCE PARTS', 'bbk-performance-parts');
INSERT INTO `manufacturers` VALUES (1066, 'XHG', 'EBC BRAKES', 'ebc-brakes');
INSERT INTO `manufacturers` VALUES (1067, 'XI3', 'FLUIDAMPR', 'fluidampr');
INSERT INTO `manufacturers` VALUES (1068, 'XII', 'GRANT', 'grant');
INSERT INTO `manufacturers` VALUES (1069, 'XKV', 'INJEN', 'injen');
INSERT INTO `manufacturers` VALUES (1070, 'XMW', 'LUNATI', 'lunati');
INSERT INTO `manufacturers` VALUES (1071, 'XR7', 'PHOENIX FRICTION PRODUCTS', 'phoenix-friction-products');
INSERT INTO `manufacturers` VALUES (1072, 'Z1C', 'NSK BEARINGS', 'nsk-bearings');
INSERT INTO `manufacturers` VALUES (1073, 'Z2W', 'RANSHU A/C', 'ranshu-a-c');
INSERT INTO `manufacturers` VALUES (1074, 'HUD', 'HUCO', 'huco');
INSERT INTO `manufacturers` VALUES (1075, 'PR6', 'DURAGO EP COATED', 'durago-ep-coated');
INSERT INTO `manufacturers` VALUES (1076, 'PT2', 'PT AUTO WAREHOUSE', 'pt-auto-warehouse');
INSERT INTO `manufacturers` VALUES (1077, 'HBM', 'HATCHIE BOTTOM', 'hatchie-bottom');
INSERT INTO `manufacturers` VALUES (1078, 'PWH', 'PRECISION WHEEL HUB ASSEMBLIES', 'precision-wheel-hub-assemblies');
INSERT INTO `manufacturers` VALUES (1079, 'OPL', 'CANADIAN TIRE OPTIMA BATTERIES', 'canadian-tire-optima-batteries');
INSERT INTO `manufacturers` VALUES (1080, 'PQT', 'PERFORMANCE QUOTIENT', 'performance-quotient');
INSERT INTO `manufacturers` VALUES (1081, 'RTR', 'RETRAX', 'retrax');
INSERT INTO `manufacturers` VALUES (1082, 'V63', 'ADVANCE/CARQUEST WEAREVER PLATINUM PRO', 'advance-carquest-wearever-platinum-pro');
INSERT INTO `manufacturers` VALUES (1083, 'V64', 'ADVANCE/WEAREVER FRONTLINE ROTORS', 'advance-wearever-frontline-rotors');
INSERT INTO `manufacturers` VALUES (1084, 'BOE', 'UNI-SELECT/BOSCH OE REPLACEMENT PARTS', 'uni-select-bosch-oe-replacement-parts');
INSERT INTO `manufacturers` VALUES (1085, 'CAG', 'CARGO GLIDE', 'cargo-glide');
INSERT INTO `manufacturers` VALUES (1086, 'ENI', 'ENI USA', 'eni-usa');
INSERT INTO `manufacturers` VALUES (1087, 'PVP', 'PRESTOLITE VALUE PLUS', 'prestolite-value-plus');
INSERT INTO `manufacturers` VALUES (1088, 'PW1', 'PRONTO/WINHERE', 'pronto-winhere');
INSERT INTO `manufacturers` VALUES (1089, 'PW2', 'PROFESSIONALS\' CHOICE/WINHERE', 'professionals-choice-winhere');
INSERT INTO `manufacturers` VALUES (1090, 'B2C', 'FBS', 'fbs');
INSERT INTO `manufacturers` VALUES (1091, 'BSK', 'BRISK', 'brisk');
INSERT INTO `manufacturers` VALUES (1092, 'FEV', 'EVS FRICTION', 'evs-friction');
INSERT INTO `manufacturers` VALUES (1093, 'LND', 'LILAND', 'liland');
INSERT INTO `manufacturers` VALUES (1094, 'MC1', 'MAGNACHARGE BATTERY', 'magnacharge-battery');
INSERT INTO `manufacturers` VALUES (1095, 'MC5', 'MAGNACHARGE HIGH PERFORMANCE AGM', 'magnacharge-high-performance-agm');
INSERT INTO `manufacturers` VALUES (1096, 'MC7', 'MASTER SELECT', 'master-select');
INSERT INTO `manufacturers` VALUES (1097, 'MOI', 'MEVOTECH ORIGINAL GRADE INTERNATIONAL', 'mevotech-original-grade-international');
INSERT INTO `manufacturers` VALUES (1098, 'RB1', 'RINGBROTHERS', 'ringbrothers');
INSERT INTO `manufacturers` VALUES (1099, 'SSP', 'PROFESSIONAL GRADE', 'professional-grade');
INSERT INTO `manufacturers` VALUES (1100, 'VPC', 'VPOC', 'vpoc');
INSERT INTO `manufacturers` VALUES (1101, 'DMC', 'DAVICO MFG CARB', 'davico-mfg-carb');
INSERT INTO `manufacturers` VALUES (1102, 'HOR', 'HANSON OFFROAD', 'hanson-offroad');
INSERT INTO `manufacturers` VALUES (1103, 'KSP', 'KSPORT', 'ksport');
INSERT INTO `manufacturers` VALUES (1104, 'MTL', 'MICKEY THOMPSON METAL', 'mickey-thompson-metal');
INSERT INTO `manufacturers` VALUES (1105, 'MUB', 'MURRAY BLOWER MOTORS', 'murray-blower-motors');
INSERT INTO `manufacturers` VALUES (1106, 'MUF', 'MURRAY RADIATOR FANS', 'murray-radiator-fans');
INSERT INTO `manufacturers` VALUES (1107, 'OIB', 'IMPORT DIRECT BLOWER MOTORS', 'import-direct-blower-motors');
INSERT INTO `manufacturers` VALUES (1108, 'OIF', 'IMPORT DIRECT RADIATOR FANS', 'import-direct-radiator-fans');
INSERT INTO `manufacturers` VALUES (1109, 'OMM', 'ORIGINAL ENGINE MANAGEMENT MEXICO', 'original-engine-management-mexico');
INSERT INTO `manufacturers` VALUES (1110, 'SAW', 'SWAY A WAY', 'sway-a-way');
INSERT INTO `manufacturers` VALUES (1111, 'TSS', 'TIMBREN SES', 'timbren-ses');
INSERT INTO `manufacturers` VALUES (1112, 'V65', 'ADVANCE/AUTOCRAFT WIPERS', 'advance-autocraft-wipers');
INSERT INTO `manufacturers` VALUES (1113, 'V66', 'ADVANCE/TRICO ONYX', 'advance-trico-onyx');
INSERT INTO `manufacturers` VALUES (1114, 'V67', 'ADVANCE/TRICO INSTINCT', 'advance-trico-instinct');
INSERT INTO `manufacturers` VALUES (1115, 'V70', 'ADVANCE/BWD AUTOMOTIVE', 'advance-bwd-automotive');
INSERT INTO `manufacturers` VALUES (1116, 'V71', 'ADVANCE/INTERMOTOR', 'advance-intermotor');
INSERT INTO `manufacturers` VALUES (1117, 'V72', 'ADVANCE/CARQUEST CARBURETOR PARTS', 'advance-carquest-carburetor-parts');
INSERT INTO `manufacturers` VALUES (1118, 'VD3', 'VOODOO 13', 'voodoo-13');
INSERT INTO `manufacturers` VALUES (1119, 'WBC', 'WORLDPARTS BRAKE CABLES', 'worldparts-brake-cables');
INSERT INTO `manufacturers` VALUES (1120, 'A1U', 'CARDONE ULTRA', 'cardone-ultra');
INSERT INTO `manufacturers` VALUES (1121, 'DUH', 'DU-HA', 'du-ha');
INSERT INTO `manufacturers` VALUES (1122, 'MXT', 'MAXTRAC', 'maxtrac');
INSERT INTO `manufacturers` VALUES (1123, 'OEE', 'OEM EXHAUST', 'oem-exhaust');
INSERT INTO `manufacturers` VALUES (1124, 'OIH', 'O\'REILLY/IMPORT DIRECT COMPRESSORS', 'o-reilly-import-direct-compressors');
INSERT INTO `manufacturers` VALUES (1125, 'OMA', 'O\'REILLY/MURRAY COMPRESSORS', 'o-reilly-murray-compressors');
INSERT INTO `manufacturers` VALUES (1126, 'OIK', 'O\'REILLY/IMPORT DIRECT FAN CLUTCHES', 'o-reilly-import-direct-fan-clutches');
INSERT INTO `manufacturers` VALUES (1127, 'PPU', 'PRO-SERIES OE PLUS BRAKE PADS', 'pro-series-oe-plus-brake-pads');
INSERT INTO `manufacturers` VALUES (1128, 'PW3', 'PRONTO/WINHERE ULTRA HC PREMIUM ROTORS', 'pronto-winhere-ultra-hc-premium-rotors');
INSERT INTO `manufacturers` VALUES (1129, 'TAP', 'TRANSIT', 'transit');
INSERT INTO `manufacturers` VALUES (1130, 'OMC', 'O\'REILLY/MURRAY FAN CLUTCHES', 'o-reilly-murray-fan-clutches');
INSERT INTO `manufacturers` VALUES (1131, 'ULG', 'ULTIMA SELECT WIRES', 'ultima-select-wires');
INSERT INTO `manufacturers` VALUES (1132, 'WRR', 'WARRIOR PRODUCTS', 'warrior-products');
INSERT INTO `manufacturers` VALUES (1133, 'ATM', 'MOTORKING', 'motorking');
INSERT INTO `manufacturers` VALUES (1134, 'FAB', 'FAB FOURS', 'fab-fours');
INSERT INTO `manufacturers` VALUES (1135, 'IDC', 'IMPORT DIRECT COMPRESSORS', 'import-direct-compressors');
INSERT INTO `manufacturers` VALUES (1136, 'TIX', 'TITAN BY EXIDE', 'titan-by-exide');
INSERT INTO `manufacturers` VALUES (1137, 'UFM', 'ULTRALIFE BY FRICTIONMASTER', 'ultralife-by-frictionmaster');
INSERT INTO `manufacturers` VALUES (1138, 'V68', 'ADVANCE/WEAREVER ONE STOP', 'advance-wearever-one-stop');
INSERT INTO `manufacturers` VALUES (1139, 'WPB', 'WORLDPARTS BRAKE PAD & ROTOR-USA', 'worldparts-brake-pad-rotor-usa');
INSERT INTO `manufacturers` VALUES (1140, 'C88', 'COOLING DEPOT/PREMIUM RADIATORS', 'cooling-depot-premium-radiators');
INSERT INTO `manufacturers` VALUES (1141, 'ESB', 'EVERSTART BATTERIES', 'everstart-batteries');
INSERT INTO `manufacturers` VALUES (1142, 'IMM', 'IMC MFG NUMBER CATALOG', 'imc-mfg-number-catalog');
INSERT INTO `manufacturers` VALUES (1143, 'FUL', 'FULTON', 'fulton');
INSERT INTO `manufacturers` VALUES (1144, 'RX4', 'RAIN-X CANADA MULTI-FIT REAR', 'rain-x-canada-multi-fit-rear');
INSERT INTO `manufacturers` VALUES (1145, 'V62', 'ADVANCE/AUTOCRAFT MARINE', 'advance-autocraft-marine');
INSERT INTO `manufacturers` VALUES (1146, 'K26', 'KRAMER', 'kramer');
INSERT INTO `manufacturers` VALUES (1147, 'MC3', 'MAGNACHARGE AGM DEEP CYCLE', 'magnacharge-agm-deep-cycle');
INSERT INTO `manufacturers` VALUES (1148, 'MUG', 'MURRAY COMPRESSORS', 'murray-compressors');
INSERT INTO `manufacturers` VALUES (1149, 'MWB', 'MICHELIN WIPERS', 'michelin-wipers');
INSERT INTO `manufacturers` VALUES (1150, 'OMI', 'O\'REILLY/MASTER PRO IGNITION', 'o-reilly-master-pro-ignition');
INSERT INTO `manufacturers` VALUES (1151, 'PIP', 'PRONTO/DURAGO EP COATED', 'pronto-durago-ep-coated');
INSERT INTO `manufacturers` VALUES (1152, 'PT3', 'PARTS MASTER/MPA', 'parts-master-mpa');
INSERT INTO `manufacturers` VALUES (1153, 'PUP', 'PURE PRO', 'pure-pro');
INSERT INTO `manufacturers` VALUES (1154, 'SRT', 'SUPER TECH', 'super-tech');
INSERT INTO `manufacturers` VALUES (1155, 'SUG', 'SUNSONG NORTH AMERICA', 'sunsong-north-america');
INSERT INTO `manufacturers` VALUES (1156, 'BFT', 'BROADFEET', 'broadfeet');
INSERT INTO `manufacturers` VALUES (1157, 'CT0', 'ADVANCE RADIATOR', 'advance-radiator');
INSERT INTO `manufacturers` VALUES (1158, 'CT2', 'ADVANCE HEATER CORE', 'advance-heater-core');
INSERT INTO `manufacturers` VALUES (1159, 'CT4', 'ADVANCE INTERCOOLER', 'advance-intercooler');
INSERT INTO `manufacturers` VALUES (1160, 'LOR', 'LORDCO ROTORS', 'lordco-rotors');
INSERT INTO `manufacturers` VALUES (1161, 'LRL', 'LESTER ROTATING ELECTRICAL PARTS', 'lester-rotating-electrical-parts');
INSERT INTO `manufacturers` VALUES (1162, 'UIM', 'UNI-SELECT/MOTORAD', 'uni-select-motorad');
INSERT INTO `manufacturers` VALUES (1163, 'UMF', 'UNI-SELECT/ MOTORAD-FAIL SAFE', 'uni-select-motorad-fail-safe');
INSERT INTO `manufacturers` VALUES (1164, 'UMH', 'UNI-SELECT/ MOTORAD-HIGH FLOW', 'uni-select-motorad-high-flow');
INSERT INTO `manufacturers` VALUES (1165, 'UMS', 'UNI-SELECT/ MOTORAD-STANDARD', 'uni-select-motorad-standard');
INSERT INTO `manufacturers` VALUES (1166, 'EGB', 'EAGLE BHP', 'eagle-bhp');
INSERT INTO `manufacturers` VALUES (1167, 'RPO', 'RAPTOR', 'raptor');
INSERT INTO `manufacturers` VALUES (1168, 'RSP', 'RACE SPORT', 'race-sport');
INSERT INTO `manufacturers` VALUES (1169, 'USF', 'US AUTOFORCE', 'us-autoforce');
INSERT INTO `manufacturers` VALUES (1170, 'VAT', 'VALVOLINE (TBC)', 'valvoline-tbc');
INSERT INTO `manufacturers` VALUES (1171, 'WHA', 'WORLDPARTS HUB ASSEMBLIES', 'worldparts-hub-assemblies');
INSERT INTO `manufacturers` VALUES (1172, 'PIG', 'PIERBURG BY HELLA', 'pierburg-by-hella');
INSERT INTO `manufacturers` VALUES (1173, 'Q0F', 'CARQUEST PREMIUM FILTERS', 'carquest-premium-filters');
INSERT INTO `manufacturers` VALUES (1174, 'Q1F', 'CARQUEST STANDARD FILTERS', 'carquest-standard-filters');
INSERT INTO `manufacturers` VALUES (1175, 'V80', 'ADVANCE/CARQUEST PREMIUM FILTERS', 'advance-carquest-premium-filters');
INSERT INTO `manufacturers` VALUES (1176, 'V81', 'ADVANCE/CARQUEST STANDARD FILTERS', 'advance-carquest-standard-filters');
INSERT INTO `manufacturers` VALUES (1177, 'W42', 'MIGHTY ARCHIVE', 'mighty-archive');
INSERT INTO `manufacturers` VALUES (1178, 'AI0', 'AUTOPART INTERNATIONAL/MOTORAD-STANDARD', 'autopart-international-motorad-standard');
INSERT INTO `manufacturers` VALUES (1179, 'AI1', 'AUTOPART INTERNATIONAL/MOTORAD-HIGH FLOW', 'autopart-international-motorad-high-flow');
INSERT INTO `manufacturers` VALUES (1180, 'AI2', 'AUTOPART INTERNATIONAL/MOTORAD-FAIL SAFE', 'autopart-international-motorad-fail-safe');
INSERT INTO `manufacturers` VALUES (1181, 'LIM', 'LORDCO IMPORTS', 'lordco-imports');
INSERT INTO `manufacturers` VALUES (1182, 'ODA', 'ODM AXLES', 'odm-axles');
INSERT INTO `manufacturers` VALUES (1183, 'OPV', 'OPTEVE BRAKES', 'opteve-brakes');
INSERT INTO `manufacturers` VALUES (1184, 'PPR', 'PARTS PLAYER', 'parts-player');
INSERT INTO `manufacturers` VALUES (1185, 'QDW', 'CARQUEST/DRIVEWORKS', 'carquest-driveworks');
INSERT INTO `manufacturers` VALUES (1186, 'V82', 'ADVANCE/CARQUEST ELECTRICAL', 'advance-carquest-electrical');
INSERT INTO `manufacturers` VALUES (1187, 'AWE', 'AUTO PLUS/WILSON ELECTRIC', 'auto-plus-wilson-electric');
INSERT INTO `manufacturers` VALUES (1188, 'BOM', 'BOOM MAT', 'boom-mat');
INSERT INTO `manufacturers` VALUES (1189, 'DID', 'DIODE DYNAMICS', 'diode-dynamics');
INSERT INTO `manufacturers` VALUES (1190, 'DME', 'DAVICO MFG EXHAUST', 'davico-mfg-exhaust');
INSERT INTO `manufacturers` VALUES (1191, 'IVD', 'ICON VEHICLE DYNAMICS', 'icon-vehicle-dynamics');
INSERT INTO `manufacturers` VALUES (1192, 'CWB', 'CHAMPION WIPERS', 'champion-wipers');
INSERT INTO `manufacturers` VALUES (1193, 'MBB', 'MAGNA POWER BRAND', 'magna-power-brand');
INSERT INTO `manufacturers` VALUES (1194, 'SFN', 'SILENCER FRICTION', 'silencer-friction');
INSERT INTO `manufacturers` VALUES (1195, 'BFM', 'BENDIX FLEET METLOK', 'bendix-fleet-metlok');
INSERT INTO `manufacturers` VALUES (1196, 'BPF', 'BENDIX PREMIUM COPPER FREE', 'bendix-premium-copper-free');
INSERT INTO `manufacturers` VALUES (1197, 'BXP', 'STOP BY BENDIX', 'stop-by-bendix');
INSERT INTO `manufacturers` VALUES (1198, 'FPB', 'FVP PLATINUM BATTERIES', 'fvp-platinum-batteries');
INSERT INTO `manufacturers` VALUES (1199, 'FVB', 'FVP VOLTEDGE BATTERIES', 'fvp-voltedge-batteries');
INSERT INTO `manufacturers` VALUES (1200, 'OEX', 'OETECHNOLOGY SERIES', 'oetechnology-series');
INSERT INTO `manufacturers` VALUES (1201, 'Q61', 'CARQUEST/HD WATER PUMPS', 'carquest-hd-water-pumps');
INSERT INTO `manufacturers` VALUES (1202, 'V83', 'ADVANCE/CARQUEST WEAREVER', 'advance-carquest-wearever');
INSERT INTO `manufacturers` VALUES (1203, 'WLE', 'WHITELINE', 'whiteline');
INSERT INTO `manufacturers` VALUES (1204, 'AP3', 'ADVANCED PRODUCTS', 'advanced-products');
INSERT INTO `manufacturers` VALUES (1205, 'BB1', 'BOSCH BATTERIES USA', 'bosch-batteries-usa');
INSERT INTO `manufacturers` VALUES (1206, 'BB2', 'BOSCH BATTERIES CANADA', 'bosch-batteries-canada');
INSERT INTO `manufacturers` VALUES (1207, 'BKM', 'BLACK MEDALLION', 'black-medallion');
INSERT INTO `manufacturers` VALUES (1208, 'BPD', 'BENDIX PREMIUM DRUM AND ROTOR', 'bendix-premium-drum-and-rotor');
INSERT INTO `manufacturers` VALUES (1209, 'BPX', 'BENDIX PREMIUM BRAKE SHOES', 'bendix-premium-brake-shoes');
INSERT INTO `manufacturers` VALUES (1210, 'OFS', 'MASTER PRO FUEL PUMPS', 'master-pro-fuel-pumps');
INSERT INTO `manufacturers` VALUES (1211, 'RTM', 'ROTOMASTER', 'rotomaster');
INSERT INTO `manufacturers` VALUES (1212, 'IBB', 'IMPALA BOBS', 'impala-bobs');
INSERT INTO `manufacturers` VALUES (1213, 'MBR', 'MBRP EXHAUST', 'mbrp-exhaust');
INSERT INTO `manufacturers` VALUES (1214, 'NMB', 'NO MORE BREAKING', 'no-more-breaking');
INSERT INTO `manufacturers` VALUES (1215, 'OCM', 'MASTER PRO CONTROL MODULES', 'master-pro-control-modules');
INSERT INTO `manufacturers` VALUES (1216, 'QC2', 'CARQUEST/CARDONE SELECT', 'carquest-cardone-select');
INSERT INTO `manufacturers` VALUES (1217, 'QC3', 'CARQUEST/CARDONE SERVICE PLUS', 'carquest-cardone-service-plus');
INSERT INTO `manufacturers` VALUES (1218, 'QC4', 'CARQUEST/WEAREVER BRAKE CALIPER', 'carquest-wearever-brake-caliper');
INSERT INTO `manufacturers` VALUES (1219, 'QC5', 'CARQUEST/WEAREVER POWER BRAKE BOOSTERS', 'carquest-wearever-power-brake-boosters');
INSERT INTO `manufacturers` VALUES (1220, 'QC6', 'CARQUEST WIPER MOTORS', 'carquest-wiper-motors');
INSERT INTO `manufacturers` VALUES (1221, 'QFL', 'CARQUEST/WEAREVER FRONTLINE', 'carquest-wearever-frontline');
INSERT INTO `manufacturers` VALUES (1222, 'QOS', 'CARQUEST/WEAREVER ONE STOP', 'carquest-wearever-one-stop');
INSERT INTO `manufacturers` VALUES (1223, 'QWG', 'CARQUEST WEAREVER GOLD', 'carquest-wearever-gold');
INSERT INTO `manufacturers` VALUES (1224, 'QWP', 'CARQUEST/WEAREVER PLATINUM PRO', 'carquest-wearever-platinum-pro');
INSERT INTO `manufacturers` VALUES (1225, 'QWS', 'CARQUEST/WEAREVER SILVER', 'carquest-wearever-silver');
INSERT INTO `manufacturers` VALUES (1226, 'UCR', 'UNIVERSAL CAR REMOTE', 'universal-car-remote');
INSERT INTO `manufacturers` VALUES (1227, 'V90', 'ADVANCE/CARDONE', 'advance-cardone');
INSERT INTO `manufacturers` VALUES (1228, 'V91', 'ADVANCE/CARDONE SELECT', 'advance-cardone-select');
INSERT INTO `manufacturers` VALUES (1229, 'V92', 'ADVANCE/CARDONE SERVICE PLUS', 'advance-cardone-service-plus');
INSERT INTO `manufacturers` VALUES (1230, 'V93', 'ADVANCE/WEAREVER BRAKE CALIPER', 'advance-wearever-brake-caliper');
INSERT INTO `manufacturers` VALUES (1231, 'V94', 'ADVANCE/WEAREVER POWER BRAKE BOOSTERS', 'advance-wearever-power-brake-boosters');
INSERT INTO `manufacturers` VALUES (1232, 'BSE', 'BSE', 'bse');
INSERT INTO `manufacturers` VALUES (1233, 'BSX', 'BLUE STREAK ELECTRONICS', 'blue-streak-electronics');
INSERT INTO `manufacturers` VALUES (1234, 'IDH', 'IMPORT DIRECT A/C', 'import-direct-a-c');
INSERT INTO `manufacturers` VALUES (1235, 'MQH', 'QUICKSTEER HUB ASSEMBLIES', 'quicksteer-hub-assemblies');
INSERT INTO `manufacturers` VALUES (1236, 'OCN', 'BRAKEBEST SELECT MASTER CYLINDER', 'brakebest-select-master-cylinder');
INSERT INTO `manufacturers` VALUES (1237, 'QA0', 'CARQUEST/DIEHARD RED', 'carquest-diehard-red');
INSERT INTO `manufacturers` VALUES (1238, 'QA1', 'CARQUEST/AUTOCRAFT FARM & TRUCK', 'carquest-autocraft-farm-truck');
INSERT INTO `manufacturers` VALUES (1239, 'QA2', 'CARQUEST/DIEHARD GOLD', 'carquest-diehard-gold');
INSERT INTO `manufacturers` VALUES (1240, 'QA3', 'CARQUEST/AUTOCRAFT MARINE', 'carquest-autocraft-marine');
INSERT INTO `manufacturers` VALUES (1241, 'QA4', 'CARQUEST/DIEHARD PLATINUM AGM', 'carquest-diehard-platinum-agm');
INSERT INTO `manufacturers` VALUES (1242, 'QA5', 'CARQUEST/DIEHARD SILVER', 'carquest-diehard-silver');
INSERT INTO `manufacturers` VALUES (1243, 'QA6', 'CARQUEST/AUTOCRAFT WIPERS', 'carquest-autocraft-wipers');
INSERT INTO `manufacturers` VALUES (1244, 'QA7', 'CARQUEST/BWD AUTOMOTIVE', 'carquest-bwd-automotive');
INSERT INTO `manufacturers` VALUES (1245, 'QA8', 'CARQUEST CARBURETOR PARTS', 'carquest-carburetor-parts');
INSERT INTO `manufacturers` VALUES (1246, 'QA9', 'CARQUEST/INTERMOTOR', 'carquest-intermotor');
INSERT INTO `manufacturers` VALUES (1247, 'QB0', 'CARQUEST HEATER CORE', 'carquest-heater-core');
INSERT INTO `manufacturers` VALUES (1248, 'QB1', 'CARQUEST INTERCOOLER', 'carquest-intercooler');
INSERT INTO `manufacturers` VALUES (1249, 'QB2', 'CARQUEST RADIATOR', 'carquest-radiator');
INSERT INTO `manufacturers` VALUES (1250, 'QB3', 'CARQUEST/CARQUEST WEAREVER', 'carquest-carquest-wearever');
INSERT INTO `manufacturers` VALUES (1251, 'QB4', 'CARQUEST/KLEENVIEW', 'carquest-kleenview');
INSERT INTO `manufacturers` VALUES (1252, 'QB5', 'CARQUEST/MOTIVE GEAR', 'carquest-motive-gear');
INSERT INTO `manufacturers` VALUES (1253, 'QB6', 'CARQUEST/OPTIMA', 'carquest-optima');
INSERT INTO `manufacturers` VALUES (1254, 'QB8', 'CARQUEST TIMING', 'carquest-timing');
INSERT INTO `manufacturers` VALUES (1255, 'QB9', 'CARQUEST/ PREMIUM AC', 'carquest-premium-ac');
INSERT INTO `manufacturers` VALUES (1256, 'QC7', 'CARQUEST/CARQUEST DISTRIBUTORS', 'carquest-carquest-distributors');
INSERT INTO `manufacturers` VALUES (1257, 'QD0', 'CARQUEST/TRICO INSTINCT', 'carquest-trico-instinct');
INSERT INTO `manufacturers` VALUES (1258, 'QD1', 'CARQUEST/TRICO ONYX', 'carquest-trico-onyx');
INSERT INTO `manufacturers` VALUES (1259, 'QD2', 'CARQUEST/WAGNER BRAKES', 'carquest-wagner-brakes');
INSERT INTO `manufacturers` VALUES (1260, 'QD4', 'CARQUEST WEAREVER FRONTLINE ROTORS', 'carquest-wearever-frontline-rotors');
INSERT INTO `manufacturers` VALUES (1261, 'QD6', 'CARQUEST/WEAREVER', 'carquest-wearever');
INSERT INTO `manufacturers` VALUES (1262, 'QD7', 'CARQUEST/TOUGHONE LIGHT', 'carquest-toughone-light');
INSERT INTO `manufacturers` VALUES (1263, 'QD8', 'CARQUEST/CARQUEST WATER PUMPS', 'carquest-carquest-water-pumps');
INSERT INTO `manufacturers` VALUES (1264, 'SFR', 'SURE FILTER', 'sure-filter');
INSERT INTO `manufacturers` VALUES (1265, 'SLS', 'SILENT STOP', 'silent-stop');
INSERT INTO `manufacturers` VALUES (1266, 'UAM', 'UNI-SELECT/AUTO EXTRA MOUNTS', 'uni-select-auto-extra-mounts');
INSERT INTO `manufacturers` VALUES (1267, 'V96', 'ADVANCE/CARQUEST FUEL PUMPS', 'advance-carquest-fuel-pumps');
INSERT INTO `manufacturers` VALUES (1268, 'AC5', 'ACEON AUTO PARTS', 'aceon-auto-parts');
INSERT INTO `manufacturers` VALUES (1269, 'QXC', 'CARQUEST/XTRACLEAR', 'carquest-xtraclear');
INSERT INTO `manufacturers` VALUES (1270, 'RX5', 'RAIN X ARCH', 'rain-x-arch');
INSERT INTO `manufacturers` VALUES (1271, 'V95', 'ADVANCE/CARQUEST IGNITION WIRES', 'advance-carquest-ignition-wires');
INSERT INTO `manufacturers` VALUES (1272, 'V97', 'ADVANCE/XTRACLEAR', 'advance-xtraclear');
INSERT INTO `manufacturers` VALUES (1273, 'WIM', 'WILLMORE', 'willmore');
INSERT INTO `manufacturers` VALUES (1274, 'GMC', 'GM GENUINE PARTS CANADA', 'gm-genuine-parts-canada');
INSERT INTO `manufacturers` VALUES (1275, 'GMP', 'GM GENUINE PARTS', 'gm-genuine-parts');
INSERT INTO `manufacturers` VALUES (1276, 'PZN', 'PUREZONE', 'purezone');
INSERT INTO `manufacturers` VALUES (1277, 'CNC', 'CRANK-N-CHARGE', 'crank-n-charge');
INSERT INTO `manufacturers` VALUES (1278, 'CT6', 'ADVANCE/OXYGEN SENSORS', 'advance-oxygen-sensors');
INSERT INTO `manufacturers` VALUES (1279, 'F73', 'FOUR SEASONS PAC-SUPER KITS', 'four-seasons-pac-super-kits');
INSERT INTO `manufacturers` VALUES (1280, 'FFP', 'FVP FUEL PUMPS', 'fvp-fuel-pumps');
INSERT INTO `manufacturers` VALUES (1281, 'GTP', 'GT PERFORMANCE', 'gt-performance');
INSERT INTO `manufacturers` VALUES (1282, 'NDP', 'NETWORK/DURAGO EP COATED', 'network-durago-ep-coated');
INSERT INTO `manufacturers` VALUES (1283, 'PT5', 'PARAMOUNT AUTOMOTIVE', 'paramount-automotive');
INSERT INTO `manufacturers` VALUES (1284, 'PZF', 'PUREZONE CABIN, FUEL, TRANS FILTERS', 'purezone-cabin-fuel-trans-filters');
INSERT INTO `manufacturers` VALUES (1285, 'QC8', 'CARQUEST/OXYGEN SENSORS', 'carquest-oxygen-sensors');
INSERT INTO `manufacturers` VALUES (1286, 'QCL', 'CARQUEST CLUTCH', 'carquest-clutch');
INSERT INTO `manufacturers` VALUES (1287, 'QWR', 'CARQUEST/WINDOW REGULATORS', 'carquest-window-regulators');
INSERT INTO `manufacturers` VALUES (1288, 'V98', 'ADVANCE CLUTCH', 'advance-clutch');
INSERT INTO `manufacturers` VALUES (1289, 'V99', 'ADVANCE/CARQUEST WINDOW REGULATORS', 'advance-carquest-window-regulators');
INSERT INTO `manufacturers` VALUES (1290, 'ZTB', 'ZUMBROTA BEARING AND GEAR', 'zumbrota-bearing-and-gear');
INSERT INTO `manufacturers` VALUES (1291, 'AL5', 'A.L. SOLUTIONS U.S.', 'a-l-solutions-u-s');
INSERT INTO `manufacturers` VALUES (1292, 'EBX', 'ECOBREX', 'ecobrex');
INSERT INTO `manufacturers` VALUES (1293, 'KTN', 'KTN THERMO DYNAMICS', 'ktn-thermo-dynamics');
INSERT INTO `manufacturers` VALUES (1294, 'SNT', 'SENTEC', 'sentec');
INSERT INTO `manufacturers` VALUES (1295, 'DNA', 'DINAN', 'dinan');
INSERT INTO `manufacturers` VALUES (1296, 'ERS', 'EURO-STOP', 'euro-stop');
INSERT INTO `manufacturers` VALUES (1297, 'FXB', 'FLEXX BOOT', 'flexx-boot');
INSERT INTO `manufacturers` VALUES (1298, 'QD9', 'CARQUEST/DRIVEWORKS WATER PUMPS', 'carquest-driveworks-water-pumps');
INSERT INTO `manufacturers` VALUES (1299, 'QDA', 'CARQUEST/DRIVEWORKS AIR FILTERS', 'carquest-driveworks-air-filters');
INSERT INTO `manufacturers` VALUES (1300, 'QRE', 'CARQUEST/PREMIUM REMANUFACTURED ENGINES', 'carquest-premium-remanufactured-engines');
INSERT INTO `manufacturers` VALUES (1301, 'RWE', 'ROWE', 'rowe');
INSERT INTO `manufacturers` VALUES (1302, 'SLX', 'SUPLEX', 'suplex');
INSERT INTO `manufacturers` VALUES (1303, 'SRL', 'SUPERLEX', 'superlex');
INSERT INTO `manufacturers` VALUES (1304, 'TPT', 'TRUE PARTS INC.', 'true-parts-inc');
INSERT INTO `manufacturers` VALUES (1305, 'V84', 'ADVANCE/DRIVEWORKS WATER PUMPS', 'advance-driveworks-water-pumps');
INSERT INTO `manufacturers` VALUES (1306, 'VLD', 'VOGTLAND', 'vogtland');
INSERT INTO `manufacturers` VALUES (1307, 'MXL', 'MAS PREMIUM XL/RD', 'mas-premium-xl-rd');
INSERT INTO `manufacturers` VALUES (1308, 'OCS', 'IMPORT DIRECT COIL SPRINGS', 'import-direct-coil-springs');
INSERT INTO `manufacturers` VALUES (1309, 'OIS', 'PRECISION COIL SPRINGS', 'precision-coil-springs');
INSERT INTO `manufacturers` VALUES (1310, 'QBA', 'QBA AUTOMOTIVE', 'qba-automotive');
INSERT INTO `manufacturers` VALUES (1311, 'ZRD', 'ZROADZ', 'zroadz');
INSERT INTO `manufacturers` VALUES (1312, 'M96', 'MONROE COMPLETE STRUT ASSEMBLIES', 'monroe-complete-strut-assemblies');
INSERT INTO `manufacturers` VALUES (1313, 'PFV', 'PERFECT STOP BY BOSCH FRICTION', 'perfect-stop-by-bosch-friction');
INSERT INTO `manufacturers` VALUES (1314, 'QMC', 'CARQUEST/WEAREVER MASTER CYLINDERS', 'carquest-wearever-master-cylinders');
INSERT INTO `manufacturers` VALUES (1315, 'SKP', 'SKP', 'skp');
INSERT INTO `manufacturers` VALUES (1316, 'VMC', 'ADVANCE/WEAREVER MASTER CYLINDERS', 'advance-wearever-master-cylinders');
INSERT INTO `manufacturers` VALUES (1317, 'A1P', 'AIR LIFT PERFORMANCE', 'air-lift-performance');
INSERT INTO `manufacturers` VALUES (1318, 'AR2', 'ARIES', 'aries');
INSERT INTO `manufacturers` VALUES (1319, 'EXL', 'EXCEL FROM RICHMOND', 'excel-from-richmond');
INSERT INTO `manufacturers` VALUES (1320, 'FGA', 'FAG USA', 'fag-usa');
INSERT INTO `manufacturers` VALUES (1321, 'HLN', 'HOLSTEIN', 'holstein');
INSERT INTO `manufacturers` VALUES (1322, 'ICA', 'ICON ALLOYS', 'icon-alloys');
INSERT INTO `manufacturers` VALUES (1323, 'JMS', 'JMS', 'jms');
INSERT INTO `manufacturers` VALUES (1324, 'OIM', 'IMPORT DIRECT CHASSIS', 'import-direct-chassis');
INSERT INTO `manufacturers` VALUES (1325, 'PUQ', 'PARTS MASTER/BRAKE DRUMS & ROTORS - UAP', 'parts-master-brake-drums-rotors-uap');
INSERT INTO `manufacturers` VALUES (1326, 'RA0', 'RAMCO AUTOMOTIVE', 'ramco-automotive');
INSERT INTO `manufacturers` VALUES (1327, 'SPQ', 'SPEC', 'spec');
INSERT INTO `manufacturers` VALUES (1328, 'ST6', 'SOUTHERN TRUCK', 'southern-truck');
INSERT INTO `manufacturers` VALUES (1329, 'ZNT', 'ZENETTI', 'zenetti');
INSERT INTO `manufacturers` VALUES (1330, 'AP6', 'US AUTO PARTS/EVAN FISCHER', 'us-auto-parts-evan-fischer');
INSERT INTO `manufacturers` VALUES (1331, 'M1C', 'MTC', 'mtc');
INSERT INTO `manufacturers` VALUES (1332, 'NVA', 'NOVAPACIFIC', 'novapacific');
INSERT INTO `manufacturers` VALUES (1333, 'QPR', 'CARQUEST PLATINUM ROTORS', 'carquest-platinum-rotors');
INSERT INTO `manufacturers` VALUES (1334, 'V85', 'ADVANCE PLATINUM ROTORS', 'advance-platinum-rotors');
INSERT INTO `manufacturers` VALUES (1335, 'BR3', 'BESTEST ROTATING ELECTRICAL', 'bestest-rotating-electrical');
INSERT INTO `manufacturers` VALUES (1336, 'OBF', 'O\'REILLY/BOSCH FILTERS', 'o-reilly-bosch-filters');
INSERT INTO `manufacturers` VALUES (1337, 'OBW', 'O\'REILLY/BOSCH WIPERS', 'o-reilly-bosch-wipers');
INSERT INTO `manufacturers` VALUES (1338, 'P5B', 'PRECISION CENTER SUPPORT BEARINGS', 'precision-center-support-bearings');
INSERT INTO `manufacturers` VALUES (1339, 'PWM', 'POWER TORQUE MOTOR MOUNTS', 'power-torque-motor-mounts');
INSERT INTO `manufacturers` VALUES (1340, 'QCB', 'CARQUEST BULBS', 'carquest-bulbs');
INSERT INTO `manufacturers` VALUES (1341, 'ULR', 'ULTIMA ROTATING ELECTRICAL', 'ultima-rotating-electrical');
INSERT INTO `manufacturers` VALUES (1342, 'UMR', 'ULTIMA SELECT ROTATING ELECTRICAL', 'ultima-select-rotating-electrical');
INSERT INTO `manufacturers` VALUES (1343, 'V86', 'ADVANCE BULBS', 'advance-bulbs');
INSERT INTO `manufacturers` VALUES (1344, '6AH', 'Alltech', 'alltech');
INSERT INTO `manufacturers` VALUES (1345, '6EX', 'Jet Performance', 'jet-performance');
INSERT INTO `manufacturers` VALUES (1346, '6HP', 'PB/MAGNUM', 'pb-magnum');
INSERT INTO `manufacturers` VALUES (1347, '6HS', 'PB/MONROE', 'pb-monroe');
INSERT INTO `manufacturers` VALUES (1348, '6HU', 'PB/MOPAR', 'pb-mopar');
INSERT INTO `manufacturers` VALUES (1349, '6HZ', 'PB/PHILIPS', 'pb-philips');
INSERT INTO `manufacturers` VALUES (1350, '6I5', 'PB/PROCOOL WATER PUMPS', 'pb-procool-water-pumps');
INSERT INTO `manufacturers` VALUES (1351, '6IA', 'PB/PROSTOP HARDWARE', 'pb-prostop-hardware');
INSERT INTO `manufacturers` VALUES (1352, '6IT', 'PB/VALUEGRADE FRICTION', 'pb-valuegrade-friction');
INSERT INTO `manufacturers` VALUES (1353, 'CSC', 'CSF COOLING', 'csf-cooling');
INSERT INTO `manufacturers` VALUES (1354, 'EHF', '1800RADIATOR PREMIUM 48 STATE', '1800radiator-premium-48-state');
INSERT INTO `manufacturers` VALUES (1355, 'KTL', 'KENTROL', 'kentrol');
INSERT INTO `manufacturers` VALUES (1356, 'N0T', 'NITROUS OUTLET', 'nitrous-outlet');
INSERT INTO `manufacturers` VALUES (1357, 'QTP', 'QTP', 'qtp');
INSERT INTO `manufacturers` VALUES (1358, 'CR5', 'CRS AUTOMOTIVE COOLING PRODUCTS', 'crs-automotive-cooling-products');
INSERT INTO `manufacturers` VALUES (1359, 'GTR', 'GEOTECH BRAKE ROTORS - UQUALITY', 'geotech-brake-rotors-uquality');
INSERT INTO `manufacturers` VALUES (1360, 'OBC', 'BRAKEBEST CALIPERS', 'brakebest-calipers');
INSERT INTO `manufacturers` VALUES (1361, 'OWP', 'MASTER PRO WATER PUMPS', 'master-pro-water-pumps');
INSERT INTO `manufacturers` VALUES (1362, 'PDN', 'PLATINUM DRIVELINE', 'platinum-driveline');
INSERT INTO `manufacturers` VALUES (1363, 'KPR', 'KAPARS', 'kapars');
INSERT INTO `manufacturers` VALUES (1364, 'UCP', 'UREMCO PLUS', 'uremco-plus');
INSERT INTO `manufacturers` VALUES (1365, 'V87', 'ADVANCE/CARQUEST DAYCO', 'advance-carquest-dayco');
INSERT INTO `manufacturers` VALUES (1366, 'FAP', 'FORMULA AUTO PARTS', 'formula-auto-parts');
INSERT INTO `manufacturers` VALUES (1367, 'Q92', 'CARQUEST/ PUROLATORONE', 'carquest-purolatorone');
INSERT INTO `manufacturers` VALUES (1368, 'UAN', 'UNI-SELECT/ASCENSION', 'uni-select-ascension');
INSERT INTO `manufacturers` VALUES (1369, 'VCV', 'ADVANCE/CV PLUS', 'advance-cv-plus');
INSERT INTO `manufacturers` VALUES (1370, 'VPO', 'ADVANCE/PUROLATORONE', 'advance-purolatorone');
INSERT INTO `manufacturers` VALUES (1371, 'ACK', 'XL A/C KITS', 'xl-a-c-kits');
INSERT INTO `manufacturers` VALUES (1372, 'IED', 'INLAND EMPIRE DRIVE LINE', 'inland-empire-drive-line');
INSERT INTO `manufacturers` VALUES (1373, 'FPK', 'FVP POWER STEERING HOSES AND KITS', 'fvp-power-steering-hoses-and-kits');
INSERT INTO `manufacturers` VALUES (1374, 'ILJ', 'ILJIN', 'iljin');
INSERT INTO `manufacturers` VALUES (1375, 'JKT', 'JACK TARMAC', 'jack-tarmac');
INSERT INTO `manufacturers` VALUES (1376, 'O1A', 'OSC (OLD)', 'osc-old');
INSERT INTO `manufacturers` VALUES (1377, 'PT7', 'PROTRAX', 'protrax');
INSERT INTO `manufacturers` VALUES (1378, 'RPB', 'REMANUFACTURED POWER BRAKE BOOSTERS', 'remanufactured-power-brake-boosters');
INSERT INTO `manufacturers` VALUES (1379, 'RSF', 'RS PARTS', 'rs-parts');
INSERT INTO `manufacturers` VALUES (1380, 'OCB', 'BRAKEBEST CALIPER BRACKETS', 'brakebest-caliper-brackets');
INSERT INTO `manufacturers` VALUES (1381, 'QFI', 'CARQUEST FUEL INJECTION', 'carquest-fuel-injection');
INSERT INTO `manufacturers` VALUES (1382, 'SA1', 'SPARTA', 'sparta');
INSERT INTO `manufacturers` VALUES (1383, 'CBI', 'CBK', 'cbk');
INSERT INTO `manufacturers` VALUES (1384, 'G2D', 'G2 DIESEL PRODUCTS', 'g2-diesel-products');
INSERT INTO `manufacturers` VALUES (1385, 'KSK', 'KING SHOCKS', 'king-shocks');
INSERT INTO `manufacturers` VALUES (1386, 'Q1A', 'QA1', 'qa1');
INSERT INTO `manufacturers` VALUES (1387, 'GRC', 'GRILLCRAFT', 'grillcraft');
INSERT INTO `manufacturers` VALUES (1388, 'MA3', 'MANEKI AUTOMOTIVE PRODUCTS', 'maneki-automotive-products');
INSERT INTO `manufacturers` VALUES (1389, 'MTY', 'MOTORCITY', 'motorcity');
INSERT INTO `manufacturers` VALUES (1390, 'PT8', 'POWERTEK', 'powertek');
INSERT INTO `manufacturers` VALUES (1391, 'SUL', 'SCHULTZ MANUFACTURING COMPANY', 'schultz-manufacturing-company');
INSERT INTO `manufacturers` VALUES (1392, 'JRD', 'JURID', 'jurid');
INSERT INTO `manufacturers` VALUES (1393, 'OD1', 'O\'REILLY/IMPORT DIRECT FUEL PUMP', 'o-reilly-import-direct-fuel-pump');
INSERT INTO `manufacturers` VALUES (1394, 'OP1', 'O\'REILLY/PRECISION FUEL PUMPS', 'o-reilly-precision-fuel-pumps');
INSERT INTO `manufacturers` VALUES (1395, 'ORH', 'O\'REILLY/MURRAY HEAT TRANSFER', 'o-reilly-murray-heat-transfer');
INSERT INTO `manufacturers` VALUES (1396, 'PT9', 'PROTECH', 'protech');
INSERT INTO `manufacturers` VALUES (1397, 'VRZ', 'VICTOR REINZ', 'victor-reinz');
INSERT INTO `manufacturers` VALUES (1398, 'C99', 'CARDONE/REPAIR & RETURN - EBAY', 'cardone-repair-return-ebay');
INSERT INTO `manufacturers` VALUES (1399, 'QSY', 'CARQUEST/SYLVANIA LIGHTING', 'carquest-sylvania-lighting');
INSERT INTO `manufacturers` VALUES (1400, 'AUR', 'AUER AUTOMOTIVE', 'auer-automotive');
INSERT INTO `manufacturers` VALUES (1401, 'OMH', 'O\'REILLY/MASTER PRO HUB ASSEMBLIES', 'o-reilly-master-pro-hub-assemblies');
INSERT INTO `manufacturers` VALUES (1402, 'OMR', 'O\'REILLY/MICROGARD', 'o-reilly-microgard');
INSERT INTO `manufacturers` VALUES (1403, 'DM1', 'DURAMOUNT', 'duramount');
INSERT INTO `manufacturers` VALUES (1404, 'FVM', 'FVP ENGINE AND TRANSMISSION MOUNTS', 'fvp-engine-and-transmission-mounts');
INSERT INTO `manufacturers` VALUES (1405, 'NUN', 'NUGEON', 'nugeon');
INSERT INTO `manufacturers` VALUES (1406, '6I8', 'PB/PROSTART ELECTRICAL', 'pb-prostart-electrical');
INSERT INTO `manufacturers` VALUES (1407, '6M1', 'PB/CARDONE', 'pb-cardone');
INSERT INTO `manufacturers` VALUES (1408, '6M2', 'PB/HOPKINS (HOPPY)', 'pb-hopkins-hoppy');
INSERT INTO `manufacturers` VALUES (1409, '6M3', 'PB/MOTORCRAFT IGNITION', 'pb-motorcraft-ignition');
INSERT INTO `manufacturers` VALUES (1410, '6M4', 'PB/MOTORCRAFT ENGINE MANAGEMENT', 'pb-motorcraft-engine-management');
INSERT INTO `manufacturers` VALUES (1411, '6M6', 'PB/MOTORCRAFT ENGINE FILTERS', 'pb-motorcraft-engine-filters');
INSERT INTO `manufacturers` VALUES (1412, '6M9', 'PB/URO', 'pb-uro');
INSERT INTO `manufacturers` VALUES (1413, '6N0', 'PB/CHAMPION BATTERIES', 'pb-champion-batteries');
INSERT INTO `manufacturers` VALUES (1414, '6N3', 'PB/MOUNTS', 'pb-mounts');
INSERT INTO `manufacturers` VALUES (1415, '6N4', 'PB/ACDELCO IGNITION', 'pb-acdelco-ignition');
INSERT INTO `manufacturers` VALUES (1416, '6N5', 'PB/ACDELCO FUEL PUMP', 'pb-acdelco-fuel-pump');
INSERT INTO `manufacturers` VALUES (1417, '6N6', 'PB/ACDELCO FILTERS', 'pb-acdelco-filters');
INSERT INTO `manufacturers` VALUES (1418, '6N8', 'PB/ACDELCO BATTERIES', 'pb-acdelco-batteries');
INSERT INTO `manufacturers` VALUES (1419, '6P1', 'PB/PROSTOP PLATINUM', 'pb-prostop-platinum');
INSERT INTO `manufacturers` VALUES (1420, '6P2', 'PB/PILOT', 'pb-pilot');
INSERT INTO `manufacturers` VALUES (1421, '6P3', 'PB/PROLINE WHEEL HUB ASSEMBLIES', 'pb-proline-wheel-hub-assemblies');
INSERT INTO `manufacturers` VALUES (1422, '6P5', 'PB/PROLINE FILTERS', 'pb-proline-filters');
INSERT INTO `manufacturers` VALUES (1423, '6S1', 'PB/PROSTOP FRICTION', 'pb-prostop-friction');
INSERT INTO `manufacturers` VALUES (1424, '6S2', 'PB/PROSTOP DRUMS AND ROTORS', 'pb-prostop-drums-and-rotors');
INSERT INTO `manufacturers` VALUES (1425, '6S8', 'PB/VALUEGRADE IGNITION', 'pb-valuegrade-ignition');
INSERT INTO `manufacturers` VALUES (1426, '6T0', 'CHAMPION LIGHTING', 'champion-lighting');
INSERT INTO `manufacturers` VALUES (1427, '6T1', 'PB/VALUGRADE ELECTRICAL', 'pb-valugrade-electrical');
INSERT INTO `manufacturers` VALUES (1428, '6T2', 'PB/VALUEGRADE EMMISION', 'pb-valuegrade-emmision');
INSERT INTO `manufacturers` VALUES (1429, 'DF1', 'DFC', 'dfc');
INSERT INTO `manufacturers` VALUES (1430, 'FVS', 'FVP RIDE CONTROL', 'fvp-ride-control');
INSERT INTO `manufacturers` VALUES (1431, '6N1', 'PB/LYNX', 'pb-lynx');
INSERT INTO `manufacturers` VALUES (1432, '6N9', 'PB/BOSCH BATTERIES', 'pb-bosch-batteries');
INSERT INTO `manufacturers` VALUES (1433, '6P4', 'PB/PROLINE BEARINGS & SEALS', 'pb-proline-bearings-seals');
INSERT INTO `manufacturers` VALUES (1434, 'QAB', 'CARQUEST/PILOT AUTOMOTIVE LAMPS', 'carquest-pilot-automotive-lamps');
INSERT INTO `manufacturers` VALUES (1435, 'VA1', 'ADVANCE/PILOT AUTOMOTIVE LAMPS', 'advance-pilot-automotive-lamps');
INSERT INTO `manufacturers` VALUES (1436, '6P7', 'PB/PROSTART BATTERIES', 'pb-prostart-batteries');
INSERT INTO `manufacturers` VALUES (1437, 'ZVN', 'INA US', 'ina-us');
INSERT INTO `manufacturers` VALUES (1438, '6P8', 'PB/PROSTEER POWER STEERING', 'pb-prosteer-power-steering');
INSERT INTO `manufacturers` VALUES (1439, 'ACG', 'ACAT PERFORMANCE', 'acat-performance');
INSERT INTO `manufacturers` VALUES (1440, 'CYT', 'COYOTE PREMIUM WHEEL ACCESSORIES', 'coyote-premium-wheel-accessories');
INSERT INTO `manufacturers` VALUES (1441, 'FVH', 'FVP HEATING AND AIR CONDITIONING', 'fvp-heating-and-air-conditioning');
INSERT INTO `manufacturers` VALUES (1442, '6Z3', 'PB/PROLINE PLATINUM WHEEL HUB ASSEMBLIES', 'pb-proline-platinum-wheel-hub-assemblies');
INSERT INTO `manufacturers` VALUES (1443, '8GS', 'GENESIS OEM', 'genesis-oem');
INSERT INTO `manufacturers` VALUES (1444, 'ALN', 'ALT TENSIONER', 'alt-tensioner');
INSERT INTO `manufacturers` VALUES (1445, 'SGN', 'STIGAN', 'stigan');
INSERT INTO `manufacturers` VALUES (1446, 'FVI', 'FVP IGNITION COILS', 'fvp-ignition-coils');
INSERT INTO `manufacturers` VALUES (1447, 'TLF', 'TECHLINE FILTER PRODUCTS', 'techline-filter-products');
INSERT INTO `manufacturers` VALUES (1448, 'V69', 'ADVANCE/MOVERAS TRANSMISSIONS', 'advance-moveras-transmissions');
INSERT INTO `manufacturers` VALUES (1449, '6T4', 'PB/ AUTO EXTRA HUB ASSEMBLIES', 'pb-auto-extra-hub-assemblies');
INSERT INTO `manufacturers` VALUES (1450, '6T5', 'AUTO PLUS/ AUTO EXTRA HUB ASSEMBLIES', 'auto-plus-auto-extra-hub-assemblies');
INSERT INTO `manufacturers` VALUES (1451, 'BB3', 'AUTOPLUS/BOSCH BATTERIES', 'autoplus-bosch-batteries');
INSERT INTO `manufacturers` VALUES (1452, 'C98', 'CARDONE EBAY', 'cardone-ebay');
INSERT INTO `manufacturers` VALUES (1453, 'DTQ', 'DTECH PRODUCTS', 'dtech-products');
INSERT INTO `manufacturers` VALUES (1454, 'GOI', 'GO INDUSTRIES', 'go-industries');
INSERT INTO `manufacturers` VALUES (1455, 'SND', 'SINISTER DIESEL', 'sinister-diesel');
INSERT INTO `manufacturers` VALUES (1456, 'STW', 'STO N SHO', 'sto-n-sho');
INSERT INTO `manufacturers` VALUES (1457, 'USZ', 'EXIDE SUBZERO', 'exide-subzero');
INSERT INTO `manufacturers` VALUES (1458, 'PP0', 'POWERTRAIN PRODUCTS INC-040', 'powertrain-products-inc-040');
INSERT INTO `manufacturers` VALUES (1459, 'PP1', 'POWERTRAIN PRODUCTS INC-051', 'powertrain-products-inc-051');
INSERT INTO `manufacturers` VALUES (1460, '6T6', 'PB/MOTORAD', 'pb-motorad');
INSERT INTO `manufacturers` VALUES (1461, 'BDD', 'BD DIESEL', 'bd-diesel');
INSERT INTO `manufacturers` VALUES (1462, 'EAE', 'EASTERN EXHAUST', 'eastern-exhaust');
INSERT INTO `manufacturers` VALUES (1463, 'FVC', 'FVP CATALYTIC CONVERTERS CARB', 'fvp-catalytic-converters-carb');
INSERT INTO `manufacturers` VALUES (1464, 'MOA', 'MAHLE ORIGINAL ADVANTAGE', 'mahle-original-advantage');
INSERT INTO `manufacturers` VALUES (1465, 'OIX', 'O\'REILLY/IMPORT DIRECT POWER STEERING', 'o-reilly-import-direct-power-steering');
INSERT INTO `manufacturers` VALUES (1466, 'OPJ', 'O\'REILLY/PRECISION POWER STEERING', 'o-reilly-precision-power-steering');
INSERT INTO `manufacturers` VALUES (1467, 'PGY', 'PURE ENERGY', 'pure-energy');
INSERT INTO `manufacturers` VALUES (1468, 'PP2', 'POWERTRAIN PRODUCTS INC-032', 'powertrain-products-inc-032');
INSERT INTO `manufacturers` VALUES (1469, 'PTZ', 'POWERTRAIN PRODUCTS INC-Z01', 'powertrain-products-inc-z01');
INSERT INTO `manufacturers` VALUES (1470, 'QAC', 'CARQUEST/REMOTES UNLIMITED', 'carquest-remotes-unlimited');
INSERT INTO `manufacturers` VALUES (1471, 'TTN', 'TITAN XF', 'titan-xf');
INSERT INTO `manufacturers` VALUES (1472, 'V73', 'ADVANCE/SHIFT PRO BY ETE', 'advance-shift-pro-by-ete');
INSERT INTO `manufacturers` VALUES (1473, 'VA2', 'ADVANCE/REMOTES UNLIMITED', 'advance-remotes-unlimited');
INSERT INTO `manufacturers` VALUES (1474, 'VRT', 'VERTEC', 'vertec');
INSERT INTO `manufacturers` VALUES (1475, 'FBB', 'FVP BELTS', 'fvp-belts');
INSERT INTO `manufacturers` VALUES (1476, 'FSA', 'FVP STARTERS & ALTERNATORS', 'fvp-starters-alternators');
INSERT INTO `manufacturers` VALUES (1477, 'OMZ', 'O\'REILLY/MASTER PRO STRUT ASSEMBLIES', 'o-reilly-master-pro-strut-assemblies');
INSERT INTO `manufacturers` VALUES (1478, 'OPQ', 'O\'REILLY/POWER TORQUE', 'o-reilly-power-torque');
INSERT INTO `manufacturers` VALUES (1479, 'OSM', 'O\'REILLY/MASTER PRO STRUT MOUNTS', 'o-reilly-master-pro-strut-mounts');
INSERT INTO `manufacturers` VALUES (1480, 'PFK', 'PERFECT STOP BY BBB INDUSTRIES CALIPERS', 'perfect-stop-by-bbb-industries-calipers');
INSERT INTO `manufacturers` VALUES (1481, '6P9', 'PB/PROSTEER CHASSIS', 'pb-prosteer-chassis');
INSERT INTO `manufacturers` VALUES (1482, 'MSP', 'MAS PREMIUM', 'mas-premium');
INSERT INTO `manufacturers` VALUES (1483, 'PS2', 'PARTSOURCE/BOSCH', 'partsource-bosch');
INSERT INTO `manufacturers` VALUES (1484, '6T7', 'PB/BOSCH FUEL PUMPS', 'pb-bosch-fuel-pumps');
INSERT INTO `manufacturers` VALUES (1485, '6T8', 'PB/BOSCH ALTERNATORS', 'pb-bosch-alternators');
INSERT INTO `manufacturers` VALUES (1486, '6T9', 'PB/BOSCH STARTERS', 'pb-bosch-starters');
INSERT INTO `manufacturers` VALUES (1487, 'BBE', 'BILLY BOAT EXHAUST', 'billy-boat-exhaust');
INSERT INTO `manufacturers` VALUES (1488, 'BMS', 'BMR SUSPENSION', 'bmr-suspension');
INSERT INTO `manufacturers` VALUES (1489, 'ODF', 'O\'REILLY/IMPORT DIRECT FUEL PUMPS', 'o-reilly-import-direct-fuel-pumps');
INSERT INTO `manufacturers` VALUES (1490, 'OP2', 'O\'REILLY/PRECISION FUEL PUMP', 'o-reilly-precision-fuel-pump');
INSERT INTO `manufacturers` VALUES (1491, 'RMF', 'ROCKSTAR MUD FLAPS', 'rockstar-mud-flaps');
INSERT INTO `manufacturers` VALUES (1492, 'SNW', 'SNOWSPORT', 'snowsport');
INSERT INTO `manufacturers` VALUES (1493, 'V9C', 'VAICO', 'vaico');
INSERT INTO `manufacturers` VALUES (1494, 'V9M', 'VEMO', 'vemo');
INSERT INTO `manufacturers` VALUES (1495, 'WHD', 'WILSON AUTO ELECTRIC MEDIUM/HEAVY DUTY', 'wilson-auto-electric-medium-heavy-duty');
INSERT INTO `manufacturers` VALUES (1496, 'NAU', 'NEWAUTOPART', 'newautopart');
INSERT INTO `manufacturers` VALUES (1497, 'OCL', 'O\'REILLY/CLEVITE', 'o-reilly-clevite');
INSERT INTO `manufacturers` VALUES (1498, 'OML', 'O\'REILLY/MAHLE', 'o-reilly-mahle');
INSERT INTO `manufacturers` VALUES (1499, '6TT', 'CHAMPION LIGHTING-TECH', 'champion-lighting-tech');
INSERT INTO `manufacturers` VALUES (1500, 'AP7', 'AUTOPART T', 'autopart-t');
INSERT INTO `manufacturers` VALUES (1501, 'TIL', 'TRIM ILLUSION', 'trim-illusion');
INSERT INTO `manufacturers` VALUES (1502, '6M5', 'PB/MOTORCRAFT FUEL FILTERS', 'pb-motorcraft-fuel-filters');
INSERT INTO `manufacturers` VALUES (1503, '6N7', 'PB/ACDELCO ENGINE MANAGEMENT', 'pb-acdelco-engine-management');
INSERT INTO `manufacturers` VALUES (1504, '6T3', 'PB/OPTIMA', 'pb-optima');
INSERT INTO `manufacturers` VALUES (1505, 'V74', 'ADVANCE/LAWN & GARDEN', 'advance-lawn-garden');
INSERT INTO `manufacturers` VALUES (1506, 'V75', 'ADVANCE/GOLF CART', 'advance-golf-cart');
INSERT INTO `manufacturers` VALUES (1507, 'A5L', 'AUTEL', 'autel');
INSERT INTO `manufacturers` VALUES (1508, 'HWR', 'HOLLYWOOD RACKS', 'hollywood-racks');
INSERT INTO `manufacturers` VALUES (1509, 'MPB', 'MOPAR BRAND', 'mopar-brand');
INSERT INTO `manufacturers` VALUES (1510, 'MGM', 'MAGNETI MARELLI OFFERED BY MOPAR', 'magneti-marelli-offered-by-mopar');
INSERT INTO `manufacturers` VALUES (1511, 'MRM', 'MOPAR REMAN', 'mopar-reman');
INSERT INTO `manufacturers` VALUES (1512, '8AC', 'ACURA', 'acura');
INSERT INTO `manufacturers` VALUES (1513, '8BM', 'BMW', 'bmw');
INSERT INTO `manufacturers` VALUES (1514, '8CY', 'CHRYSLER', 'chrysler');
INSERT INTO `manufacturers` VALUES (1515, '8DW', 'DAEWOO', 'daewoo');
INSERT INTO `manufacturers` VALUES (1516, '8FI', 'FIAT', 'fiat');
INSERT INTO `manufacturers` VALUES (1517, '8FO', 'FORD', 'ford');
INSERT INTO `manufacturers` VALUES (1518, '8GM', 'GENERAL MOTORS', 'general-motors');
INSERT INTO `manufacturers` VALUES (1519, '8HO', 'HONDA', 'honda');
INSERT INTO `manufacturers` VALUES (1520, '8HY', 'HYUNDAI', 'hyundai');
INSERT INTO `manufacturers` VALUES (1521, '8IN', 'INFINITI', 'infiniti');
INSERT INTO `manufacturers` VALUES (1522, '8IS', 'ISUZU', 'isuzu');
INSERT INTO `manufacturers` VALUES (1523, '8JG', 'JAGUAR', 'jaguar');
INSERT INTO `manufacturers` VALUES (1524, '8KI', 'KIA', 'kia');
INSERT INTO `manufacturers` VALUES (1525, '8LR', 'LAND ROVER', 'land-rover');
INSERT INTO `manufacturers` VALUES (1526, '8LX', 'LEXUS', 'lexus');
INSERT INTO `manufacturers` VALUES (1527, '8MZ', 'MAZDA', 'mazda');
INSERT INTO `manufacturers` VALUES (1528, '8MB', 'MERCEDES BENZ', 'mercedes-benz');
INSERT INTO `manufacturers` VALUES (1529, '8MI', 'MITSUBISHI', 'mitsubishi');
INSERT INTO `manufacturers` VALUES (1530, '8NI', 'NISSAN', 'nissan');
INSERT INTO `manufacturers` VALUES (1531, '8PO', 'PORSCHE', 'porsche');
INSERT INTO `manufacturers` VALUES (1532, '8SB', 'SAAB', 'saab');
INSERT INTO `manufacturers` VALUES (1533, '8SU', 'SUBARU', 'subaru');
INSERT INTO `manufacturers` VALUES (1534, '8SZ', 'SUZUKI', 'suzuki');
INSERT INTO `manufacturers` VALUES (1535, '8TO', 'TOYOTA', 'toyota');
INSERT INTO `manufacturers` VALUES (1536, '8VW', 'VOLKSWAGEN', 'volkswagen');
INSERT INTO `manufacturers` VALUES (1537, '8VO', 'VOLVO', 'volvo');
INSERT INTO `manufacturers` VALUES (1538, 'MIG', 'MIGHTY', 'mighty');
INSERT INTO `manufacturers` VALUES (1539, 'S89', 'SAFETY AUTO PARTS', 'safety-auto-parts');
INSERT INTO `manufacturers` VALUES (1540, '7B4', 'BAJA DESIGNS', 'baja-designs');
INSERT INTO `manufacturers` VALUES (1541, '7YI', 'V-THUNDER/ COMPETITION CAM', 'v-thunder-competition-cam');
INSERT INTO `manufacturers` VALUES (1542, 'EIH', 'EIBACH SPRINGS', 'eibach-springs');
INSERT INTO `manufacturers` VALUES (1543, 'BPR', 'BRAKE PRO', 'brake-pro');
INSERT INTO `manufacturers` VALUES (1544, 'C23', 'CERTIFIED', 'certified');
INSERT INTO `manufacturers` VALUES (1545, 'MGP', 'MOTIVE GEAR PERFORMANCE', 'motive-gear-performance');
INSERT INTO `manufacturers` VALUES (1546, 'NPS', 'NEWARK AUTO CARPET', 'newark-auto-carpet');
INSERT INTO `manufacturers` VALUES (1547, 'PRF', 'PROFORM', 'proform');
INSERT INTO `manufacturers` VALUES (1548, 'XWK', 'TUFFY SEC', 'tuffy-sec');
INSERT INTO `manufacturers` VALUES (1549, '6BI', 'Bolt Lock', 'bolt-lock');
INSERT INTO `manufacturers` VALUES (1550, '6DQ', 'ENGINE PRO', 'engine-pro');
INSERT INTO `manufacturers` VALUES (1551, 'OM1', 'OMNIPARTS', 'omniparts');
INSERT INTO `manufacturers` VALUES (1552, 'YC1', 'SUNBELT RADIATORS INC', 'sunbelt-radiators-inc');
INSERT INTO `manufacturers` VALUES (1553, 'PO1', 'PRONTO/OE BOSCH', 'pronto-oe-bosch');
INSERT INTO `manufacturers` VALUES (1554, 'PO2', 'PRONTO/OE DELPHI', 'pronto-oe-delphi');
INSERT INTO `manufacturers` VALUES (1555, 'PO3', 'PRONTO/OE ACDELCO', 'pronto-oe-acdelco');
INSERT INTO `manufacturers` VALUES (1556, 'PO4', 'PRONTO/OE DENSO', 'pronto-oe-denso');
INSERT INTO `manufacturers` VALUES (1557, 'PO5', 'PRONTO/OE MOTORCRAFT', 'pronto-oe-motorcraft');
INSERT INTO `manufacturers` VALUES (1558, 'SP8', 'SUSPENSIA', 'suspensia');
INSERT INTO `manufacturers` VALUES (1559, 'ACJ', 'ACI PLUS', 'aci-plus');
INSERT INTO `manufacturers` VALUES (1560, 'BBK', 'BENDIX BRAKE SHOE KITS', 'bendix-brake-shoe-kits');
INSERT INTO `manufacturers` VALUES (1561, 'FEM', 'FACET ENGINE MANAGEMENT', 'facet-engine-management');
INSERT INTO `manufacturers` VALUES (1562, 'OBT', 'O\'REILLY/BRAKEBEST SELECT TRANSGLOBE', 'o-reilly-brakebest-select-transglobe');
INSERT INTO `manufacturers` VALUES (1563, 'SEZ', 'SENS.IT', 'sens.it');
INSERT INTO `manufacturers` VALUES (1564, 'GNS', 'GNS WATER PUMPS', 'gns-water-pumps');
INSERT INTO `manufacturers` VALUES (1565, 'PCK', 'PACIFIC', 'pacific');
INSERT INTO `manufacturers` VALUES (1566, 'ALD', 'ALDAN AMERICAN', 'aldan-american');
INSERT INTO `manufacturers` VALUES (1567, 'RSH', 'ROUSH', 'roush');
INSERT INTO `manufacturers` VALUES (1568, 'SFK', 'SFK MANUFACTURING', 'sfk-manufacturing');
INSERT INTO `manufacturers` VALUES (1569, 'TCY', 'TOUGH COUNTRY', 'tough-country');
INSERT INTO `manufacturers` VALUES (1570, 'FMC', 'FVP MASTER CYLINDERS', 'fvp-master-cylinders');
INSERT INTO `manufacturers` VALUES (1571, 'FVO', 'FMP BLENDED O2 SENSORS', 'fmp-blended-o2-sensors');
INSERT INTO `manufacturers` VALUES (1572, 'HRG', 'HANSUN WINDOW REGULATOR', 'hansun-window-regulator');
INSERT INTO `manufacturers` VALUES (1573, 'NAV', 'NEW ADVANTAGE POWER STEERING', 'new-advantage-power-steering');
INSERT INTO `manufacturers` VALUES (1574, 'ORC', 'O\'REILLY/MASTER PRO IGNITION COILS', 'o-reilly-master-pro-ignition-coils');
INSERT INTO `manufacturers` VALUES (1575, 'PGI', 'PGI EXTENDED LIFE', 'pgi-extended-life');
INSERT INTO `manufacturers` VALUES (1576, 'RTD', 'RT OFFROAD', 'rt-offroad');
INSERT INTO `manufacturers` VALUES (1577, 'RX6', 'RAIN-X CANADA QUANTUM', 'rain-x-canada-quantum');
INSERT INTO `manufacturers` VALUES (1578, 'BOA', 'ALLIANCE OE BOSCH  SPARK PLUGS', 'alliance-oe-bosch-spark-plugs');
INSERT INTO `manufacturers` VALUES (1579, 'SQY', 'STRONGARM ALPHA', 'strongarm-alpha');
INSERT INTO `manufacturers` VALUES (1580, 'UHC', 'ULTRA HC', 'ultra-hc');
INSERT INTO `manufacturers` VALUES (1581, 'APU', 'WAGNER CALIPERS', 'wagner-calipers');
INSERT INTO `manufacturers` VALUES (1582, 'NGO', 'ALLIANCE OE NGK  SPARK PLUGS', 'alliance-oe-ngk-spark-plugs');
INSERT INTO `manufacturers` VALUES (1583, 'DSA', 'ALLIANCE OE DENSO  SPARK PLUGS', 'alliance-oe-denso-spark-plugs');
INSERT INTO `manufacturers` VALUES (1584, 'POE', 'PRO-SERIES OE BRAKE SHOES AND PADS', 'pro-series-oe-brake-shoes-and-pads');
INSERT INTO `manufacturers` VALUES (1585, 'DPR', 'DURAGO - BRAKE PAD AND ROTOR KIT', 'durago-brake-pad-and-rotor-kit');
INSERT INTO `manufacturers` VALUES (1586, 'SPY', 'SPIRE SUPPLY', 'spire-supply');
INSERT INTO `manufacturers` VALUES (1587, '6SA', 'PB/PROSTOP NEW', 'pb-prostop-new');
INSERT INTO `manufacturers` VALUES (1588, 'AKK', 'AMERICAN CAR CRAFT', 'american-car-craft');
INSERT INTO `manufacturers` VALUES (1589, 'KBD', 'KBD BODY KITS', 'kbd-body-kits');
INSERT INTO `manufacturers` VALUES (1590, 'TDJ', 'TRACTION DJ', 'traction-dj');
INSERT INTO `manufacturers` VALUES (1591, 'ARK', 'ARK PERFORMANCE', 'ark-performance');
INSERT INTO `manufacturers` VALUES (1592, 'AYP', 'AGENCY POWER', 'agency-power');
INSERT INTO `manufacturers` VALUES (1593, 'BKB', 'BAJA KITS BY BRENTHEL', 'baja-kits-by-brenthel');
INSERT INTO `manufacturers` VALUES (1594, 'BLF', 'BLACK FOREST', 'black-forest');
INSERT INTO `manufacturers` VALUES (1595, 'BLW', 'BLACKWATER ENGINES', 'blackwater-engines');
INSERT INTO `manufacturers` VALUES (1596, 'MC6', 'MILLER CAT', 'miller-cat');
INSERT INTO `manufacturers` VALUES (1597, 'NOL', 'NOLATHANE', 'nolathane');
INSERT INTO `manufacturers` VALUES (1598, 'TDP', 'TRANS-DAPT PERFORMANCE', 'trans-dapt-performance');
INSERT INTO `manufacturers` VALUES (1599, 'VGD', 'VANGUARD OFF ROAD', 'vanguard-off-road');
INSERT INTO `manufacturers` VALUES (1600, 'WPQ', 'ADVANCE/CARQUEST WORLDPAC', 'advance-carquest-worldpac');
INSERT INTO `manufacturers` VALUES (1601, '1FR', '1 FACTORY RADIO', '1-factory-radio');
INSERT INTO `manufacturers` VALUES (1602, 'AP8', 'APOC INDUSTRIES', 'apoc-industries');
INSERT INTO `manufacturers` VALUES (1603, 'B1T', 'BANKS POWER', 'banks-power');
INSERT INTO `manufacturers` VALUES (1604, 'CLT', 'CLAYTON OFF ROAD', 'clayton-off-road');
INSERT INTO `manufacturers` VALUES (1605, 'CUE', 'CURRIE ENTERPRISES', 'currie-enterprises');
INSERT INTO `manufacturers` VALUES (1606, 'FBR', 'FISHBONE OFFROAD', 'fishbone-offroad');
INSERT INTO `manufacturers` VALUES (1607, 'GNR', 'GENRIGHT', 'genright');
INSERT INTO `manufacturers` VALUES (1608, 'GRR', 'GENESIS OFF ROAD', 'genesis-off-road');
INSERT INTO `manufacturers` VALUES (1609, 'HYL', 'HYLINE OFF ROAD', 'hyline-off-road');
INSERT INTO `manufacturers` VALUES (1610, 'K0U', 'KOOKS HEADERS & EXHAUST', 'kooks-headers-exhaust');
INSERT INTO `manufacturers` VALUES (1611, 'OSI', 'O\'REILLY/STI', 'o-reilly-sti');
INSERT INTO `manufacturers` VALUES (1612, 'OST', 'O\'REILLY/STD', 'o-reilly-std');
INSERT INTO `manufacturers` VALUES (1613, 'RDA', 'ROAD ARMOR', 'road-armor');
INSERT INTO `manufacturers` VALUES (1614, 'RGA', 'REVOLUTION GEAR & AXLE', 'revolution-gear-axle');
INSERT INTO `manufacturers` VALUES (1615, 'RIP', 'RIPP SUPERCHARGERS', 'ripp-superchargers');
INSERT INTO `manufacturers` VALUES (1616, 'RKR', 'ROCK KRAWLER', 'rock-krawler');
INSERT INTO `manufacturers` VALUES (1617, 'RMN', 'REMMEN', 'remmen');
INSERT INTO `manufacturers` VALUES (1618, 'SOD', 'SPOD', 'spod');
INSERT INTO `manufacturers` VALUES (1619, 'USI', 'UNDER THE SUN INSERTS', 'under-the-sun-inserts');
INSERT INTO `manufacturers` VALUES (1620, '6U1', 'PB/VALUEGRADE BATTERIES', 'pb-valuegrade-batteries');
INSERT INTO `manufacturers` VALUES (1621, 'AUE', 'AUTOLECTRA INC.', 'autolectra-inc');
INSERT INTO `manufacturers` VALUES (1622, 'D1Q', 'DUCK COVERS', 'duck-covers');
INSERT INTO `manufacturers` VALUES (1623, 'DBD', 'DIRTBOUND OFF ROAD', 'dirtbound-off-road');
INSERT INTO `manufacturers` VALUES (1624, 'FL2', 'FLAMING RIVER', 'flaming-river');
INSERT INTO `manufacturers` VALUES (1625, 'HLE', 'HOLLEY EFI', 'holley-efi');
INSERT INTO `manufacturers` VALUES (1626, 'JMP', 'J&M PRODUCTS', 'j-m-products');
INSERT INTO `manufacturers` VALUES (1627, 'OFT', 'PRECISION FUEL TANKS AND STRAPS', 'precision-fuel-tanks-and-straps');
INSERT INTO `manufacturers` VALUES (1628, 'S3S', 'STEER SMARTS', 'steer-smarts');
INSERT INTO `manufacturers` VALUES (1629, 'TGL', 'TOP GLASS', 'top-glass');
INSERT INTO `manufacturers` VALUES (1630, 'WVE', 'WVE', 'wve');
INSERT INTO `manufacturers` VALUES (1631, 'B1F', 'BAER BRAKE SYSTEMS', 'baer-brake-systems');
INSERT INTO `manufacturers` VALUES (1632, 'CC1', 'COLD-CASE RADIATORS', 'cold-case-radiators');
INSERT INTO `manufacturers` VALUES (1633, 'CGO', 'CARGOEASE', 'cargoease');
INSERT INTO `manufacturers` VALUES (1634, 'FI1', 'FLOG INDUSTRIES', 'flog-industries');
INSERT INTO `manufacturers` VALUES (1635, 'ICC', 'ICON COMPOSITES', 'icon-composites');
INSERT INTO `manufacturers` VALUES (1636, 'JCR', 'JCR OFFROAD PRODUCTS', 'jcr-offroad-products');
INSERT INTO `manufacturers` VALUES (1637, 'N0H', 'NITRO GEAR & AXLE', 'nitro-gear-axle');
INSERT INTO `manufacturers` VALUES (1638, 'PA4', 'PERFORMANCE AUTOMATIC INC.', 'performance-automatic-inc');
INSERT INTO `manufacturers` VALUES (1639, 'PC1', 'PEDAL COMMANDER', 'pedal-commander');
INSERT INTO `manufacturers` VALUES (1640, 'RFX', 'REMFLEX', 'remflex');
INSERT INTO `manufacturers` VALUES (1641, 'RMK', 'ROMIK', 'romik');
INSERT INTO `manufacturers` VALUES (1642, 'TFX', 'TERAFLEX', 'teraflex');
INSERT INTO `manufacturers` VALUES (1643, 'V76', 'CARQUEST/RAIN-X WIPER BLADES', 'carquest-rain-x-wiper-blades');
INSERT INTO `manufacturers` VALUES (1644, 'V77', 'CARQUEST CV SHAFTS', 'carquest-cv-shafts');
INSERT INTO `manufacturers` VALUES (1645, 'PW5', 'POWER TANK', 'power-tank');
INSERT INTO `manufacturers` VALUES (1667, 'C2C', 'FBS CANADA', 'fbs-canada');
INSERT INTO `manufacturers` VALUES (1668, '7IH', 'FOX RACING SHOX', 'fox-racing-shox');
INSERT INTO `manufacturers` VALUES (1669, 'CR2', 'C&R RACING', 'c-r-racing');
INSERT INTO `manufacturers` VALUES (1670, 'UVR', 'FLEXSHADE UVR SUNSHIELD', 'flexshade-uvr-sunshield');
INSERT INTO `manufacturers` VALUES (1671, 'BWK', 'BUSHWACKER', 'bushwacker');
INSERT INTO `manufacturers` VALUES (1672, 'EXT', 'EXTANG', 'extang');
INSERT INTO `manufacturers` VALUES (1673, 'XBL', 'ALL SALES MANUFACTURING', 'all-sales-manufacturing');
INSERT INTO `manufacturers` VALUES (1674, 'XBX', 'AEROQUIP', 'aeroquip');
INSERT INTO `manufacturers` VALUES (1675, 'XDD', 'BAK INDUSTRIES', 'bak-industries');
INSERT INTO `manufacturers` VALUES (1676, 'XDQ', 'CARR', 'carr');
INSERT INTO `manufacturers` VALUES (1677, 'XFL', 'CROWER', 'crower');
INSERT INTO `manufacturers` VALUES (1678, 'XFP', 'COVERKING', 'coverking');
INSERT INTO `manufacturers` VALUES (1679, 'XOU', 'NICKSON', 'nickson');
INSERT INTO `manufacturers` VALUES (1680, 'XWU', 'TRUXEDO', 'truxedo');
INSERT INTO `manufacturers` VALUES (1681, '6BJ', 'Borgeson', 'borgeson');
INSERT INTO `manufacturers` VALUES (1682, '6DW', 'EXEDY RACING CLUTCH', 'exedy-racing-clutch');
INSERT INTO `manufacturers` VALUES (1683, '6FE', 'Mishimoto Automotive', 'mishimoto-automotive');
INSERT INTO `manufacturers` VALUES (1684, '6LJ', 'UWS', 'uws');
INSERT INTO `manufacturers` VALUES (1685, 'OMK', 'OMNICRAFT', 'omnicraft');
INSERT INTO `manufacturers` VALUES (1686, 'TD5', 'THUNDERER TIRES', 'thunderer-tires');
INSERT INTO `manufacturers` VALUES (1687, 'RDM', 'RADIUM ENGINEERING', 'radium-engineering');
INSERT INTO `manufacturers` VALUES (1688, 'DRD', 'DRAKE OFF ROAD', 'drake-off-road');
INSERT INTO `manufacturers` VALUES (1689, 'PP5', 'PPE DIESEL', 'ppe-diesel');
INSERT INTO `manufacturers` VALUES (1690, 'AO1', 'ALLIANCE OE BOSCH - FUEL PUMPS', 'alliance-oe-bosch-fuel-pumps');
INSERT INTO `manufacturers` VALUES (1691, 'AO2', 'ALLIANCE OE DELPHI - FUEL PUMPS', 'alliance-oe-delphi-fuel-pumps');
INSERT INTO `manufacturers` VALUES (1692, 'AO3', 'ALLIANCE OE DENSO - FUEL PUMPS', 'alliance-oe-denso-fuel-pumps');
INSERT INTO `manufacturers` VALUES (1693, 'EN0', 'ENGO', 'engo');
INSERT INTO `manufacturers` VALUES (1694, 'FVD', 'FMP OE DENSO O2 SENSORS', 'fmp-oe-denso-o2-sensors');
INSERT INTO `manufacturers` VALUES (1695, 'LMX', 'LOMAX', 'lomax');
INSERT INTO `manufacturers` VALUES (1696, 'LU0', 'LUVERNE', 'luverne');
INSERT INTO `manufacturers` VALUES (1697, 'NDM', 'NTH DEGREE MOBILITY', 'nth-degree-mobility');
INSERT INTO `manufacturers` VALUES (1698, 'SE5', 'SPARTA EVOLUTION', 'sparta-evolution');
INSERT INTO `manufacturers` VALUES (1699, 'DP1', 'DORMAN PREMIUM', 'dorman-premium');
INSERT INTO `manufacturers` VALUES (1700, 'FA3', 'FAE SENSORS AND SWITCHES', 'fae-sensors-and-switches');
INSERT INTO `manufacturers` VALUES (1701, 'FC0', 'FAIRCHILD INDUSTRIES', 'fairchild-industries');
INSERT INTO `manufacturers` VALUES (1702, 'LLR', 'LUBE LOCKER', 'lube-locker');
INSERT INTO `manufacturers` VALUES (1703, 'MB1', 'MAS BASE', 'mas-base');
INSERT INTO `manufacturers` VALUES (1704, 'MPH', 'MACPHERSON', 'macpherson');
INSERT INTO `manufacturers` VALUES (1705, 'Q2A', 'QUALITY AUTO ACCESSORIES', 'quality-auto-accessories');
INSERT INTO `manufacturers` VALUES (1706, 'QBM', 'CARQUEST/BLUE MASS AIR FLOW SENSORS', 'carquest-blue-mass-air-flow-sensors');
INSERT INTO `manufacturers` VALUES (1707, 'QHE', 'CARQUEST HEAVY DUTY ELECTRICAL', 'carquest-heavy-duty-electrical');
INSERT INTO `manufacturers` VALUES (1708, 'QWO', 'CARQUEST WATER OUTLETS', 'carquest-water-outlets');
INSERT INTO `manufacturers` VALUES (1709, 'TNT', 'TNT CUSTOMS', 'tnt-customs');
INSERT INTO `manufacturers` VALUES (1710, 'V78', 'ADVANCE/AUTOCRAFT', 'advance-autocraft');
INSERT INTO `manufacturers` VALUES (1711, 'FV0', 'FVP CALIPERS', 'fvp-calipers');
INSERT INTO `manufacturers` VALUES (1712, 'P2T', 'PRECISION FUEL TANKS', 'precision-fuel-tanks');
INSERT INTO `manufacturers` VALUES (1713, 'RL0', 'ROLL-N-LOCK', 'roll-n-lock');
INSERT INTO `manufacturers` VALUES (1714, 'S6D', 'STAMPEDE', 'stampede');
INSERT INTO `manufacturers` VALUES (1715, 'T3A', 'TALON BRAKE BOOSTERS', 'talon-brake-boosters');
INSERT INTO `manufacturers` VALUES (1716, 'T3B', 'TALON BRAKE MASTER CYLINDERS & RESERVOIR', 'talon-brake-master-cylinders-reservoir');
INSERT INTO `manufacturers` VALUES (1717, 'TDF', 'TSS DASH4', 'tss-dash4');
INSERT INTO `manufacturers` VALUES (1718, 'TNP', 'TONNO PRO', 'tonno-pro');
INSERT INTO `manufacturers` VALUES (1719, 'X1B', 'XLB', 'xlb');
INSERT INTO `manufacturers` VALUES (1720, 'X4R', 'XFORCE', 'xforce');
INSERT INTO `manufacturers` VALUES (1721, 'CT5', 'PRO-SERIES OE PLUS DRUMS AND ROTORS', 'pro-series-oe-plus-drums-and-rotors');
INSERT INTO `manufacturers` VALUES (1722, 'CT7', 'MOTOMASTER OE PLUS DRUMS AND ROTORS', 'motomaster-oe-plus-drums-and-rotors');
INSERT INTO `manufacturers` VALUES (1723, 'DRP', 'DEVIANT RACE PARTS', 'deviant-race-parts');
INSERT INTO `manufacturers` VALUES (1724, 'ETG', 'ELONGATOR TAILGATE', 'elongator-tailgate');
INSERT INTO `manufacturers` VALUES (1725, 'HUP', 'HEADS UP', 'heads-up');
INSERT INTO `manufacturers` VALUES (1726, 'JAC', 'JORGEN AUTOMOTIVE-JAC RACK U.S.A.', 'jorgen-automotive-jac-rack-u-s-a');
INSERT INTO `manufacturers` VALUES (1727, 'QBP', 'QBP INTERNATIONAL', 'qbp-international');
INSERT INTO `manufacturers` VALUES (1728, 'ROR', 'RUGGED OFF ROAD', 'rugged-off-road');
INSERT INTO `manufacturers` VALUES (1729, 'RSE', 'ROCK-SLIDE ENGINEERING', 'rock-slide-engineering');
INSERT INTO `manufacturers` VALUES (1730, 'BBT', 'OE-TURBOPOWER', 'oe-turbopower');
INSERT INTO `manufacturers` VALUES (1731, 'PB1', 'PACBRAKE', 'pacbrake');
INSERT INTO `manufacturers` VALUES (1732, 'PS0', 'PRO-SERIES OE PLUS', 'pro-series-oe-plus');
INSERT INTO `manufacturers` VALUES (1733, 'RTS', 'RTS', 'rts');
INSERT INTO `manufacturers` VALUES (1734, 'TUD', 'TOUCH UP DIRECT', 'touch-up-direct');
INSERT INTO `manufacturers` VALUES (1735, 'BOF', 'BOSCH FOCUS', 'bosch-focus');
INSERT INTO `manufacturers` VALUES (1736, 'EX1', 'EXIDE BATTERIES', 'exide-batteries');
INSERT INTO `manufacturers` VALUES (1737, 'F2R', 'FIRESTONE RIDE-RITE', 'firestone-ride-rite');
INSERT INTO `manufacturers` VALUES (1738, 'IF1', 'INFINITI', 'infiniti');
INSERT INTO `manufacturers` VALUES (1739, 'IF2', 'INFINITI MAINTENANCE ADVANTAGE', 'infiniti-maintenance-advantage');
INSERT INTO `manufacturers` VALUES (1740, 'ISK', 'ISKY RACING CAMS', 'isky-racing-cams');
INSERT INTO `manufacturers` VALUES (1741, 'NS1', 'NISSAN', 'nissan');
INSERT INTO `manufacturers` VALUES (1742, 'TQS', 'TORQSTORM BILLET SUPERCHARGERS', 'torqstorm-billet-superchargers');
INSERT INTO `manufacturers` VALUES (1743, '6R1', 'PB/CHAMPION', 'pb-champion');
INSERT INTO `manufacturers` VALUES (1744, '6R3', 'AUTO PLUS/PROSTOP', 'auto-plus-prostop');
INSERT INTO `manufacturers` VALUES (1745, 'AT5', 'AUTOTECNICA', 'autotecnica');
INSERT INTO `manufacturers` VALUES (1746, 'CC2', 'CERAMICOOL', 'ceramicool');
INSERT INTO `manufacturers` VALUES (1747, 'CDC', 'CLASSIC DESIGN CONCEPTS (CDC)', 'classic-design-concepts-cdc');
INSERT INTO `manufacturers` VALUES (1748, 'ILT', 'PREFORMED VEHICLE LINES & BRAKE PRODUCTS', 'preformed-vehicle-lines-brake-products');
INSERT INTO `manufacturers` VALUES (1749, 'P1U', 'PRO COMP SUSPENSION', 'pro-comp-suspension');
INSERT INTO `manufacturers` VALUES (1750, 'TCF', 'TRADECRAFT', 'tradecraft');
INSERT INTO `manufacturers` VALUES (1751, 'FR1', 'FULLRIVER BATTERY', 'fullriver-battery');
INSERT INTO `manufacturers` VALUES (1752, 'SVR', 'SAVIOR PRODUCTS', 'savior-products');
INSERT INTO `manufacturers` VALUES (1753, 'HBF', 'HUBB FILTERS', 'hubb-filters');
INSERT INTO `manufacturers` VALUES (1754, 'IAS', 'INFINITY SERIES AXLE SHAFTS', 'infinity-series-axle-shafts');
INSERT INTO `manufacturers` VALUES (1755, 'JEE', 'JE ENGINEERING', 'je-engineering');
INSERT INTO `manufacturers` VALUES (1757, 'NP3', 'NAPA/SOLENOIDS-RAY', 'napa-solenoids-ray');
INSERT INTO `manufacturers` VALUES (1758, 'ZQ5', 'AUTOZONE/POWERFLOW', 'autozone-powerflow');
INSERT INTO `manufacturers` VALUES (1759, 'ZDH', 'AUTOZONE/DORMAN BATTERIES', 'autozone-dorman-batteries');
INSERT INTO `manufacturers` VALUES (1760, 'ZMP', 'AUTOZONE/MOPAR', 'autozone-mopar');
INSERT INTO `manufacturers` VALUES (1761, 'ZTW', 'AUTOZONE/TRW', 'autozone-trw');
INSERT INTO `manufacturers` VALUES (1762, 'QBO', 'CARQUEST CANADA OPTIMA', 'carquest-canada-optima');
INSERT INTO `manufacturers` VALUES (1763, 'OTU', 'OUTLAW UTILITY', 'outlaw-utility');
INSERT INTO `manufacturers` VALUES (1764, '4DW', 'DIP WHEELS', 'dip-wheels');
INSERT INTO `manufacturers` VALUES (1765, 'SAX', 'SOLID AXLE', 'solid-axle');
INSERT INTO `manufacturers` VALUES (1766, 'ID1', 'IMPORT DIRECT FUEL PUMPS', 'import-direct-fuel-pumps');
INSERT INTO `manufacturers` VALUES (1767, 'ZM2', 'AUTOZONE/PULSTAR', 'autozone-pulstar');
INSERT INTO `manufacturers` VALUES (1768, 'N9H', 'NAPA/HUBB FILTERS-HUB', 'napa-hubb-filters-hub');
INSERT INTO `manufacturers` VALUES (1769, 'ZAB', 'AUTOZONE/ DURALAST-AERO BLADES', 'autozone-duralast-aero-blades');
INSERT INTO `manufacturers` VALUES (1770, 'ZPP', 'AUTOZONE/PRO POWER', 'autozone-pro-power');
INSERT INTO `manufacturers` VALUES (1771, 'S8M', 'STAB MOTORSPORTS', 'stab-motorsports');
INSERT INTO `manufacturers` VALUES (1772, 'INA', 'INSANE AUDIO', 'insane-audio');
INSERT INTO `manufacturers` VALUES (1773, 'ZL3', 'AUTOZONE/ VALUCRAFT-OWI', 'autozone-valucraft-owi');
INSERT INTO `manufacturers` VALUES (1774, 'ZN6', 'AUTOZONE/DURALAST GOLD-EAST PENN', 'autozone-duralast-gold-east-penn');
INSERT INTO `manufacturers` VALUES (1775, 'ZQ6', 'AUTOZONE/PLASTICOLOR', 'autozone-plasticolor');
INSERT INTO `manufacturers` VALUES (1776, 'ZT8', 'AUTOZONE/ DURALAST-PLATINUM-JCI', 'autozone-duralast-platinum-jci');
INSERT INTO `manufacturers` VALUES (1777, 'ZT9', 'AUTOZONE/ VALUCRAFT-JCI', 'autozone-valucraft-jci');
INSERT INTO `manufacturers` VALUES (1778, 'NE3', 'NAPA/E3 SPARK PLUGS-EEE', 'napa-e3-spark-plugs-eee');
INSERT INTO `manufacturers` VALUES (1779, 'ZUJ', 'AUTOZONE/JAM STRAIT', 'autozone-jam-strait');
INSERT INTO `manufacturers` VALUES (1780, 'ZET', 'AUTOZONE/ECONOCRAFT', 'autozone-econocraft');
INSERT INTO `manufacturers` VALUES (1781, 'ZRX', 'AUTOZONE/RAIN X QUANTUM', 'autozone-rain-x-quantum');
INSERT INTO `manufacturers` VALUES (1782, 'XVS', 'SHERWOOD DASH', 'sherwood-dash');
INSERT INTO `manufacturers` VALUES (1783, 'ZTO', 'AUTOZONE/ DURALAST-TRICO', 'autozone-duralast-trico');
INSERT INTO `manufacturers` VALUES (1784, 'NHB', 'NAPA/HYBRID BATTERIES-NHB', 'napa-hybrid-batteries-nhb');
INSERT INTO `manufacturers` VALUES (1785, 'ZPA', 'AUTOZONE/PRO POWER AGM', 'autozone-pro-power-agm');
INSERT INTO `manufacturers` VALUES (1786, 'AA8', 'ADVANCED ACCESSORY CONCEPTS', 'advanced-accessory-concepts');
INSERT INTO `manufacturers` VALUES (1787, 'PYP', 'PRODIGY PERFORMANCE', 'prodigy-performance');
INSERT INTO `manufacturers` VALUES (1788, 'EZL', 'EZ LIP', 'ez-lip');
INSERT INTO `manufacturers` VALUES (1789, 'HMO', 'HEAVY METAL OFF ROAD', 'heavy-metal-off-road');
INSERT INTO `manufacturers` VALUES (1790, '4IT', 'ION TRAILER', 'ion-trailer');
INSERT INTO `manufacturers` VALUES (1791, 'FCE', 'CHAMPION BATTERIES ENERGIZER', 'champion-batteries-energizer');
INSERT INTO `manufacturers` VALUES (1792, 'NA9', 'NAPIER ENTERPRISES', 'napier-enterprises');
INSERT INTO `manufacturers` VALUES (1793, 'FCO', 'CHAMPION BATTERIES OPTIMA', 'champion-batteries-optima');
INSERT INTO `manufacturers` VALUES (1794, 'AC3', 'NAPA/AC SPARK PLUGS-AC', 'napa-ac-spark-plugs-ac');
INSERT INTO `manufacturers` VALUES (1795, 'AL3', 'NAPA/AUTOLITE SPK PLUGS-ASP', 'napa-autolite-spk-plugs-asp');
INSERT INTO `manufacturers` VALUES (1796, 'BE3', 'NAPA/BELDEN-BEL', 'napa-belden-bel');
INSERT INTO `manufacturers` VALUES (1797, 'CH3', 'NAPA/CHAMPION SPK PLUGS-CHA', 'napa-champion-spk-plugs-cha');
INSERT INTO `manufacturers` VALUES (1798, 'DP3', 'NAPA/DELPHI AC COMPRESS-DAC', 'napa-delphi-ac-compress-dac');
INSERT INTO `manufacturers` VALUES (1799, 'LM3', 'NAPA/LAMPS-LMP', 'napa-lamps-lmp');
INSERT INTO `manufacturers` VALUES (1800, 'N31', 'NAPA/ADAPTIVE ONE BRK PADS-ADO', 'napa-adaptive-one-brk-pads-ado');
INSERT INTO `manufacturers` VALUES (1801, 'N32', 'NAPA/ADVANTAGE BULBS-AVB', 'napa-advantage-bulbs-avb');
INSERT INTO `manufacturers` VALUES (1802, 'N34', 'NAPA/AIR PUMPS-NAP', 'napa-air-pumps-nap');
INSERT INTO `manufacturers` VALUES (1803, 'N38', 'NAPA/BATTERIES-BAT', 'napa-batteries-bat');
INSERT INTO `manufacturers` VALUES (1804, 'N3A', 'NAPA/CV BOOTS-CVB', 'napa-cv-boots-cvb');
INSERT INTO `manufacturers` VALUES (1805, 'N3B', 'NAPA/DRIVE BELT KIT-DBK', 'napa-drive-belt-kit-dbk');
INSERT INTO `manufacturers` VALUES (1806, 'N3C', 'NAPA/BATTERIES REGULAR-BAT', 'napa-batteries-regular-bat');
INSERT INTO `manufacturers` VALUES (1807, 'N3D', 'NAPA/PROFORMER JOINT-NPJ', 'napa-proformer-joint-npj');
INSERT INTO `manufacturers` VALUES (1808, 'N3E', 'NAPA/PROKING MAN TRANS-TORQUE CONV-PMT', 'napa-proking-man-trans-torque-conv-pmt');
INSERT INTO `manufacturers` VALUES (1809, 'N3H', 'NAPA TRU-STOP CALIPERS-TS', 'napa-tru-stop-calipers-ts');
INSERT INTO `manufacturers` VALUES (1810, 'N3J', 'NAPA/ULTRA PREMIUM CALIPERS-UP', 'napa-ultra-premium-calipers-up');
INSERT INTO `manufacturers` VALUES (1811, 'N3M', 'NAPA/CONDENSERS-MO', 'napa-condensers-mo');
INSERT INTO `manufacturers` VALUES (1812, 'N3P', 'NAPA/HEATER CORES-NHS', 'napa-heater-cores-nhs');
INSERT INTO `manufacturers` VALUES (1813, 'N3U', 'NAPA/WATER PUMP KITS-WPK', 'napa-water-pump-kits-wpk');
INSERT INTO `manufacturers` VALUES (1814, 'N40', 'NAPA/BATTERY CABLES-CBL', 'napa-battery-cables-cbl');
INSERT INTO `manufacturers` VALUES (1815, 'N45', 'NAPA/COLD POWER-NCC', 'napa-cold-power-ncc');
INSERT INTO `manufacturers` VALUES (1816, 'N55', 'NAPA/FLASHERS-NF', 'napa-flashers-nf');
INSERT INTO `manufacturers` VALUES (1817, 'N58', 'NAPA/GROTE-GRO', 'napa-grote-gro');
INSERT INTO `manufacturers` VALUES (1818, 'N61', 'NAPA/LIGHTING-LIT', 'napa-lighting-lit');
INSERT INTO `manufacturers` VALUES (1819, 'N64', 'NAPA/MILEAGE PLUS BELDEN-MPB', 'napa-mileage-plus-belden-mpb');
INSERT INTO `manufacturers` VALUES (1820, 'N66', 'NAPA/MILEAGE PLUS FUEL-MPF', 'napa-mileage-plus-fuel-mpf');
INSERT INTO `manufacturers` VALUES (1821, 'N67', 'NAPA/MILEAGE PLUS WIRES-MPW', 'napa-mileage-plus-wires-mpw');
INSERT INTO `manufacturers` VALUES (1822, 'N69', 'NAPA/MOORES CYLINDER HEAD-MCY', 'napa-moores-cylinder-head-mcy');
INSERT INTO `manufacturers` VALUES (1823, 'N72', 'NAPA/NEW DISTRIBUTORS-NND', 'napa-new-distributors-nnd');
INSERT INTO `manufacturers` VALUES (1824, 'N74', 'NAPA/NGK WIRES-NGW', 'napa-ngk-wires-ngw');
INSERT INTO `manufacturers` VALUES (1825, 'N78', 'NAPA/POWERGRIP BELTS HOSE-PBH', 'napa-powergrip-belts-hose-pbh');
INSERT INTO `manufacturers` VALUES (1826, 'N81', 'NAPA/PROFORMER BEARINGS-PGB', 'napa-proformer-bearings-pgb');
INSERT INTO `manufacturers` VALUES (1827, 'N83', 'NAPA/PROSELECT FILTERS-SFI', 'napa-proselect-filters-sfi');
INSERT INTO `manufacturers` VALUES (1828, 'N85', 'NAPA/RADIATOR HOSE KITS-RHK', 'napa-radiator-hose-kits-rhk');
INSERT INTO `manufacturers` VALUES (1829, 'N87', 'NAPA/RARE PARTS CHASSIS-RPC', 'napa-rare-parts-chassis-rpc');
INSERT INTO `manufacturers` VALUES (1830, 'N95', 'NAPA/REMAN DISTRIBUTORS-NRD', 'napa-reman-distributors-nrd');
INSERT INTO `manufacturers` VALUES (1831, 'N98', 'NAPA/REMAN WATER PUMPS-WP', 'napa-reman-water-pumps-wp');
INSERT INTO `manufacturers` VALUES (1832, 'N99', 'NAPA/RESPONSE-RR', 'napa-response-rr');
INSERT INTO `manufacturers` VALUES (1833, 'NA1', 'NAPA/TRU FLOW WATER PUMPS-TFW', 'napa-tru-flow-water-pumps-tfw');
INSERT INTO `manufacturers` VALUES (1834, 'NA4', 'NAPA/VACUUM PUMPS-NVP', 'napa-vacuum-pumps-nvp');
INSERT INTO `manufacturers` VALUES (1835, 'NP4', 'NAPA/SOUNDMASTER MUFFLERS-SMU', 'napa-soundmaster-mufflers-smu');
INSERT INTO `manufacturers` VALUES (1836, 'NP7', 'NAPA/TRANSFER CASE MOTORS-TCM', 'napa-transfer-case-motors-tcm');
INSERT INTO `manufacturers` VALUES (1837, 'NPW', 'NAPA/WIPER BLADES-OWI', 'napa-wiper-blades-owi');
INSERT INTO `manufacturers` VALUES (1838, 'ST3', 'NAPA/THERMOSTATS-THM', 'napa-thermostats-thm');
INSERT INTO `manufacturers` VALUES (1839, 'UP3', 'NAPA/ULTRA PREMIUM BRAKE HOSE & CABLE-UP', 'napa-ultra-premium-brake-hose-cable-up');
INSERT INTO `manufacturers` VALUES (1840, 'N3T', 'NAPA/LOADED BRAKE CALIPERS-CAL', 'napa-loaded-brake-calipers-cal');
INSERT INTO `manufacturers` VALUES (1841, 'N4B', 'NAPA/ENGINE HEATERS-KAT', 'napa-engine-heaters-kat');
INSERT INTO `manufacturers` VALUES (1842, 'N4C', 'NAPA/NAPA DRUMS-NDA', 'napa-napa-drums-nda');
INSERT INTO `manufacturers` VALUES (1843, 'N4F', 'NAPA/RESPONSE COMPL ASSY-RRB', 'napa-response-compl-assy-rrb');
INSERT INTO `manufacturers` VALUES (1844, 'N4M', 'NAPA/WIRE PRODUCTS-NW', 'napa-wire-products-nw');
INSERT INTO `manufacturers` VALUES (1845, 'N4N', 'NAPA/RAIN X WIPER BLADES-RNX', 'napa-rain-x-wiper-blades-rnx');
INSERT INTO `manufacturers` VALUES (1846, 'CTY', 'CANADIAN TIRE MOTOMASTER', 'canadian-tire-motomaster');
INSERT INTO `manufacturers` VALUES (1847, 'N4T', 'NAPA/PLATINUM FILTERS-PFL', 'napa-platinum-filters-pfl');
INSERT INTO `manufacturers` VALUES (1848, 'N4U', 'NAPA/SILVER FILTERS-SFL', 'napa-silver-filters-sfl');
INSERT INTO `manufacturers` VALUES (1849, 'ZA2', 'AUTOZONE/ VALUCRAFT-BOSCH', 'autozone-valucraft-bosch');
INSERT INTO `manufacturers` VALUES (1850, 'ZA3', 'AUTOZONE/DURALAST GOLD CMAX-GRI', 'autozone-duralast-gold-cmax-gri');
INSERT INTO `manufacturers` VALUES (1851, 'ZA7', 'AUTOZONE/DURALAST WIRESET', 'autozone-duralast-wireset');
INSERT INTO `manufacturers` VALUES (1852, 'ZB4', 'AUTOZONE/BUSSMANN', 'autozone-bussmann');
INSERT INTO `manufacturers` VALUES (1853, 'ZB7', 'AUTOZONE/CRANKSHAFT REBUILDERS', 'autozone-crankshaft-rebuilders');
INSERT INTO `manufacturers` VALUES (1854, 'ZE8', 'AUTOZONE/HOPKINS', 'autozone-hopkins');
INSERT INTO `manufacturers` VALUES (1855, 'ZK3', 'AUTOZONE/AEM', 'autozone-aem');
INSERT INTO `manufacturers` VALUES (1856, 'ZSY', 'AUTOZONE/SYLVANIA', 'autozone-sylvania');
INSERT INTO `manufacturers` VALUES (1857, 'ZL1', 'AUTOZONE/ VALUCRAFT-BRAKE PARTS', 'autozone-valucraft-brake-parts');
INSERT INTO `manufacturers` VALUES (1858, 'ZL4', 'AUTOZONE/ DURALAST-AUTOMOTIVE DISTRIBS', 'autozone-duralast-automotive-distribs');
INSERT INTO `manufacturers` VALUES (1859, 'ZL6', 'AUTOZONE/ DURALAST-FELPRO', 'autozone-duralast-felpro');
INSERT INTO `manufacturers` VALUES (1860, 'ZL7', 'AUTOZONE/ VALUCRAFT-UNITED PARTS', 'autozone-valucraft-united-parts');
INSERT INTO `manufacturers` VALUES (1861, 'ZM5', 'AUTOZONE/ DURALAST-DAWS MFG', 'autozone-duralast-daws-mfg');
INSERT INTO `manufacturers` VALUES (1862, 'ZM8', 'AUTOZONE/ VALUCRAFT-GMB', 'autozone-valucraft-gmb');
INSERT INTO `manufacturers` VALUES (1863, 'ZN1', 'AUTOZONE/ DURALAST-CST', 'autozone-duralast-cst');
INSERT INTO `manufacturers` VALUES (1864, 'ZN2', 'AUTOZONE/ VALUCRAFT-CST', 'autozone-valucraft-cst');
INSERT INTO `manufacturers` VALUES (1865, 'ZN3', 'AUTOZONE/ DURALAST-CHAMP LABS', 'autozone-duralast-champ-labs');
INSERT INTO `manufacturers` VALUES (1866, 'ZN4', 'AUTOZONE/ VALUCRAFT-CHAMP LABS', 'autozone-valucraft-champ-labs');
INSERT INTO `manufacturers` VALUES (1867, 'ZN5', 'AUTOZONE/ DURALAST-EAST PENN', 'autozone-duralast-east-penn');
INSERT INTO `manufacturers` VALUES (1868, 'ZP2', 'AUTOZONE/DURALAST GOLD-MPA', 'autozone-duralast-gold-mpa');
INSERT INTO `manufacturers` VALUES (1869, 'ZP3', 'AUTOZONE/ DURALAST-RICH PORTER TECH', 'autozone-duralast-rich-porter-tech');
INSERT INTO `manufacturers` VALUES (1870, 'ZP6', 'AUTOZONE/ DURALAST-CONI-SEAL', 'autozone-duralast-coni-seal');
INSERT INTO `manufacturers` VALUES (1871, 'ZP9', 'AUTOZONE/ZEX', 'autozone-zex');
INSERT INTO `manufacturers` VALUES (1872, 'ZQ0', 'AUTOZONE/PAINLESS WIRING', 'autozone-painless-wiring');
INSERT INTO `manufacturers` VALUES (1873, 'ZQ1', 'AUTOZONE/STANDARD FLYWHEEL', 'autozone-standard-flywheel');
INSERT INTO `manufacturers` VALUES (1874, 'ZQ2', 'AUTOZONE/RUSSELL', 'autozone-russell');
INSERT INTO `manufacturers` VALUES (1875, 'ZQ4', 'AUTOZONE/ROCKLAND', 'autozone-rockland');
INSERT INTO `manufacturers` VALUES (1876, 'ZQ9', 'AUTOZONE/PFC', 'autozone-pfc');
INSERT INTO `manufacturers` VALUES (1877, 'ZR1', 'AUTOZONE/PERCY\'S', 'autozone-percy-s');
INSERT INTO `manufacturers` VALUES (1878, 'ZR4', 'AUTOZONE/MSD', 'autozone-msd');
INSERT INTO `manufacturers` VALUES (1879, 'ZR7', 'AUTOZONE/METRO', 'autozone-metro');
INSERT INTO `manufacturers` VALUES (1880, 'ZS1', 'AUTOZONE/HOLLEY', 'autozone-holley');
INSERT INTO `manufacturers` VALUES (1881, 'ZS2', 'AUTOZONE/HIGHLAND', 'autozone-highland');
INSERT INTO `manufacturers` VALUES (1882, 'ZS3', 'AUTOZONE/FLOWMASTER', 'autozone-flowmaster');
INSERT INTO `manufacturers` VALUES (1883, 'ZS4', 'AUTOZONE/FLEX_A_LITE', 'autozone-flex_a_lite');
INSERT INTO `manufacturers` VALUES (1884, 'ZS5', 'AUTOZONE/FAILSAFE', 'autozone-failsafe');
INSERT INTO `manufacturers` VALUES (1885, 'ZS8', 'AUTOZONE/EUROPEAN LINE', 'autozone-european-line');
INSERT INTO `manufacturers` VALUES (1886, 'ZT2', 'AUTOZONE/ BETTER-BUILT', 'autozone-better-built');
INSERT INTO `manufacturers` VALUES (1887, 'ZT5', 'AUTOZONE/GOLD-WELLS', 'autozone-gold-wells');
INSERT INTO `manufacturers` VALUES (1888, 'ZL2', 'AUTOZONE/ DURALAST-OWI', 'autozone-duralast-owi');
INSERT INTO `manufacturers` VALUES (1889, 'ZM3', 'AUTOZONE/ VALUCRAFT-CADNA', 'autozone-valucraft-cadna');
INSERT INTO `manufacturers` VALUES (1890, 'N4Y', 'NAPA/WINDSHIELD PRODUCTS-WIP', 'napa-windshield-products-wip');
INSERT INTO `manufacturers` VALUES (1891, 'ZT6', 'AUTOZONE/DURALAST-JCI', 'autozone-duralast-jci');
INSERT INTO `manufacturers` VALUES (1892, 'ZT7', 'AUTOZONE/DURALAST GOLD-JCI', 'autozone-duralast-gold-jci');
INSERT INTO `manufacturers` VALUES (1893, 'ZU1', 'AUTOZONE/APC', 'autozone-apc');
INSERT INTO `manufacturers` VALUES (1894, 'ZU2', 'AUTOZONE/AUTOMETER', 'autozone-autometer');
INSERT INTO `manufacturers` VALUES (1895, 'ZU0', 'AUTOZONE/MOOG', 'autozone-moog');
INSERT INTO `manufacturers` VALUES (1896, 'ZU7', 'AUTOZONE/ DURALAST-GABRIEL', 'autozone-duralast-gabriel');
INSERT INTO `manufacturers` VALUES (1897, 'ZU9', 'AUTOZONE/TRUGRADE', 'autozone-trugrade');
INSERT INTO `manufacturers` VALUES (1898, 'N4Z', 'NAPA/SOLUTIONS INSTRUMENT CLUSTERS-NOE', 'napa-solutions-instrument-clusters-noe');
INSERT INTO `manufacturers` VALUES (1899, 'N5B', 'NAPA/POWER STEERING PARTS-NPS', 'napa-power-steering-parts-nps');
INSERT INTO `manufacturers` VALUES (1900, 'N5D', 'NAPA/SERVICE TOOLS-SER', 'napa-service-tools-ser');
INSERT INTO `manufacturers` VALUES (1901, 'ZV1', 'AUTOZONE/FRAM COMMERCIAL GRADE', 'autozone-fram-commercial-grade');
INSERT INTO `manufacturers` VALUES (1902, 'ZV2', 'AUTOZONE/AUTOLITE FILTERS', 'autozone-autolite-filters');
INSERT INTO `manufacturers` VALUES (1903, 'ZV3', 'AUTOZONE/NEEDA PARTS MANUFACTURING', 'autozone-needa-parts-manufacturing');
INSERT INTO `manufacturers` VALUES (1904, 'ZV5', 'AUTOZONE/ASC INDUSTRIES', 'autozone-asc-industries');
INSERT INTO `manufacturers` VALUES (1905, '7AY', 'AKRAPOVIC', 'akrapovic');
INSERT INTO `manufacturers` VALUES (1906, '7FX', 'DENNIS STUBBLEFIELD SALES', 'dennis-stubblefield-sales');
INSERT INTO `manufacturers` VALUES (1907, '7PW', 'ODYSSEY BATTERIES', 'odyssey-batteries');
INSERT INTO `manufacturers` VALUES (1908, 'MMA', 'MILE MARKER ACCESSORIES', 'mile-marker-accessories');
INSERT INTO `manufacturers` VALUES (1909, 'ZV7', 'AUTOZONE/EXEDY', 'autozone-exedy');
INSERT INTO `manufacturers` VALUES (1910, 'DST', 'DAYSTAR', 'daystar');
INSERT INTO `manufacturers` VALUES (1911, 'N5L', 'NAPA/DELPHI DIESEL DIRECT-DDD', 'napa-delphi-diesel-direct-ddd');
INSERT INTO `manufacturers` VALUES (1912, 'ZV8', 'AUTOZONE/ DURALAST-IPC', 'autozone-duralast-ipc');
INSERT INTO `manufacturers` VALUES (1913, 'ZAC', 'AUTOZONE/CRS', 'autozone-crs');
INSERT INTO `manufacturers` VALUES (1914, 'ZLM', 'AUTOZONE/MCLEOD', 'autozone-mcleod');
INSERT INTO `manufacturers` VALUES (1915, 'ZBL', 'AUTOZONE/BLAZER', 'autozone-blazer');
INSERT INTO `manufacturers` VALUES (1916, 'ZDR', 'AUTOZONE/ DURALAST-ROTOMASTER', 'autozone-duralast-rotomaster');
INSERT INTO `manufacturers` VALUES (1917, 'ZGR', 'AUTOZONE/DURALAST GOLD-REMY', 'autozone-duralast-gold-remy');
INSERT INTO `manufacturers` VALUES (1918, 'ZHI', 'AUTOZONE/HITACHI', 'autozone-hitachi');
INSERT INTO `manufacturers` VALUES (1919, 'ZVR', 'AUTOZONE/ VALUCRAFT-REMY', 'autozone-valucraft-remy');
INSERT INTO `manufacturers` VALUES (1920, 'ZGS', 'AUTOZONE/DURALAST GOLD-ROTORS', 'autozone-duralast-gold-rotors');
INSERT INTO `manufacturers` VALUES (1921, 'ZGD', 'AUTOZONE/DURALAST GOLD-SMP', 'autozone-duralast-gold-smp');
INSERT INTO `manufacturers` VALUES (1922, 'ZGU', 'AUTOZONE/DURALAST GOLD-U-JOINTS', 'autozone-duralast-gold-u-joints');
INSERT INTO `manufacturers` VALUES (1923, 'ZDC', 'AUTOZONE/ DURALAST-CALIPERS (BBB)', 'autozone-duralast-calipers-bbb');
INSERT INTO `manufacturers` VALUES (1924, 'ZOP', 'AUTOZONE/OPTIMA', 'autozone-optima');
INSERT INTO `manufacturers` VALUES (1925, 'XGL', 'DC SPORTS', 'dc-sports');
INSERT INTO `manufacturers` VALUES (1926, 'XXN', 'VOLANT', 'volant');
INSERT INTO `manufacturers` VALUES (1927, 'ZWN', 'DURALAST GT STREET', 'duralast-gt-street');
INSERT INTO `manufacturers` VALUES (1928, 'ZAI', 'AUTOZONE/AIRSEPT', 'autozone-airsept');
INSERT INTO `manufacturers` VALUES (1929, 'ZDU', 'AUTOZONE/ DURALAST-SPE', 'autozone-duralast-spe');
INSERT INTO `manufacturers` VALUES (1930, 'ZPH', 'AUTOZONE/PHILIPS', 'autozone-philips');
INSERT INTO `manufacturers` VALUES (1931, 'ZO1', 'AUTOZONE/ VALUCRAFT-CHASSIS', 'autozone-valucraft-chassis');
INSERT INTO `manufacturers` VALUES (1932, 'ZPL', 'AUTOZONE/PRO POWER PLUS', 'autozone-pro-power-plus');
INSERT INTO `manufacturers` VALUES (1933, 'ZPU', 'AUTOZONE/PRO POWER ULTRA', 'autozone-pro-power-ultra');
INSERT INTO `manufacturers` VALUES (1934, 'ZRP', 'AUTOZONE/DURALAST GOLD-RICH PORTER TECH', 'autozone-duralast-gold-rich-porter-tech');
INSERT INTO `manufacturers` VALUES (1935, 'ZVV', 'AUTOZONE/ VALUCRAFT-WELLS', 'autozone-valucraft-wells');
INSERT INTO `manufacturers` VALUES (1936, 'ZDX', 'AUTOZONE/DURALAST- MAX', 'autozone-duralast-max');
INSERT INTO `manufacturers` VALUES (1937, 'NAQ', 'NAPA/ARNOTT SUSPENSION-ARN', 'napa-arnott-suspension-arn');
INSERT INTO `manufacturers` VALUES (1938, 'ZAL', 'AUTOZONE/ DURALAST-REAR BLADES', 'autozone-duralast-rear-blades');
INSERT INTO `manufacturers` VALUES (1939, 'Z9S', 'AUTOZONE/SENSEN', 'autozone-sensen');
INSERT INTO `manufacturers` VALUES (1940, 'N1M', 'NAPA/PREMIUM MOTOR MOUNTS-DTT', 'napa-premium-motor-mounts-dtt');
INSERT INTO `manufacturers` VALUES (1941, 'N2G', 'NAPA/COLD POWER-GPI', 'napa-cold-power-gpi');
INSERT INTO `manufacturers` VALUES (1942, 'N2M', 'NAPA/DRIVE TECH MOUNTS-DTM', 'napa-drive-tech-mounts-dtm');
INSERT INTO `manufacturers` VALUES (1943, 'N9A', 'NAPA/ULTRA PREMIUM SEVERE DUTY ROTORS-UP', 'napa-ultra-premium-severe-duty-rotors-up');
INSERT INTO `manufacturers` VALUES (1944, 'Z9A', 'AUTOZONE/AIRLIFT', 'autozone-airlift');
INSERT INTO `manufacturers` VALUES (1945, 'Z9P', 'AUTOZONE/PUTCO LIGHTING', 'autozone-putco-lighting');
INSERT INTO `manufacturers` VALUES (1946, 'ZBG', 'AUTOZONE/BULLY DOG', 'autozone-bully-dog');
INSERT INTO `manufacturers` VALUES (1947, 'ZFL', 'AUTOZONE/FLOWTECH', 'autozone-flowtech');
INSERT INTO `manufacturers` VALUES (1948, 'ZRG', 'AUTOZONE/RAMPAGE', 'autozone-rampage');
INSERT INTO `manufacturers` VALUES (1949, 'NB4', 'NAPA/AKEBONO BRAKE PADS-AKE', 'napa-akebono-brake-pads-ake');
INSERT INTO `manufacturers` VALUES (1950, 'NB5', 'NAPA/BOSCH DIESEL PRODUCTS-BDP', 'napa-bosch-diesel-products-bdp');
INSERT INTO `manufacturers` VALUES (1951, 'Z9B', 'AUTOZONE/BESTOP', 'autozone-bestop');
INSERT INTO `manufacturers` VALUES (1952, 'ZBT', 'AUTOZONE/BOSTECH', 'autozone-bostech');
INSERT INTO `manufacturers` VALUES (1953, 'ZMG', 'AUTOZONE/MCGARD', 'autozone-mcgard');
INSERT INTO `manufacturers` VALUES (1954, 'ZPK', 'AUTOZONE/ DURALAST-PRO KING', 'autozone-duralast-pro-king');
INSERT INTO `manufacturers` VALUES (1955, 'N9C', 'NAPA/BOSCH ENGINE MANAGEMENT-BED', 'napa-bosch-engine-management-bed');
INSERT INTO `manufacturers` VALUES (1956, 'ZDF', 'AUTOZONE/DURALAST FLEX', 'autozone-duralast-flex');
INSERT INTO `manufacturers` VALUES (1957, 'N5U', 'NAPA/UJOINTS BY SKF', 'napa-ujoints-by-skf');
INSERT INTO `manufacturers` VALUES (1958, 'N9F', 'NAPA/NEW POWER STEERING', 'napa-new-power-steering');
INSERT INTO `manufacturers` VALUES (1959, 'MS1', 'MILESTAR TIRES', 'milestar-tires');
INSERT INTO `manufacturers` VALUES (1960, 'NBP', 'NAPA/FLEET BRAKE PARTS-FLT', 'napa-fleet-brake-parts-flt');
INSERT INTO `manufacturers` VALUES (1961, 'VTR', 'VITOUR TIRES', 'vitour-tires');
INSERT INTO `manufacturers` VALUES (1962, 'ZSI', 'AUTOZONE/SANTECH', 'autozone-santech');
INSERT INTO `manufacturers` VALUES (1963, 'T1B', 'VREDESTEIN TIRES', 'vredestein-tires');
INSERT INTO `manufacturers` VALUES (1964, 'T1A', 'ATTURO TIRES', 'atturo-tires');
INSERT INTO `manufacturers` VALUES (1965, '6BB', 'Backrack', 'backrack');
INSERT INTO `manufacturers` VALUES (1966, 'N9R', 'NAPA/ROTOMASTER TURBOCHARGERS-RTM', 'napa-rotomaster-turbochargers-rtm');
INSERT INTO `manufacturers` VALUES (1967, 'NBW', 'NAPA/BOXED WATER PUMP KITS', 'napa-boxed-water-pump-kits');
INSERT INTO `manufacturers` VALUES (1968, 'NCR', 'NAPA/CRANKSHAFT REBUILDERS', 'napa-crankshaft-rebuilders');
INSERT INTO `manufacturers` VALUES (1969, 'ZTC', 'AUTOZONE/STP_PREMIUM', 'autozone-stp_premium');
INSERT INTO `manufacturers` VALUES (1970, 'ZTE', 'AUTOZONE/ STP_EXTENDED _LIFE', 'autozone-stp_extended-_life');
INSERT INTO `manufacturers` VALUES (1971, 'ZDE', 'AUTOZONE/DURALAST ENHANCED OE', 'autozone-duralast-enhanced-oe');
INSERT INTO `manufacturers` VALUES (1972, 'NF2', 'NAPA/MANN+HUMMEL FILTERS-MAN', 'napa-mann-hummel-filters-man');
INSERT INTO `manufacturers` VALUES (1973, 'CLZ', 'CLAZZIO', 'clazzio');
INSERT INTO `manufacturers` VALUES (1974, 'ZPM', 'AUTOZONE/ZOOM PERFORMANCE', 'autozone-zoom-performance');
INSERT INTO `manufacturers` VALUES (1975, 'N2D', 'NAPA CANADA/DELPHI FUEL PUMPS - DFE', 'napa-canada-delphi-fuel-pumps-dfe');
INSERT INTO `manufacturers` VALUES (1976, 'ZUE', 'AUTOZONE/CARDONE_R&R C19', 'autozone-cardone_r-r-c19');
INSERT INTO `manufacturers` VALUES (1977, 'ZUF', 'AUTOZONE/CARDONE_CUSTOM_FX', 'autozone-cardone_custom_fx');
INSERT INTO `manufacturers` VALUES (1978, 'M7S', 'M7 SPEED', 'm7-speed');
INSERT INTO `manufacturers` VALUES (1979, 'PP8', 'PURE PERFORMANCE SUSPENSION', 'pure-performance-suspension');
INSERT INTO `manufacturers` VALUES (1980, 'R7F', 'ROTO-FAB', 'roto-fab');
INSERT INTO `manufacturers` VALUES (1981, 'FCB', 'CHAMPION BATTERIES', 'champion-batteries');
INSERT INTO `manufacturers` VALUES (1982, 'GFC', 'G-FORCE CROSSMEMBERS', 'g-force-crossmembers');
INSERT INTO `manufacturers` VALUES (1983, 'SS2', 'SUPERSPRINGS', 'supersprings');
INSERT INTO `manufacturers` VALUES (1984, 'AT1', 'AMERICAN TRAIL PRODUCTS', 'american-trail-products');
INSERT INTO `manufacturers` VALUES (1985, 'OZ1', 'OZ TUNER', 'oz-tuner');
INSERT INTO `manufacturers` VALUES (1986, 'QCP', 'CARQUEST CHASSIS PARTS', 'carquest-chassis-parts');
INSERT INTO `manufacturers` VALUES (1987, 'QHB', 'CARQUEST HUB BEARING', 'carquest-hub-bearing');
INSERT INTO `manufacturers` VALUES (1988, 'QPS', 'CARQUEST POWER STEERING PUMPS', 'carquest-power-steering-pumps');
INSERT INTO `manufacturers` VALUES (1989, '1CF', 'CLINCHED FLARES', 'clinched-flares');
INSERT INTO `manufacturers` VALUES (1990, 'C1F', 'CORSA PERFORMANCE', 'corsa-performance');
INSERT INTO `manufacturers` VALUES (1991, 'Q3F', 'CARQUEST/OIL AND FLUIDS', 'carquest-oil-and-fluids');
INSERT INTO `manufacturers` VALUES (1992, 'F1X', 'FILLER NECK SUPPLY CO', 'filler-neck-supply-co');
INSERT INTO `manufacturers` VALUES (1993, 'ZDD', 'AUTOZONE/DURALAST BATTERY ACC', 'autozone-duralast-battery-acc');
INSERT INTO `manufacturers` VALUES (1994, 'ZKA', 'AUTOZONE/ ACDELCO STARTER & ALTERNATOR', 'autozone-acdelco-starter-alternator');
INSERT INTO `manufacturers` VALUES (1995, 'ZSC', 'AUTOZONE/SPEC', 'autozone-spec');
INSERT INTO `manufacturers` VALUES (1996, 'ZVC', 'AUTOZONE/VALUECRAFT DI', 'autozone-valuecraft-di');
INSERT INTO `manufacturers` VALUES (1997, 'V88', 'ADVANCE/DIEHARD HD', 'advance-diehard-hd');
INSERT INTO `manufacturers` VALUES (1998, 'HEP', 'HEPU', 'hepu');
INSERT INTO `manufacturers` VALUES (1999, 'OP3', 'PRECISION FUEL PUMP', 'precision-fuel-pump');
INSERT INTO `manufacturers` VALUES (2000, 'MXB', 'MAX BILT', 'max-bilt');
INSERT INTO `manufacturers` VALUES (2001, 'SKT', 'STEELMAN WHEEL SOCKET', 'steelman-wheel-socket');
INSERT INTO `manufacturers` VALUES (2002, '914', '914RUBBER', '914rubber');
INSERT INTO `manufacturers` VALUES (2003, 'CF1', 'CERTIFIED FILTER', 'certified-filter');
INSERT INTO `manufacturers` VALUES (2004, 'DBH', 'DC BATTERY HUB', 'dc-battery-hub');
INSERT INTO `manufacturers` VALUES (2005, 'RDL', 'REEL DRIVELINE', 'reel-driveline');
INSERT INTO `manufacturers` VALUES (2006, 'NTP', 'NTP', 'ntp');
INSERT INTO `manufacturers` VALUES (2007, 'DEM', 'DAVICO MFG EXHAUST MANIFOLD', 'davico-mfg-exhaust-manifold');
INSERT INTO `manufacturers` VALUES (2008, 'SBC', 'SOUTH BEND CLUTCH', 'south-bend-clutch');
INSERT INTO `manufacturers` VALUES (2009, 'QBQ', 'CARQUEST CANADA BATTERIES', 'carquest-canada-batteries');
INSERT INTO `manufacturers` VALUES (2010, 'NLF', 'NO LIMIT FABRICATION', 'no-limit-fabrication');
INSERT INTO `manufacturers` VALUES (2011, 'DV8', 'DV8 OFFROAD', 'dv8-offroad');
INSERT INTO `manufacturers` VALUES (2012, 'ZDN', 'AUTOZONE/DURALAST NEW', 'autozone-duralast-new');
INSERT INTO `manufacturers` VALUES (2013, 'SUM', 'SUSPENSION MAXX', 'suspension-maxx');
INSERT INTO `manufacturers` VALUES (2014, 'MAB', 'MAX ADVANCED BRAKES', 'max-advanced-brakes');
INSERT INTO `manufacturers` VALUES (2015, 'ZBE', 'AUTOZONE/BOSCH ENVISION', 'autozone-bosch-envision');
INSERT INTO `manufacturers` VALUES (2016, 'SLP', 'SALERI WATER PUMPS', 'saleri-water-pumps');
INSERT INTO `manufacturers` VALUES (2017, 'PS1', 'PRP SEATS', 'prp-seats');
INSERT INTO `manufacturers` VALUES (2018, 'ANX', 'ANDERSON COMPOSITES', 'anderson-composites');
INSERT INTO `manufacturers` VALUES (2019, '4MZ', 'MAZZI', 'mazzi');
INSERT INTO `manufacturers` VALUES (2020, '4R1', 'RIDLER', 'ridler');
INSERT INTO `manufacturers` VALUES (2021, '6F0', 'MOMO', 'momo');
INSERT INTO `manufacturers` VALUES (2022, 'GME', 'GM ENGINES', 'gm-engines');
INSERT INTO `manufacturers` VALUES (2023, 'TI0', 'TI AUTOMOTIVE', 'ti-automotive');
INSERT INTO `manufacturers` VALUES (2024, 'V2A', 'ADVANCE/AUTOLITE', 'advance-autolite');
INSERT INTO `manufacturers` VALUES (2025, '4DL', 'DIRTY LIFE', 'dirty-life');
INSERT INTO `manufacturers` VALUES (2026, '2PE', 'PYPES PERFORMANCE EXHAUST', 'pypes-performance-exhaust');
INSERT INTO `manufacturers` VALUES (2027, '4CO', 'CALI OFFROAD', 'cali-offroad');
INSERT INTO `manufacturers` VALUES (2028, 'FAD', 'FOCUS AUTO DESIGN', 'focus-auto-design');
INSERT INTO `manufacturers` VALUES (2029, 'CSX', 'CST PERFORMANCE SUSPENSION', 'cst-performance-suspension');
INSERT INTO `manufacturers` VALUES (2030, '8PS', 'PEDDERS SUSPENSION', 'pedders-suspension');
INSERT INTO `manufacturers` VALUES (2031, '8ZA', 'AUTOZONE/SPC_ALIGNMENT', 'autozone-spc_alignment');
INSERT INTO `manufacturers` VALUES (2032, '4AI', 'ALLOY ION', 'alloy-ion');
INSERT INTO `manufacturers` VALUES (2033, 'X75', 'XRF - MAXIMUM CONTROL ARMS', 'xrf-maximum-control-arms');
INSERT INTO `manufacturers` VALUES (2034, '4TN', 'TOUREN', 'touren');
INSERT INTO `manufacturers` VALUES (2035, 'DP9', 'DECKED', 'decked');
INSERT INTO `manufacturers` VALUES (2036, 'BST', 'BESTOP', 'bestop');
INSERT INTO `manufacturers` VALUES (2037, 'GFB', 'GO FAST BITS', 'go-fast-bits');
INSERT INTO `manufacturers` VALUES (2038, 'MCE', 'MCE FENDERS', 'mce-fenders');
INSERT INTO `manufacturers` VALUES (2039, 'BWH', 'B&W TRAILER HITCHES', 'b-w-trailer-hitches');
INSERT INTO `manufacturers` VALUES (2040, '4M4', 'MAYHEM', 'mayhem');
INSERT INTO `manufacturers` VALUES (2041, 'PDA', 'PENDA', 'penda');
INSERT INTO `manufacturers` VALUES (2042, 'CRH', 'CARRICHS ACCESSORIES', 'carrichs-accessories');
INSERT INTO `manufacturers` VALUES (2043, 'RS1', 'RADFLO SUSPENSION TECHNOLOGY', 'radflo-suspension-technology');
INSERT INTO `manufacturers` VALUES (2044, 'HOP', 'HOPKINS MANUFACTURING', 'hopkins-manufacturing');
INSERT INTO `manufacturers` VALUES (2045, 'N65', 'NAPA/MILEAGE PLUS ELECTRICAL-MPE', 'napa-mileage-plus-electrical-mpe');
INSERT INTO `manufacturers` VALUES (2046, 'N73', 'NAPA/NEW ELECTRICAL-NNE', 'napa-new-electrical-nne');
INSERT INTO `manufacturers` VALUES (2047, 'N84', 'NAPA/RACK & PINION-NRP', 'napa-rack-pinion-nrp');
INSERT INTO `manufacturers` VALUES (2048, 'RN3', 'NAPA/RANCHO SUSP-RS', 'napa-rancho-susp-rs');
INSERT INTO `manufacturers` VALUES (2049, 'RA1', 'RAYBESTOS UNBRANDED', 'raybestos-unbranded');
INSERT INTO `manufacturers` VALUES (2050, 'ZA5', 'AUTOZONE/DURALAST GOLD-BOSCH', 'autozone-duralast-gold-bosch');
INSERT INTO `manufacturers` VALUES (2051, 'ZC6', 'AUTOZONE/ DURALAST-WELLS', 'autozone-duralast-wells');
INSERT INTO `manufacturers` VALUES (2052, 'ZE3', 'AUTOZONE/COMPRESSOR WORKS', 'autozone-compressor-works');
INSERT INTO `manufacturers` VALUES (2053, 'ZM0', 'AUTOZONE/ VALUCRAFT-FENWICK', 'autozone-valucraft-fenwick');
INSERT INTO `manufacturers` VALUES (2054, 'ZM7', 'AUTOZONE/ DURALAST-ASC', 'autozone-duralast-asc');
INSERT INTO `manufacturers` VALUES (2055, 'ZR3', 'AUTOZONE/NATIONAL CARBURETORS', 'autozone-national-carburetors');
INSERT INTO `manufacturers` VALUES (2056, 'ZR5', 'AUTOZONE/MIGHTY LIFT ARVIN', 'autozone-mighty-lift-arvin');
INSERT INTO `manufacturers` VALUES (2057, 'ZR6', 'AUTOZONE/MIDWEST', 'autozone-midwest');
INSERT INTO `manufacturers` VALUES (2058, 'ZR8', 'AUTOZONE/METRA', 'autozone-metra');
INSERT INTO `manufacturers` VALUES (2059, 'ZS6', 'AUTOZONE/EXTANG', 'autozone-extang');
INSERT INTO `manufacturers` VALUES (2060, 'N5H', 'NAPA/TECH EXPERT-TEE', 'napa-tech-expert-tee');
INSERT INTO `manufacturers` VALUES (2061, 'XF9', 'CENTERFORCE', 'centerforce');
INSERT INTO `manufacturers` VALUES (2062, 'SB1', 'SEIBON CARBON', 'seibon-carbon');
INSERT INTO `manufacturers` VALUES (2063, 'KON', 'KONI SHOCK', 'koni-shock');
INSERT INTO `manufacturers` VALUES (2064, 'AN1', 'AUTO NATION', 'auto-nation');
INSERT INTO `manufacturers` VALUES (2065, 'CL3', 'NAPA/TIMING PRODUCTS-NTP', 'napa-timing-products-ntp');
INSERT INTO `manufacturers` VALUES (2066, 'N36', 'NAPA/AUTOMATIC TRANS PARTS-ATP', 'napa-automatic-trans-parts-atp');
INSERT INTO `manufacturers` VALUES (2067, 'N3Q', 'NAPA/RADIATORS-NR', 'napa-radiators-nr');
INSERT INTO `manufacturers` VALUES (2068, 'N52', 'NAPA/ENGINE KITS-KIT', 'napa-engine-kits-kit');
INSERT INTO `manufacturers` VALUES (2069, 'N90', 'NAPA/RAYLOC SAFETY STOP-RSS', 'napa-rayloc-safety-stop-rss');
INSERT INTO `manufacturers` VALUES (2070, 'N92', 'NAPA/PROFORMER BRAKE PADS-SHOES-TS', 'napa-proformer-brake-pads-shoes-ts');
INSERT INTO `manufacturers` VALUES (2071, 'N4K', 'NAPA/ULTRA PREMIUM MSTR CYLS-NMC', 'napa-ultra-premium-mstr-cyls-nmc');
INSERT INTO `manufacturers` VALUES (2072, 'ZM1', 'AUTOZONE/ VALUCRAFT-CARDONE', 'autozone-valucraft-cardone');
INSERT INTO `manufacturers` VALUES (2073, 'ZP4', 'AUTOZONE/ DURALAST-ALL PARTS', 'autozone-duralast-all-parts');
INSERT INTO `manufacturers` VALUES (2074, 'ZR0', 'AUTOZONE/HYPERTECH', 'autozone-hypertech');
INSERT INTO `manufacturers` VALUES (2075, 'ZS0', 'AUTOZONE/EDELBROCK', 'autozone-edelbrock');
INSERT INTO `manufacturers` VALUES (2076, 'ZT3', 'AUTOZONE/STP', 'autozone-stp');
INSERT INTO `manufacturers` VALUES (2077, 'ZU4', 'AUTOZONE/AIRAID', 'autozone-airaid');
INSERT INTO `manufacturers` VALUES (2078, 'ZU8', 'AUTOZONE/DURALAST GOLD-SURTRACK', 'autozone-duralast-gold-surtrack');
INSERT INTO `manufacturers` VALUES (2079, 'ZDT', 'AUTOZONE/ DURALAST-REMY', 'autozone-duralast-remy');
INSERT INTO `manufacturers` VALUES (2080, 'ZAT', 'AUTOZONE/ATLANTIC AUTOMOTIVE', 'autozone-atlantic-automotive');
INSERT INTO `manufacturers` VALUES (2081, 'ZAZ', 'AUTOZONE/ANZO', 'autozone-anzo');
INSERT INTO `manufacturers` VALUES (2082, 'ZVD', 'AUTOZONE/VDO', 'autozone-vdo');
INSERT INTO `manufacturers` VALUES (2083, 'ZLQ', 'AUTOZONE/LKQ-PARTS', 'autozone-lkq-parts');
INSERT INTO `manufacturers` VALUES (2084, 'P7T', 'PERITUS', 'peritus');
INSERT INTO `manufacturers` VALUES (2085, 'ORB', 'BRAKEBEST BRAKE PADS', 'brakebest-brake-pads');
INSERT INTO `manufacturers` VALUES (2086, 'BHO', 'BLACK HORSE OFF ROAD', 'black-horse-off-road');
INSERT INTO `manufacturers` VALUES (2087, 'DPM', 'DURAGO - BRAKE PAD AND ROTOR KIT C/MS', 'durago-brake-pad-and-rotor-kit-c-ms');
INSERT INTO `manufacturers` VALUES (2088, 'BH3', 'NAPA/BOSCH-BSH', 'napa-bosch-bsh');
INSERT INTO `manufacturers` VALUES (2089, 'BS3', 'NAPA/BOSAL-BOS', 'napa-bosal-bos');
INSERT INTO `manufacturers` VALUES (2090, 'CAN', 'CANADIAN TIRE', 'canadian-tire');
INSERT INTO `manufacturers` VALUES (2091, 'FI3', 'NAPA/FILTERS-FIL', 'napa-filters-fil');
INSERT INTO `manufacturers` VALUES (2092, 'FS3', 'NAPA/TEMP-TEM', 'napa-temp-tem');
INSERT INTO `manufacturers` VALUES (2093, 'N33', 'NAPA/AIR COND KITS-ACK', 'napa-air-cond-kits-ack');
INSERT INTO `manufacturers` VALUES (2094, 'N3K', 'NAPA/BRAKE CALIPERS-CAL', 'napa-brake-calipers-cal');
INSERT INTO `manufacturers` VALUES (2095, 'N41', 'NAPA/BEARINGS-BRG', 'napa-bearings-brg');
INSERT INTO `manufacturers` VALUES (2096, 'N42', 'NAPA/BRAKE BOOSTERS-NBB', 'napa-brake-boosters-nbb');
INSERT INTO `manufacturers` VALUES (2097, 'N43', 'NAPA/BRAKE ROTORS & DRUMS-NB', 'napa-brake-rotors-drums-nb');
INSERT INTO `manufacturers` VALUES (2098, 'N44', 'NAPA/CHASSIS PARTS-NCP', 'napa-chassis-parts-ncp');
INSERT INTO `manufacturers` VALUES (2099, 'N48', 'NAPA/DELPHI ENG MANAGEMENT-DEM', 'napa-delphi-eng-management-dem');
INSERT INTO `manufacturers` VALUES (2100, 'N49', 'NAPA/DELPHI FUEL PUMPS-DFP', 'napa-delphi-fuel-pumps-dfp');
INSERT INTO `manufacturers` VALUES (2101, 'N50', 'NAPA/DENSO-DEN', 'napa-denso-den');
INSERT INTO `manufacturers` VALUES (2102, 'N63', 'NAPA/MAXDRIVE-NMD', 'napa-maxdrive-nmd');
INSERT INTO `manufacturers` VALUES (2103, 'N75', 'NAPA/OIL SEALS-NOS', 'napa-oil-seals-nos');
INSERT INTO `manufacturers` VALUES (2104, 'N77', 'NAPA/POWER STEERING PUMPS-NSP', 'napa-power-steering-pumps-nsp');
INSERT INTO `manufacturers` VALUES (2105, 'N80', 'NAPA/PRO SERIES ELECTRICAL-NAE', 'napa-pro-series-electrical-nae');
INSERT INTO `manufacturers` VALUES (2106, 'N82', 'NAPA/PROPOWR REMAN ENGS-ATK', 'napa-propowr-reman-engs-atk');
INSERT INTO `manufacturers` VALUES (2107, 'N88', 'NAPA/ULTRA PREMIUM BRAKE PADS & SHOES-UP', 'napa-ultra-premium-brake-pads-shoes-up');
INSERT INTO `manufacturers` VALUES (2108, 'N91', 'NAPA/POWER PREMIUM PLUS-RAY', 'napa-power-premium-plus-ray');
INSERT INTO `manufacturers` VALUES (2109, 'N93', 'NAPA/ELECTRICAL MOTORS-RAY', 'napa-electrical-motors-ray');
INSERT INTO `manufacturers` VALUES (2110, 'N94', 'NAPA/REMAN COMPUTERS-NEC', 'napa-reman-computers-nec');
INSERT INTO `manufacturers` VALUES (2111, 'NG3', 'NAPA/NGK SPARK PLGS-OXYGEN SENS-NGK', 'napa-ngk-spark-plgs-oxygen-sens-ngk');
INSERT INTO `manufacturers` VALUES (2112, 'NS3', 'NAPA/SHOCKS-NS', 'napa-shocks-ns');
INSERT INTO `manufacturers` VALUES (2113, 'WA3', 'NAPA/EXHAUST-EXH', 'napa-exhaust-exh');
INSERT INTO `manufacturers` VALUES (2114, 'N3F', 'NAPA BRAKE CALIPERS SEMI LOADED-CAL', 'napa-brake-calipers-semi-loaded-cal');
INSERT INTO `manufacturers` VALUES (2115, 'N3I', 'NAPA ABS SENSORS-UP', 'napa-abs-sensors-up');
INSERT INTO `manufacturers` VALUES (2116, 'N4A', 'NAPA/FUEL PUMPS-AFP', 'napa-fuel-pumps-afp');
INSERT INTO `manufacturers` VALUES (2117, 'N4E', 'NAPA/SPECTRA TANKS PANS-STP', 'napa-spectra-tanks-pans-stp');
INSERT INTO `manufacturers` VALUES (2118, 'N4L', 'NAPA/PROFORMER MSTR CYLS-NMC', 'napa-proformer-mstr-cyls-nmc');
INSERT INTO `manufacturers` VALUES (2119, 'N4Q', 'NAPA/CLUTCH AND FLYWHEEL-NCF', 'napa-clutch-and-flywheel-ncf');
INSERT INTO `manufacturers` VALUES (2120, 'N4S', 'NAPA/NAPA BRAKE KITS-NBK', 'napa-napa-brake-kits-nbk');
INSERT INTO `manufacturers` VALUES (2121, 'BL3', 'NAPA/BALKAMP-BK', 'napa-balkamp-bk');
INSERT INTO `manufacturers` VALUES (2122, 'ZA6', 'AUTOZONE/ DURALAST-BOSCH', 'autozone-duralast-bosch');
INSERT INTO `manufacturers` VALUES (2123, 'ZA8', 'AUTOZONE/ DURALAST-PLEWS-EDELMANN', 'autozone-duralast-plews-edelmann');
INSERT INTO `manufacturers` VALUES (2124, 'ZB6', 'AUTOZONE/DELPHI', 'autozone-delphi');
INSERT INTO `manufacturers` VALUES (2125, 'ZC7', 'AUTOZONE/ DURALAST-DEA', 'autozone-duralast-dea');
INSERT INTO `manufacturers` VALUES (2126, 'ZC9', 'AUTOZONE/ DURALAST-DAYCO', 'autozone-duralast-dayco');
INSERT INTO `manufacturers` VALUES (2127, 'ZCL', 'AUTOZONE/AZ FILTERS-CHAMP LABS', 'autozone-az-filters-champ-labs');
INSERT INTO `manufacturers` VALUES (2128, 'ZD4', 'AUTOZONE/CRP', 'autozone-crp');
INSERT INTO `manufacturers` VALUES (2129, 'ZE0', 'AUTOZONE/MAHLE ORIGINAL', 'autozone-mahle-original');
INSERT INTO `manufacturers` VALUES (2130, 'ZJ2', 'AUTOZONE/AIRTEX', 'autozone-airtex');
INSERT INTO `manufacturers` VALUES (2131, 'CMS', 'CANADIAN TIRE MONROE SHOCKS/STRUTS', 'canadian-tire-monroe-shocks-struts');
INSERT INTO `manufacturers` VALUES (2132, 'ZK8', 'AUTOZONE/ACDELCO', 'autozone-acdelco');
INSERT INTO `manufacturers` VALUES (2133, 'ZL0', 'AUTOZONE/DURALAST-MPA', 'autozone-duralast-mpa');
INSERT INTO `manufacturers` VALUES (2134, 'ZL5', 'AUTOZONE/ DURALAST-ATSCO', 'autozone-duralast-atsco');
INSERT INTO `manufacturers` VALUES (2135, 'ZL9', 'AUTOZONE/ DURALAST-IBI', 'autozone-duralast-ibi');
INSERT INTO `manufacturers` VALUES (2136, 'ZM4', 'AUTOZONE/ DURALAST-QUALIS', 'autozone-duralast-qualis');
INSERT INTO `manufacturers` VALUES (2137, 'ZM6', 'AUTOZONE/ DURALAST-CARDONE', 'autozone-duralast-cardone');
INSERT INTO `manufacturers` VALUES (2138, 'ZM9', 'AUTOZONE/DURALAST PERFECTION', 'autozone-duralast-perfection');
INSERT INTO `manufacturers` VALUES (2139, 'ZN9', 'AUTOZONE/ DURALAST-BRAKE PARTS', 'autozone-duralast-brake-parts');
INSERT INTO `manufacturers` VALUES (2140, 'ZP0', 'AUTOZONE/SUREFIRE', 'autozone-surefire');
INSERT INTO `manufacturers` VALUES (2141, 'ZP8', 'AUTOZONE/WESTIN', 'autozone-westin');
INSERT INTO `manufacturers` VALUES (2142, 'ZQ7', 'AUTOZONE/PILOT', 'autozone-pilot');
INSERT INTO `manufacturers` VALUES (2143, 'ZQ8', 'AUTOZONE/PILOT COLLISION', 'autozone-pilot-collision');
INSERT INTO `manufacturers` VALUES (2144, 'ZR2', 'AUTOZONE/NIFTY', 'autozone-nifty');
INSERT INTO `manufacturers` VALUES (2145, 'ZS9', 'AUTOZONE/ENERGY SUSPENSION', 'autozone-energy-suspension');
INSERT INTO `manufacturers` VALUES (2146, 'ZT1', 'AUTOZONE/DEEZEE', 'autozone-deezee');
INSERT INTO `manufacturers` VALUES (2147, 'N4W', 'NAPA/AUTOMATIC TRANS-MTC', 'napa-automatic-trans-mtc');
INSERT INTO `manufacturers` VALUES (2148, 'N4X', 'NAPA/PREMIUM STEERING-PS', 'napa-premium-steering-ps');
INSERT INTO `manufacturers` VALUES (2149, 'ZT0', 'AUTOZONE/BILSTEIN', 'autozone-bilstein');
INSERT INTO `manufacturers` VALUES (2150, 'ZU3', 'AUTOZONE/COMP CAMS', 'autozone-comp-cams');
INSERT INTO `manufacturers` VALUES (2151, 'ZU5', 'AUTOZONE/ DURALAST-DAYCO HOSES', 'autozone-duralast-dayco-hoses');
INSERT INTO `manufacturers` VALUES (2152, 'N5C', 'NAPA/POWER STEERING HOSES-NPS', 'napa-power-steering-hoses-nps');
INSERT INTO `manufacturers` VALUES (2153, 'ZV0', 'AUTOZONE/S A GEAR', 'autozone-s-a-gear');
INSERT INTO `manufacturers` VALUES (2154, 'EH3', 'NAPA/ECHLIN PARTS-ECH', 'napa-echlin-parts-ech');
INSERT INTO `manufacturers` VALUES (2155, 'ZAU', 'AUTOZONE/AUTO 7', 'autozone-auto-7');
INSERT INTO `manufacturers` VALUES (2156, 'ZAR', 'AUTOZONE/ DURALAST-ANCHOR', 'autozone-duralast-anchor');
INSERT INTO `manufacturers` VALUES (2157, 'XLW', 'KING BEARING', 'king-bearing');
INSERT INTO `manufacturers` VALUES (2158, 'ZDB', 'AUTOZONE/ DURALAST-BEARING&SEALS (BTECH)', 'autozone-duralast-bearing-seals-btech');
INSERT INTO `manufacturers` VALUES (2159, 'ZBD', 'AUTOZONE/ BRAKEWARE-DORMAN', 'autozone-brakeware-dorman');
INSERT INTO `manufacturers` VALUES (2160, 'ZCV', 'AUTOZONE/DURALAST GOLD-CV AXLES', 'autozone-duralast-gold-cv-axles');
INSERT INTO `manufacturers` VALUES (2161, 'N5M', 'NAPA/KYB SHOCKS-KYB', 'napa-kyb-shocks-kyb');
INSERT INTO `manufacturers` VALUES (2162, 'N9B', 'NAPA/ULTRA PREMIUM BRAKE PARTS-UP', 'napa-ultra-premium-brake-parts-up');
INSERT INTO `manufacturers` VALUES (2163, 'ZNB', 'AUTOZONE/NATIONAL BEARINGS & SEALS', 'autozone-national-bearings-seals');
INSERT INTO `manufacturers` VALUES (2164, 'ZPT', 'AUTOZONE/PUTCO', 'autozone-putco');
INSERT INTO `manufacturers` VALUES (2165, 'ZRT', 'AUTOZONE/RODATECH', 'autozone-rodatech');
INSERT INTO `manufacturers` VALUES (2166, 'Z9D', 'AUTOZONE/YUKON GEAR', 'autozone-yukon-gear');
INSERT INTO `manufacturers` VALUES (2167, 'ZHL', 'AUTOZONE/HUSKY LINERS', 'autozone-husky-liners');
INSERT INTO `manufacturers` VALUES (2168, 'N9D', 'NAPA/CONTROL ARMS & SUSPENSION-NCP', 'napa-control-arms-suspension-ncp');
INSERT INTO `manufacturers` VALUES (2169, 'N5P', 'NAPA/PROFORMER CHASSIS-PCC', 'napa-proformer-chassis-pcc');
INSERT INTO `manufacturers` VALUES (2170, 'N9E', 'NAPA/DENSO STARTERS & ALTERNATORS', 'napa-denso-starters-alternators');
INSERT INTO `manufacturers` VALUES (2171, 'ZFK', 'AUTOZONE/4SEASONS - KIT', 'autozone-4seasons-kit');
INSERT INTO `manufacturers` VALUES (2172, 'NF1', 'NAPA/ CARTER FUEL PUMPS - CFP', 'napa-carter-fuel-pumps-cfp');
INSERT INTO `manufacturers` VALUES (2173, 'ZCO', 'AUTOZONE/CONTINENTAL', 'autozone-continental');
INSERT INTO `manufacturers` VALUES (2174, 'ND1', 'NAPA/BEARINGS-BRQ', 'napa-bearings-brq');
INSERT INTO `manufacturers` VALUES (2175, 'ND2', 'NAPA/OIL SEALS - OSQ', 'napa-oil-seals-osq');
INSERT INTO `manufacturers` VALUES (2176, 'ZUC', 'AUTOZONE/DURALAST_REMAN', 'autozone-duralast_reman');
INSERT INTO `manufacturers` VALUES (2177, 'ZUD', 'AUTOZONE/DURALAST_BRACKETED', 'autozone-duralast_bracketed');
INSERT INTO `manufacturers` VALUES (2178, 'AU0', 'ARMORDILLO USA', 'armordillo-usa');
INSERT INTO `manufacturers` VALUES (2179, 'BS1', 'BSE ORIGINAL', 'bse-original');
INSERT INTO `manufacturers` VALUES (2180, 'BSO', 'BLUE STREAK ELECTRONICS ORIGINAL', 'blue-streak-electronics-original');
INSERT INTO `manufacturers` VALUES (2181, 'HR1', 'H&R SPECIAL SPRINGS', 'h-r-special-springs');
INSERT INTO `manufacturers` VALUES (2182, 'SD1', 'SCOTT DRAKE', 'scott-drake');
INSERT INTO `manufacturers` VALUES (2183, 'DCV', 'ACDELCO MEXICO UNIQUE', 'acdelco-mexico-unique');
INSERT INTO `manufacturers` VALUES (2184, 'EH1', '1-800-RADIATOR/CARB APPROVED', '1-800-radiator-carb-approved');
INSERT INTO `manufacturers` VALUES (2185, 'EDO', 'ELRING - DAS ORIGINAL', 'elring-das-original');
INSERT INTO `manufacturers` VALUES (2186, '3RP', 'RESTO PARTS', 'resto-parts');
INSERT INTO `manufacturers` VALUES (2187, 'FDV', 'VALUESTOP DISC PADS AND BRAKE SHOES', 'valuestop-disc-pads-and-brake-shoes');
INSERT INTO `manufacturers` VALUES (2188, 'FVF', 'FVP SPECTRA FUEL PUMPS', 'fvp-spectra-fuel-pumps');
INSERT INTO `manufacturers` VALUES (2189, 'AC8', 'ACTION CLUTCH', 'action-clutch');
INSERT INTO `manufacturers` VALUES (2190, 'GMT', 'GM TRANSMISSIONS', 'gm-transmissions');
INSERT INTO `manufacturers` VALUES (2191, 'X50', 'XRF - SILVER', 'xrf-silver');
INSERT INTO `manufacturers` VALUES (2192, 'PF9', 'PERFECT STOP BRAKE WEAR & ABS SENSORS', 'perfect-stop-brake-wear-abs-sensors');
INSERT INTO `manufacturers` VALUES (2193, '6DR', 'ETE Reman', 'ete-reman');
INSERT INTO `manufacturers` VALUES (2194, 'PP9', 'PACESETTER PERFORMANCE', 'pacesetter-performance');
INSERT INTO `manufacturers` VALUES (2195, 'CT9', 'CTR', 'ctr');
INSERT INTO `manufacturers` VALUES (2196, 'PR0', 'PROTHANE', 'prothane');
INSERT INTO `manufacturers` VALUES (2197, 'EBP', 'EVOLUTION BUNDLES BY POWER STOP', 'evolution-bundles-by-power-stop');
INSERT INTO `manufacturers` VALUES (2198, 'WSS', 'WELD STREET AND STRIP', 'weld-street-and-strip');
INSERT INTO `manufacturers` VALUES (2199, 'H9S', 'HPS PERFORMANCE', 'hps-performance');
INSERT INTO `manufacturers` VALUES (2200, 'OBH', 'BRAKEBEST BRAKE HOSES', 'brakebest-brake-hoses');
INSERT INTO `manufacturers` VALUES (2201, 'ZSP', 'AUTOZONE/SPECTRA PREMIUM', 'autozone-spectra-premium');
INSERT INTO `manufacturers` VALUES (2202, 'ZQ3', 'AUTOZONE/ DURALAST-RARE PARTS', 'autozone-duralast-rare-parts');
INSERT INTO `manufacturers` VALUES (2203, 'ZS7', 'AUTOZONE/FOUR SEASONS - EVERCO', 'autozone-four-seasons-everco');
INSERT INTO `manufacturers` VALUES (2204, 'RCN', 'R1 CONCEPTS', 'r1-concepts');
INSERT INTO `manufacturers` VALUES (2205, 'N5G', 'NAPA/SOLUTIONS-NOE', 'napa-solutions-noe');
INSERT INTO `manufacturers` VALUES (2206, 'NBK', 'NAPA/BALKAMP SPECIAL HANDLING-BKN', 'napa-balkamp-special-handling-bkn');
INSERT INTO `manufacturers` VALUES (2207, 'Z9C', 'AUTOZONE/DURALAST CHASSIS', 'autozone-duralast-chassis');
INSERT INTO `manufacturers` VALUES (2208, 'AP5', 'US AUTO PARTS/KOOL-VUE', 'us-auto-parts-kool-vue');
INSERT INTO `manufacturers` VALUES (2209, 'N35', 'NAPA/ALTROM IMPORTS-ATM', 'napa-altrom-imports-atm');
INSERT INTO `manufacturers` VALUES (2210, 'N51', 'NAPA/ECHLIN FUEL SYSTEM-CRB', 'napa-echlin-fuel-system-crb');
INSERT INTO `manufacturers` VALUES (2211, 'NB3', 'NAPA/BELTS & HOSE-NBH', 'napa-belts-hose-nbh');
INSERT INTO `manufacturers` VALUES (2212, 'ZK1', 'AUTOZONE/DENSO', 'autozone-denso');
INSERT INTO `manufacturers` VALUES (2213, 'ZK2', 'AUTOZONE/CHAMPION SPARK PLUGS', 'autozone-champion-spark-plugs');
INSERT INTO `manufacturers` VALUES (2214, 'ZK4', 'AUTOZONE/ACI', 'autozone-aci');
INSERT INTO `manufacturers` VALUES (2215, 'ZK5', 'AUTOZONE/EAST PENN BATTERY', 'autozone-east-penn-battery');
INSERT INTO `manufacturers` VALUES (2216, 'ZK6', 'AUTOZONE/SEALED POWER', 'autozone-sealed-power');
INSERT INTO `manufacturers` VALUES (2217, 'ZKN', 'AUTOZONE/K&N FILTER', 'autozone-k-n-filter');
INSERT INTO `manufacturers` VALUES (2218, 'ZMO', 'AUTOZONE/MOBIL', 'autozone-mobil');
INSERT INTO `manufacturers` VALUES (2219, 'ZNG', 'AUTOZONE/NGK', 'autozone-ngk');
INSERT INTO `manufacturers` VALUES (2220, 'ZRY', 'AUTOZONE/RAYBESTOS', 'autozone-raybestos');
INSERT INTO `manufacturers` VALUES (2221, 'ZSE', 'AUTOZONE/SIEMENS', 'autozone-siemens');
INSERT INTO `manufacturers` VALUES (2222, 'ZTI', 'AUTOZONE/TIMKEN', 'autozone-timken');
INSERT INTO `manufacturers` VALUES (2223, 'ZK0', 'AUTOZONE/ BRAKEWARE-BENDIX', 'autozone-brakeware-bendix');
INSERT INTO `manufacturers` VALUES (2224, 'ZK9', 'AUTOZONE/FELPRO', 'autozone-felpro');
INSERT INTO `manufacturers` VALUES (2225, 'ZLY', 'AUTOZONE/LYNX', 'autozone-lynx');
INSERT INTO `manufacturers` VALUES (2226, 'NP2', 'NAPA/SEALED PWR ENG PARTS-SEP', 'napa-sealed-pwr-eng-parts-sep');
INSERT INTO `manufacturers` VALUES (2227, 'ZO0', 'AUTOZONE/FRAM ULTRA', 'autozone-fram-ultra');
INSERT INTO `manufacturers` VALUES (2228, 'ZBA', 'AUTOZONE/BECK ARNLEY', 'autozone-beck-arnley');
INSERT INTO `manufacturers` VALUES (2229, 'ZBS', 'AUTOZONE/BLUE STREAK', 'autozone-blue-streak');
INSERT INTO `manufacturers` VALUES (2230, 'ZSR', 'AUTOZONE/SCHRADER', 'autozone-schrader');
INSERT INTO `manufacturers` VALUES (2231, 'ZTS', 'AUTOZONE/TECHSMART', 'autozone-techsmart');
INSERT INTO `manufacturers` VALUES (2232, 'ZPB', 'AUTOZONE/POWER BRAKE EXCHANGE', 'autozone-power-brake-exchange');
INSERT INTO `manufacturers` VALUES (2233, 'ZV4', 'AUTOZONE/KYB', 'autozone-kyb');
INSERT INTO `manufacturers` VALUES (2234, 'ZHY', 'AUTOZONE/HY-KO PRODUCTS', 'autozone-hy-ko-products');
INSERT INTO `manufacturers` VALUES (2235, 'B3M', 'BENCHMARK', 'benchmark');
INSERT INTO `manufacturers` VALUES (2236, 'ZCC', 'AUTOZONE/COVERCRAFT', 'autozone-covercraft');
INSERT INTO `manufacturers` VALUES (2268, 'MPK', 'RSSW', 'rssw');
INSERT INTO `manufacturers` VALUES (2269, 'AT8', 'ALLIANCE - TRAD BRAKE ROTOR & PS PADS', 'alliance-trad-brake-rotor-ps-pads');
INSERT INTO `manufacturers` VALUES (2270, 'EVO', 'EVO MANUFACTURING', 'evo-manufacturing');
INSERT INTO `manufacturers` VALUES (2271, 'FBH', 'FVP BRAKE HARDWARE', 'fvp-brake-hardware');
INSERT INTO `manufacturers` VALUES (2272, 'HD1', 'HSP DIESEL', 'hsp-diesel');
INSERT INTO `manufacturers` VALUES (2273, 'MAV', 'MAVAL', 'maval');
INSERT INTO `manufacturers` VALUES (2274, 'AB8', 'ALLIANCE - TRAD BRAKE BASE', 'alliance-trad-brake-base');
INSERT INTO `manufacturers` VALUES (2275, 'BS2', 'BSE NEW', 'bse-new');
INSERT INTO `manufacturers` VALUES (2276, 'BSN', 'BLUE STREAK ELECTRONICS NEW', 'blue-streak-electronics-new');
INSERT INTO `manufacturers` VALUES (2277, 'D9D', 'DYNOMITE DIESEL', 'dynomite-diesel');
INSERT INTO `manufacturers` VALUES (2278, 'DS1', 'DIAMOND STANDARD / REFLEXXION AUTOMOTIVE', 'diamond-standard-reflexxion-automotive');
INSERT INTO `manufacturers` VALUES (2279, 'FT2', 'FVP TIMING', 'fvp-timing');
INSERT INTO `manufacturers` VALUES (2280, 'RBX', 'RUBICON EXPRESS', 'rubicon-express');
INSERT INTO `manufacturers` VALUES (2281, 'OEO', 'ORIGINAL EQUIPMENT OIL FILTER', 'original-equipment-oil-filter');
INSERT INTO `manufacturers` VALUES (2282, 'RX7', 'RAIN X WEATHERBEATER', 'rain-x-weatherbeater');
INSERT INTO `manufacturers` VALUES (2283, 'SD2', 'SATISFACTION DISTRIBUTION', 'satisfaction-distribution');
INSERT INTO `manufacturers` VALUES (2284, 'SSH', 'SPEED SHOP', 'speed-shop');
INSERT INTO `manufacturers` VALUES (2285, 'US1', 'PERFECT STOP FULL LINE', 'perfect-stop-full-line');
INSERT INTO `manufacturers` VALUES (2286, 'BA1', 'APSG OXYGEN SENSORS', 'apsg-oxygen-sensors');
INSERT INTO `manufacturers` VALUES (2287, 'FDX', 'FMP DEXTAR', 'fmp-dextar');
INSERT INTO `manufacturers` VALUES (2288, 'L9D', 'LOCK\'ER DOWN', 'lock-er-down');
INSERT INTO `manufacturers` VALUES (2289, 'PGS', 'PWR STEER', 'pwr-steer');
INSERT INTO `manufacturers` VALUES (2290, 'UB1', 'USTART BATTERIES', 'ustart-batteries');
INSERT INTO `manufacturers` VALUES (2291, 'C3D', 'CORTECO', 'corteco');
INSERT INTO `manufacturers` VALUES (2292, 'DTS', 'DURAGO TITANIUM SERIES  DTS', 'durago-titanium-series-dts');
INSERT INTO `manufacturers` VALUES (2293, 'PDT', 'PDQ-TPMS', 'pdq-tpms');
INSERT INTO `manufacturers` VALUES (2294, 'LKU', 'LKQ CORP', 'lkq-corp');
INSERT INTO `manufacturers` VALUES (2295, 'LJF', 'LESJOFORS', 'lesjofors');
INSERT INTO `manufacturers` VALUES (2296, 'MO6', 'MOTORCRAFT BRAKE KITS', 'motorcraft-brake-kits');
INSERT INTO `manufacturers` VALUES (2297, 'P2W', 'PRECISION CHASSIS', 'precision-chassis');
INSERT INTO `manufacturers` VALUES (2298, 'UA3', 'UAC SHORT KITS', 'uac-short-kits');
INSERT INTO `manufacturers` VALUES (2299, '62C', 'PB/CV AXLE', 'pb-cv-axle');
INSERT INTO `manufacturers` VALUES (2300, 'BPO', 'BRAKE PRO OVERSTOCK', 'brake-pro-overstock');
INSERT INTO `manufacturers` VALUES (2301, 'CVH', 'COVERCRAFT - CARHARTT', 'covercraft-carhartt');
INSERT INTO `manufacturers` VALUES (2302, 'DR8', 'DORMAN PREMIUM RD', 'dorman-premium-rd');
INSERT INTO `manufacturers` VALUES (2303, 'PD0', 'PARTS MASTER / DORMAN  NO HOSES', 'parts-master-dorman-no-hoses');
INSERT INTO `manufacturers` VALUES (2305, 'WP2', 'WORLDPAC WP2', 'worldpac-wp2');
INSERT INTO `manufacturers` VALUES (2306, 'A3V', 'ABEX BRAKE', 'abex-brake');
INSERT INTO `manufacturers` VALUES (2307, 'IMU', 'IDEMITSU', 'idemitsu');
INSERT INTO `manufacturers` VALUES (2308, 'CCB', 'COLD CRANKER', 'cold-cranker');
INSERT INTO `manufacturers` VALUES (2309, 'GK9', 'GKN', 'gkn');
INSERT INTO `manufacturers` VALUES (2310, 'OPZ', 'PRECISION DRIVESHAFT', 'precision-driveshaft');
INSERT INTO `manufacturers` VALUES (2311, 'P6T', 'PERFORMANCE RIDE TECHNOLOGY BY ADD', 'performance-ride-technology-by-add');
INSERT INTO `manufacturers` VALUES (2312, 'QAP', 'QUALITY AUTOMOTIVE PRODUCTS', 'quality-automotive-products');
INSERT INTO `manufacturers` VALUES (2313, 'SBA', 'SANGSIN BRAKE AMERICA', 'sangsin-brake-america');
INSERT INTO `manufacturers` VALUES (2314, 'SYA', 'SPYDER AUTO', 'spyder-auto');
INSERT INTO `manufacturers` VALUES (2315, 'WA1', 'WHEEL ACCESSORIES PARTS', 'wheel-accessories-parts');
INSERT INTO `manufacturers` VALUES (2316, 'PS9', 'PERFECT STOP', 'perfect-stop');
INSERT INTO `manufacturers` VALUES (2317, 'URZ', 'URO SHORT SKU', 'uro-short-sku');
INSERT INTO `manufacturers` VALUES (2318, 'OSY', 'O\'REILLY/SYLVANIA', 'o-reilly-sylvania');
INSERT INTO `manufacturers` VALUES (2319, 'AB1', 'AMERIBRAKES', 'ameribrakes');
INSERT INTO `manufacturers` VALUES (2320, 'OBX', 'BRAKEBEST SELECT BRAKE SHOES', 'brakebest-select-brake-shoes');
INSERT INTO `manufacturers` VALUES (2321, '6PA', 'AUTOPLUS CHAMPION AGM-PB', 'autoplus-champion-agm-pb');
INSERT INTO `manufacturers` VALUES (2322, '6PB', 'AUTOPLUS CHAMPION-PB', 'autoplus-champion-pb');
INSERT INTO `manufacturers` VALUES (2323, '6PO', 'AUTOPLUS OPTIMA', 'autoplus-optima');
INSERT INTO `manufacturers` VALUES (2324, 'F81', 'FVP BRAKE PADS', 'fvp-brake-pads');
INSERT INTO `manufacturers` VALUES (2325, 'GGB', 'GG BAILEY', 'gg-bailey');
INSERT INTO `manufacturers` VALUES (2326, 'JTW', 'JANTE WHEEL', 'jante-wheel');
INSERT INTO `manufacturers` VALUES (2327, 'CF8', 'CF ADVANCE', 'cf-advance');
INSERT INTO `manufacturers` VALUES (2328, 'MCZ', 'MOTORCADE/ ZIM', 'motorcade-zim');
INSERT INTO `manufacturers` VALUES (2329, 'DE3', 'DURAGO EP COATED  PAD & ROTOR KIT C/MS', 'durago-ep-coated-pad-rotor-kit-c-ms');
INSERT INTO `manufacturers` VALUES (2330, 'OB1', 'O\'REILLY BRAKEBEST', 'o-reilly-brakebest');
INSERT INTO `manufacturers` VALUES (2331, 'LA1', 'LENZ IGNITION', 'lenz-ignition');
INSERT INTO `manufacturers` VALUES (2332, 'NTS', 'NUGEON TECHSHIELD', 'nugeon-techshield');
INSERT INTO `manufacturers` VALUES (2333, 'OM2', 'OMNIPARTS GENUINE OE', 'omniparts-genuine-oe');
INSERT INTO `manufacturers` VALUES (2336, 'CA1', 'CONTINENTAL AFTERMARKET', 'continental-aftermarket');
INSERT INTO `manufacturers` VALUES (2337, 'CPM', 'CANADA PROOF MIXTECH', 'canada-proof-mixtech');
INSERT INTO `manufacturers` VALUES (2338, 'DM3', 'DISCOVER MIXTECH', 'discover-mixtech');
INSERT INTO `manufacturers` VALUES (2339, 'OCC', 'BRAKEBEST COATED CALIPERS', 'brakebest-coated-calipers');
INSERT INTO `manufacturers` VALUES (2346, 'BU9', 'BUDGE', 'budge');
INSERT INTO `manufacturers` VALUES (2347, 'DMO', 'DYNAMO', 'dynamo');
INSERT INTO `manufacturers` VALUES (2348, 'FHG', 'FH GROUP', 'fh-group');
INSERT INTO `manufacturers` VALUES (2349, 'HAU', 'HERKO AUTOMOTIVE GROUP', 'herko-automotive-group');
INSERT INTO `manufacturers` VALUES (2350, 'RX8', 'RAIN-X CANADA SILICONE ADVANTEDGE', 'rain-x-canada-silicone-advantedge');
INSERT INTO `manufacturers` VALUES (2351, 'S0Q', 'SIDEM', 'sidem');
INSERT INTO `manufacturers` VALUES (2352, 'S5D', 'SPIDAN', 'spidan');
INSERT INTO `manufacturers` VALUES (2354, '705', 'MIDTRONICS, INC.', 'midtronics-inc');
INSERT INTO `manufacturers` VALUES (2355, '709', 'PORT-A-COOL / GENERAL SHELTERS OF TEXAS', 'port-a-cool-general-shelters-of-texas');
INSERT INTO `manufacturers` VALUES (2356, '70C', 'AMALIE OIL COMPANY', 'amalie-oil-company');
INSERT INTO `manufacturers` VALUES (2357, '70F', 'BADA WHEEL WEIGHTS', 'bada-wheel-weights');
INSERT INTO `manufacturers` VALUES (2358, '70Q', 'FIREPOWER / THERMADYNE COMPANY', 'firepower-thermadyne-company');
INSERT INTO `manufacturers` VALUES (2359, '70V', 'HENKEL CORPORATION (LOCTITE)', 'henkel-corporation-loctite');
INSERT INTO `manufacturers` VALUES (2360, '71O', 'TRACER PRODUCTS', 'tracer-products');
INSERT INTO `manufacturers` VALUES (2361, '71Q', 'VALSPAR', 'valspar');
INSERT INTO `manufacturers` VALUES (2362, '7NC', 'LUCAS OIL', 'lucas-oil');
INSERT INTO `manufacturers` VALUES (2363, '7NU', 'MECHANIX WEAR', 'mechanix-wear');
INSERT INTO `manufacturers` VALUES (2364, '7ZQ', 'WPS', 'wps');
INSERT INTO `manufacturers` VALUES (2365, '8MC', 'NETWORK MOTORCRAFT', 'network-motorcraft');
INSERT INTO `manufacturers` VALUES (2366, 'ACD', 'ACDELCO US', 'acdelco-us');
INSERT INTO `manufacturers` VALUES (2367, 'AFO', 'AMFLO', 'amflo');
INSERT INTO `manufacturers` VALUES (2368, 'AIF', 'ASC INDUSTRIES (FRENCH)', 'asc-industries-french');
INSERT INTO `manufacturers` VALUES (2369, 'B63', 'BASE SUSP', 'base-susp');
INSERT INTO `manufacturers` VALUES (2370, 'CRC', 'CRC CHEM', 'crc-chem');
INSERT INTO `manufacturers` VALUES (2371, 'K30', 'KYB MEXICO', 'kyb-mexico');
INSERT INTO `manufacturers` VALUES (2372, 'LIL', 'LISLE CORPORATION', 'lisle-corporation');
INSERT INTO `manufacturers` VALUES (2373, 'MIL', 'MILTON', 'milton');
INSERT INTO `manufacturers` VALUES (2374, 'MTV', 'MITYVAC', 'mityvac');
INSERT INTO `manufacturers` VALUES (2375, 'P18', 'PERFECT EQUIPMENT', 'perfect-equipment');
INSERT INTO `manufacturers` VALUES (2376, 'PLE', 'PLEWS', 'plews');
INSERT INTO `manufacturers` VALUES (2377, 'SLB', 'STA-LUBE', 'sta-lube');
INSERT INTO `manufacturers` VALUES (2378, 'THR', 'THRUSH', 'thrush');
INSERT INTO `manufacturers` VALUES (2379, 'UAB', 'ULTRALUBE', 'ultralube');
INSERT INTO `manufacturers` VALUES (2380, 'XME', 'LUBRIMATIC', 'lubrimatic');
INSERT INTO `manufacturers` VALUES (2381, 'ZER', 'ZEREX', 'zerex');
INSERT INTO `manufacturers` VALUES (2382, 'DEV', 'Devilbiss', 'devilbiss');
INSERT INTO `manufacturers` VALUES (2383, 'AO4', 'ALLIANCE OE BOSCH - ENGINE MANAGEMENT', 'alliance-oe-bosch-engine-management');
INSERT INTO `manufacturers` VALUES (2384, 'AO5', 'ALLIANCE OE DELPHI - ENGINE MANAGEMENT', 'alliance-oe-delphi-engine-management');
INSERT INTO `manufacturers` VALUES (2385, 'S0A', 'STABILUS', 'stabilus');
INSERT INTO `manufacturers` VALUES (2417, 'BHT', 'BH SENS', 'bh-sens');
INSERT INTO `manufacturers` VALUES (2418, 'BSU', 'BRAKING SOLUTIONS', 'braking-solutions');
INSERT INTO `manufacturers` VALUES (2419, 'D9A', 'ALLTECH DC', 'alltech-dc');
INSERT INTO `manufacturers` VALUES (2420, 'E2A', 'ENCORE AUTOMOTIVE', 'encore-automotive');
INSERT INTO `manufacturers` VALUES (2421, 'ETS', 'ETS', 'ets');
INSERT INTO `manufacturers` VALUES (2422, 'MCX', 'MOTORCADE/ FEB', 'motorcade-feb');
INSERT INTO `manufacturers` VALUES (2423, 'OBK', 'O\'REILLY/BRAKEBEST CALIPERS', 'o-reilly-brakebest-calipers');
INSERT INTO `manufacturers` VALUES (2424, 'OBZ', 'BRAKEBEST SELECT BRAKE PADS', 'brakebest-select-brake-pads');
INSERT INTO `manufacturers` VALUES (2425, 'OC1', 'O\'REILLY/MASTER PRO CV', 'o-reilly-master-pro-cv');
INSERT INTO `manufacturers` VALUES (2426, 'PS8', 'PERFECTSTOP PAD AND FULL LINE ROTOR KIT', 'perfectstop-pad-and-full-line-rotor-kit');
INSERT INTO `manufacturers` VALUES (2427, 'SML', 'SMARTLINER', 'smartliner');
INSERT INTO `manufacturers` VALUES (2428, 'VAW', 'VALKEN WIPERS', 'valken-wipers');
INSERT INTO `manufacturers` VALUES (2429, 'VT3', 'ADVANCE/TRICO MAXX', 'advance-trico-maxx');
INSERT INTO `manufacturers` VALUES (2430, 'VT9', 'ADVANCE/TRICO REAR WIPERS', 'advance-trico-rear-wipers');
INSERT INTO `manufacturers` VALUES (2431, 'W4F', 'FVP ROTOR AND DRUM2', 'fvp-rotor-and-drum2');
INSERT INTO `manufacturers` VALUES (2448, 'B0C', 'BURCO', 'burco');
INSERT INTO `manufacturers` VALUES (2449, 'NTU', 'NGK USA/NTK SENSORS', 'ngk-usa-ntk-sensors');
INSERT INTO `manufacturers` VALUES (2453, '9MT', 'OR/MURRAY HEAT TRANSFER', 'or-murray-heat-transfer');
INSERT INTO `manufacturers` VALUES (2454, 'C7P', 'CARTER WATER PUMPS', 'carter-water-pumps');
INSERT INTO `manufacturers` VALUES (2455, 'C8P', 'CARTER PREMIUM WATER PUMPS', 'carter-premium-water-pumps');
INSERT INTO `manufacturers` VALUES (2456, 'HKW', 'HK WHEELS', 'hk-wheels');
INSERT INTO `manufacturers` VALUES (2457, 'R2Y', 'REMY2020', 'remy2020');
INSERT INTO `manufacturers` VALUES (2458, 'URG', 'UNITED RECYCLERS GROUP', 'united-recyclers-group');
INSERT INTO `manufacturers` VALUES (2459, 'V89', 'ADVANCE/DIEHARD PLATINUM ROBUST', 'advance-diehard-platinum-robust');
INSERT INTO `manufacturers` VALUES (2469, '1A2', 'TRAIL RIDGE', 'trail-ridge');
INSERT INTO `manufacturers` VALUES (2470, 'E0P', 'ENEOS (MOTOR OIL/FLUIDS)', 'eneos-motor-oil-fluids');
INSERT INTO `manufacturers` VALUES (2471, 'EW1', 'EVEREST WAREHOUSE INC', 'everest-warehouse-inc');
INSERT INTO `manufacturers` VALUES (2472, 'GR3', 'GRAF', 'graf');
INSERT INTO `manufacturers` VALUES (2473, 'HBX', 'HUSKY TRUCK TOOL BOXES', 'husky-truck-tool-boxes');
INSERT INTO `manufacturers` VALUES (2474, 'MLI', 'METELLI', 'metelli');
INSERT INTO `manufacturers` VALUES (2475, 'PG6', 'PREMIUM GUARD PUREFLOW', 'premium-guard-pureflow');
INSERT INTO `manufacturers` VALUES (2476, 'RN5', 'RAIN-X CANADA ARCH', 'rain-x-canada-arch');
INSERT INTO `manufacturers` VALUES (2477, 'RN6', 'RAIN-X CANADA DISCOVER', 'rain-x-canada-discover');
INSERT INTO `manufacturers` VALUES (2478, 'RN7', 'RAIN-X CANADA WEATHERTRAC', 'rain-x-canada-weathertrac');
INSERT INTO `manufacturers` VALUES (2479, 'VB1', 'ADVANCE VALUE BATTERY', 'advance-value-battery');
INSERT INTO `manufacturers` VALUES (2484, 'A7D', 'AGILITY RADIATORS', 'agility-radiators');
INSERT INTO `manufacturers` VALUES (2485, 'A7E', 'AGILITY ENGINE OIL PANS', 'agility-engine-oil-pans');
INSERT INTO `manufacturers` VALUES (2486, 'A7F', 'AGILITY FUEL & EMISSIONS', 'agility-fuel-emissions');
INSERT INTO `manufacturers` VALUES (2487, 'A7W', 'AGILITY WIPER LINKAGES', 'agility-wiper-linkages');
INSERT INTO `manufacturers` VALUES (2488, 'A7X', 'AGILITY BRAKE CALIPERS', 'agility-brake-calipers');
INSERT INTO `manufacturers` VALUES (2489, 'A9A', 'AUTOPARTSRUNNERS', 'autopartsrunners');
INSERT INTO `manufacturers` VALUES (2490, 'AJA', 'AJUSA USA', 'ajusa-usa');
INSERT INTO `manufacturers` VALUES (2491, 'AT6', 'AUTOMOTIVE TENSIONERS INC (ATI)', 'automotive-tensioners-inc-ati');
INSERT INTO `manufacturers` VALUES (2492, 'B62', 'BASE HVAC', 'base-hvac');
INSERT INTO `manufacturers` VALUES (2493, 'BMV', 'BESTBUY BY MEVOTECH', 'bestbuy-by-mevotech');
INSERT INTO `manufacturers` VALUES (2494, 'BYB', 'BESTBUY BATTERIES', 'bestbuy-batteries');
INSERT INTO `manufacturers` VALUES (2495, 'CDR', 'CARDONE/REPAIR & RETURN', 'cardone-repair-return');
INSERT INTO `manufacturers` VALUES (2496, 'F9B', 'PRECISION BATTERIES', 'precision-batteries');
INSERT INTO `manufacturers` VALUES (2497, 'FLP', 'FLUID CAPACITIES', 'fluid-capacities');
INSERT INTO `manufacturers` VALUES (2498, 'MC9', 'MACPHERSON RIDE CHASSIS', 'macpherson-ride-chassis');
INSERT INTO `manufacturers` VALUES (2499, 'NVD', 'NISSAN VALUE ADVANTAGE', 'nissan-value-advantage');
INSERT INTO `manufacturers` VALUES (2500, 'P3S', 'PRO SERIES OE', 'pro-series-oe');
INSERT INTO `manufacturers` VALUES (2501, 'P8F', 'PRO-SERIES OE FILTERS', 'pro-series-oe-filters');
INSERT INTO `manufacturers` VALUES (2502, 'RO1', 'RAYBESTOS OVERSTOCK', 'raybestos-overstock');
INSERT INTO `manufacturers` VALUES (2503, 'RT1', 'RANGE TECHNOLOGY', 'range-technology');
INSERT INTO `manufacturers` VALUES (2504, 'TUS', 'TUNE UP SPECIFICATIONS', 'tune-up-specifications');
INSERT INTO `manufacturers` VALUES (2505, 'ULO', 'ULO', 'ulo');
INSERT INTO `manufacturers` VALUES (2506, 'WDP', 'WORLDPAC INC.', 'worldpac-inc');
INSERT INTO `manufacturers` VALUES (2560, '6EZ', 'JOHN DOW INDUSTRIES', 'john-dow-industries');
INSERT INTO `manufacturers` VALUES (2561, '703', 'LUBEGARD INTERNATIONAL LUBRICANTS, INC.', 'lubegard-international-lubricants-inc');
INSERT INTO `manufacturers` VALUES (2562, '707', 'MOTOR GUARD CORPORATION', 'motor-guard-corporation');
INSERT INTO `manufacturers` VALUES (2563, '708', 'OIL-DRI CORPORATION OF AMERICA', 'oil-dri-corporation-of-america');
INSERT INTO `manufacturers` VALUES (2564, '70D', 'AMERICAN FORGE AND FOUNDRY, INC.', 'american-forge-and-foundry-inc');
INSERT INTO `manufacturers` VALUES (2565, '70Z', 'IRWIN INDUSTRIAL TOOLS', 'irwin-industrial-tools');
INSERT INTO `manufacturers` VALUES (2566, '9F4', 'ENERGIZER', 'energizer');
INSERT INTO `manufacturers` VALUES (2567, 'GOQ', 'Go Gear', 'go-gear');
INSERT INTO `manufacturers` VALUES (2568, 'NVS', 'NVISION', 'nvision');
INSERT INTO `manufacturers` VALUES (2569, 'XCP', 'BLUE MAGIC', 'blue-magic');
INSERT INTO `manufacturers` VALUES (2570, 'XTE', 'VHT', 'vht');
INSERT INTO `manufacturers` VALUES (2575, 'O4C', 'O\'REILLY/ PRECISION CV AXLE', 'o-reilly-precision-cv-axle');
INSERT INTO `manufacturers` VALUES (2576, 'O5C', 'O\'REILLY/ IMPORT DIRECT CV AXLE', 'o-reilly-import-direct-cv-axle');
INSERT INTO `manufacturers` VALUES (2577, 'P4M', 'PIECE MEAL AUTO', 'piece-meal-auto');
INSERT INTO `manufacturers` VALUES (2578, 'QBS', 'CARQUEST PREMIUM STRUTS', 'carquest-premium-struts');
INSERT INTO `manufacturers` VALUES (2582, 'CFT', 'CASTROL FILTERS', 'castrol-filters');
INSERT INTO `manufacturers` VALUES (2583, 'EW9', 'EVEREST WAREHOUSE', 'everest-warehouse');
INSERT INTO `manufacturers` VALUES (2584, 'ICP', 'ICP/ CPI', 'icp-cpi');
INSERT INTO `manufacturers` VALUES (2585, 'O72', 'OR/MASTER PRO POWER STEERING', 'or-master-pro-power-steering');
INSERT INTO `manufacturers` VALUES (2586, 'OM3', 'MASTER PRO INTAKE MANIFOLD', 'master-pro-intake-manifold');
INSERT INTO `manufacturers` VALUES (2587, 'P4C', 'PARTS MASTER/ CHAMPION LABORATORIES', 'parts-master-champion-laboratories');
INSERT INTO `manufacturers` VALUES (2588, 'PTQ', 'POWER TORQUE - TORQUE CONVERTERS', 'power-torque-torque-converters');
INSERT INTO `manufacturers` VALUES (2589, 'Q5P', 'CARQUEST PROFESSIONAL HUB ASSEMBLIES', 'carquest-professional-hub-assemblies');
INSERT INTO `manufacturers` VALUES (2597, 'MA1', 'Motorad', 'motorad');
INSERT INTO `manufacturers` VALUES (2598, 'AIH', 'ASC', 'asc');
INSERT INTO `manufacturers` VALUES (2599, 'FCW', 'FRAM', 'fram');
INSERT INTO `manufacturers` VALUES (2600, 'MO5', 'Motorcraft', 'motorcraft');
INSERT INTO `manufacturers` VALUES (2609, 'ALM', 'ALTROM', 'altrom');
INSERT INTO `manufacturers` VALUES (2610, 'CST', 'CASTROL', 'castrol');
INSERT INTO `manufacturers` VALUES (2611, 'MA8', 'MAGMA BRAKES', 'magma-brakes');
INSERT INTO `manufacturers` VALUES (2612, 'NDA', 'NAVISTAR DIAMOND ADVANTAGE', 'navistar-diamond-advantage');
INSERT INTO `manufacturers` VALUES (2613, 'P3E', 'PROTEC-NEW', 'protec-new');
INSERT INTO `manufacturers` VALUES (2614, 'WC1', 'WABCO COMPRESSORS', 'wabco-compressors');
INSERT INTO `manufacturers` VALUES (2620, 'GY1', 'Goodyear', 'goodyear');
INSERT INTO `manufacturers` VALUES (2622, '70I', 'Chicago Pneumatic', 'chicago-pneumatic');
INSERT INTO `manufacturers` VALUES (2623, '70W', 'BADA', 'bada');
INSERT INTO `manufacturers` VALUES (2624, '71J', 'Seymour Spray Match Aerosols', 'seymour-spray-match-aerosols');
INSERT INTO `manufacturers` VALUES (2625, '7ZE', 'Wilmar', 'wilmar');
INSERT INTO `manufacturers` VALUES (2626, 'DPT', 'Dorman', 'dorman');
INSERT INTO `manufacturers` VALUES (2627, 'F0C', 'FTE', 'fte');
INSERT INTO `manufacturers` VALUES (2628, 'L0K', 'Lanair', 'lanair');
INSERT INTO `manufacturers` VALUES (2629, 'LTK', 'Launch', 'launch');
INSERT INTO `manufacturers` VALUES (2630, 'MC8', 'BBB Industries', 'bbb-industries');
INSERT INTO `manufacturers` VALUES (2631, 'NG0', 'Pig', 'pig');
INSERT INTO `manufacturers` VALUES (2632, 'P3C', 'Portacool', 'portacool');
INSERT INTO `manufacturers` VALUES (2633, 'PMX', 'Permatex', 'permatex');
INSERT INTO `manufacturers` VALUES (2634, 'Q8B', 'Quality-Built', 'quality-built');
INSERT INTO `manufacturers` VALUES (2635, 'QSB', 'QuickStart', 'quickstart');
INSERT INTO `manufacturers` VALUES (2636, 'V05', 'Valvoline', 'valvoline');
INSERT INTO `manufacturers` VALUES (2637, 'WB1', 'Winstop', 'winstop');
INSERT INTO `manufacturers` VALUES (2638, 'HS8', 'PREMIUM LINES', 'premium-lines');
INSERT INTO `manufacturers` VALUES (2641, 'TE7', 'TOTALENERGIES', 'totalenergies');

SET FOREIGN_KEY_CHECKS = 1;
