from django.shortcuts import get_object_or_404, render
from django.http import JsonResponse, HttpResponse, Http404
from django.views import View
from django.views.generic import TemplateView
from django.conf import settings
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.db import models
import json
from .models import (
    ManufacturerCode, UploadedFile, Retailer,
    CrawlJob, PricingData, ImportedDataTemp, DataMappingConfig, PartNumber
)
from .tasks import process_uploaded_file, start_crawling_task
from .utils import export_to_excel, export_to_csv


class WizardView(TemplateView):
    """Main wizard interface"""
    template_name = 'core/wizard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['retailers'] = Retailer.objects.filter(is_active=True)
        return context


@method_decorator(csrf_exempt, name='dispatch')
class UploadFileView(View):
    """Handle Excel file uploads"""
    
    def post(self, request):
        try:
            if 'file' not in request.FILES:
                return JsonResponse({'error': 'No file provided'}, status=400)

            uploaded_file = request.FILES['file']

            # Validate file type
            if not uploaded_file.name.lower().endswith(('.xlsx', '.xls')):
                return JsonResponse({'error': 'Invalid file type. Please upload an Excel file.'}, status=400)

            # Validate file size
            if uploaded_file.size > settings.FILE_UPLOAD_MAX_MEMORY_SIZE:
                return JsonResponse({'error': 'File too large. Maximum size is 10MB.'}, status=400)

            # Get selected manufacturer code from form data
            selected_mfgcode = request.POST.get('selected_mfgcode')
            selected_mfgcode_obj = None

            if selected_mfgcode:
                try:
                    selected_mfgcode_obj = ManufacturerCode.objects.get(mfgcode=selected_mfgcode)
                    # Store in session for later use
                    request.session['selected_mfgcode'] = selected_mfgcode
                except ManufacturerCode.DoesNotExist:
                    return JsonResponse({'error': f'Invalid manufacturer code: {selected_mfgcode}'}, status=400)

            # Save file
            file_instance = UploadedFile.objects.create(
                filename=uploaded_file.name,
                file_path=uploaded_file,
                file_size=uploaded_file.size,
                user=request.user if request.user.is_authenticated else None,
                selected_mfgcode=selected_mfgcode_obj
            )

            # Process file asynchronously
            process_uploaded_file.delay(str(file_instance.id))

            return JsonResponse({
                'success': True,
                'file_id': str(file_instance.id),
                'filename': uploaded_file.name,
                'size': uploaded_file.size,
                'mapping_url': f'/mapping/{file_instance.id}/',
                'selected_mfgcode': selected_mfgcode
            })
            
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


class ManufacturerCodesView(View):
    """Get manufacturer codes for dropdown with incremental search"""

    def get(self, request):
        search = request.GET.get('search', '').strip()
        limit = int(request.GET.get('limit', 50))  # Default to 50 results

        # Start with all codes, ordered by brand name
        codes = ManufacturerCode.objects.all().order_by('brand')

        if search:
            # Search primarily in brand name, secondarily in mfgcode
            codes = codes.filter(
                models.Q(brand__icontains=search) |
                models.Q(mfgcode__icontains=search)
            ).order_by(
                # Prioritize exact brand matches, then brand starts with, then contains
                models.Case(
                    models.When(brand__iexact=search, then=models.Value(1)),
                    models.When(brand__istartswith=search, then=models.Value(2)),
                    models.When(brand__icontains=search, then=models.Value(3)),
                    models.When(mfgcode__iexact=search, then=models.Value(4)),
                    models.When(mfgcode__istartswith=search, then=models.Value(5)),
                    default=models.Value(6),
                    output_field=models.IntegerField()
                ),
                'brand'
            )

        # Limit results for performance
        codes = codes[:limit]

        data = [
            {
                'mfgcode': code.mfgcode,
                'brand': code.brand,
                'display': f"{code.brand} ({code.mfgcode})"  # Show brand first, then code
            }
            for code in codes
        ]

        return JsonResponse({
            'codes': data,
            'total_count': ManufacturerCode.objects.count(),
            'filtered_count': len(data),
            'has_more': len(data) == limit
        })


@method_decorator(csrf_exempt, name='dispatch')
class StartCrawlView(View):
    """Start crawling process"""
    
    def post(self, request):
        try:
            data = json.loads(request.body)
            file_id = data.get('file_id')
            retailer_ids = data.get('retailers', [])
            mfg_code = data.get('mfg_code')
            use_proxy = data.get('use_proxy', False)
            
            # Validate inputs
            if not file_id or not retailer_ids:
                return JsonResponse({'error': 'Missing required parameters'}, status=400)
            
            uploaded_file = get_object_or_404(UploadedFile, id=file_id)
            retailers = Retailer.objects.filter(id__in=retailer_ids, is_active=True)
            
            if not retailers.exists():
                return JsonResponse({'error': 'No valid retailers selected'}, status=400)
            
            # Update manufacturer code if provided
            if mfg_code:
                mfg_code_obj = get_object_or_404(ManufacturerCode, mfgcode=mfg_code)
                uploaded_file.selected_mfgcode = mfg_code_obj
                uploaded_file.save()
            
            # Create crawl job
            crawl_job = CrawlJob.objects.create(
                uploaded_file=uploaded_file,
                total_parts=uploaded_file.total_parts,
                use_proxy=use_proxy
            )
            crawl_job.retailers.set(retailers)
            
            # Start crawling task
            start_crawling_task.delay(str(crawl_job.id))
            
            return JsonResponse({
                'success': True,
                'job_id': str(crawl_job.id)
            })
            
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


class CrawlStatusView(View):
    """Get crawling status"""
    
    def get(self, request, job_id):
        try:
            crawl_job = get_object_or_404(CrawlJob, id=job_id)
            
            # Get retailer-specific progress
            retailer_progress = {}
            for retailer in crawl_job.retailers.all():
                pricing_data = PricingData.objects.filter(
                    crawl_job=crawl_job,
                    retailer=retailer
                ).count()
                retailer_progress[retailer.name.lower().replace(' ', '_')] = {
                    'name': retailer.name,
                    'processed': pricing_data,
                    'total': crawl_job.total_parts,
                    'percentage': round((pricing_data / crawl_job.total_parts) * 100, 1) if crawl_job.total_parts > 0 else 0
                }
            
            return JsonResponse({
                'status': crawl_job.status,
                'overall_progress': crawl_job.progress_percentage,
                'processed_parts': crawl_job.processed_parts,
                'total_parts': crawl_job.total_parts,
                'successful_crawls': crawl_job.successful_crawls,
                'failed_crawls': crawl_job.failed_crawls,
                'retailer_progress': retailer_progress,
                'error_message': crawl_job.error_message
            })
            
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


class ExportResultsView(View):
    """Get export options and summary"""
    
    def get(self, request, file_id):
        try:
            uploaded_file = get_object_or_404(UploadedFile, id=file_id)
            
            # Get latest completed crawl job
            crawl_job = CrawlJob.objects.filter(
                uploaded_file=uploaded_file,
                status='completed'
            ).order_by('-completed_at').first()
            
            if not crawl_job:
                return JsonResponse({'error': 'No completed crawl jobs found'}, status=404)
            
            # Calculate summary statistics
            total_products = crawl_job.total_parts
            retailers_count = crawl_job.retailers.count()
            price_points = PricingData.objects.filter(
                crawl_job=crawl_job,
                price__isnull=False
            ).count()
            success_rate = round((crawl_job.successful_crawls / (crawl_job.successful_crawls + crawl_job.failed_crawls)) * 100, 1) if (crawl_job.successful_crawls + crawl_job.failed_crawls) > 0 else 0
            
            return JsonResponse({
                'summary': {
                    'total_products': total_products,
                    'retailers_count': retailers_count,
                    'price_points': price_points,
                    'success_rate': success_rate
                },
                'job_id': str(crawl_job.id)
            })
            
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


class DownloadResultsView(View):
    """Download results in specified format"""
    
    def get(self, request, file_id, format):
        try:
            uploaded_file = get_object_or_404(UploadedFile, id=file_id)
            
            # Get latest completed crawl job
            crawl_job = CrawlJob.objects.filter(
                uploaded_file=uploaded_file,
                status='completed'
            ).order_by('-completed_at').first()
            
            if not crawl_job:
                raise Http404("No completed crawl jobs found")
            
            if format.lower() == 'excel':
                file_path = export_to_excel(crawl_job)
                content_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                filename = f"pricing_analysis_{uploaded_file.filename.split('.')[0]}.xlsx"
            elif format.lower() == 'csv':
                file_path = export_to_csv(crawl_job)
                content_type = 'text/csv'
                filename = f"pricing_analysis_{uploaded_file.filename.split('.')[0]}.csv"
            else:
                return JsonResponse({'error': 'Invalid format'}, status=400)
            
            # Serve file
            with open(file_path, 'rb') as f:
                response = HttpResponse(f.read(), content_type=content_type)
                response['Content-Disposition'] = f'attachment; filename="{filename}"'
                return response
                
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


class SettingsPageView(View):
    """Serve the settings page"""

    def get(self, request):
        return render(request, 'core/settings.html')


@method_decorator(csrf_exempt, name='dispatch')
class SettingsView(View):
    """Handle settings configuration"""

    def get(self, request):
        try:
            return JsonResponse({
                'proxy': {
                    'enabled': getattr(settings, 'WEBSHARE_PROXY_ENABLED', False),
                    'api_token': getattr(settings, 'WEBSHARE_API_TOKEN', ''),
                    'mode': getattr(settings, 'WEBSHARE_PROXY_MODE', 'direct'),
                    'country_codes': getattr(settings, 'WEBSHARE_COUNTRY_CODES', [])
                }
            })

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
    
    def post(self, request):
        try:
            data = json.loads(request.body)

            # For now, we'll just validate the data
            # In a production environment, you might want to update environment variables
            # or store these in a database configuration table

            proxy_data = data.get('proxy', {})

            # Validate API token if provided
            if proxy_data.get('enabled') and proxy_data.get('api_token'):
                from .webshare_client import WebshareClient

                client = WebshareClient(proxy_data['api_token'])
                test_result = client.test_connection()

                if not test_result['success']:
                    return JsonResponse({
                        'error': f'Invalid webshare.io API token: {test_result["error"]}'
                    }, status=400)

            # In a real implementation, you would save these settings
            # For now, we'll just return success
            return JsonResponse({
                'success': True,
                'message': 'Settings validated successfully. Update your .env file to persist changes.'
            })

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class SaveSettingsView(View):
    """Save settings configuration"""

    def post(self, request):
        try:
            data = json.loads(request.body)

            # Save proxy settings to ProxyConfiguration model
            proxy_data = data.get('proxy', {})
            if proxy_data:
                from .models import ProxyConfiguration

                # Disable all existing proxies if we're enabling this one
                if proxy_data.get('is_enabled'):
                    ProxyConfiguration.objects.all().update(is_enabled=False)

                # Create or update proxy configuration
                proxy, created = ProxyConfiguration.objects.get_or_create(
                    name='Webshare.io',
                    defaults={
                        'api_key': proxy_data.get('api_key', ''),
                        'is_enabled': proxy_data.get('is_enabled', False)
                    }
                )

                if not created:
                    proxy.api_key = proxy_data.get('api_key', '')
                    proxy.is_enabled = proxy_data.get('is_enabled', False)
                    proxy.save()

            # Database settings would typically be saved to environment variables
            # For now, we'll just validate and return success
            db_data = data.get('database', {})
            if db_data:
                # In a production environment, you might want to update .env file
                # or store these in a secure configuration table
                pass

            return JsonResponse({
                'success': True,
                'message': 'Settings saved successfully!'
            })

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class ProxyConfigView(View):
    """Get current proxy configuration"""

    def get(self, request):
        try:
            from .models import ProxyConfiguration
            proxy = ProxyConfiguration.objects.filter(is_enabled=True).first()

            if proxy:
                return JsonResponse({
                    'success': True,
                    'proxy': {
                        'api_key': proxy.api_key,  # In production, consider masking this
                        'is_enabled': proxy.is_enabled
                    }
                })
            else:
                return JsonResponse({
                    'success': True,
                    'proxy': None
                })

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class TestProxyView(View):
    """Test proxy connection"""

    def post(self, request):
        try:
            data = json.loads(request.body)
            api_key = data.get('api_key', '')

            if not api_key:
                return JsonResponse({'error': 'API key is required'}, status=400)

            # Test webshare.io API connection
            import requests

            # Test the webshare.io API endpoint
            headers = {
                'Authorization': f'Token {api_key}'
            }

            # Test API access by getting proxy list with required mode parameter
            response = requests.get('https://proxy.webshare.io/api/v2/proxy/list/?mode=direct&page_size=1',
                                  headers=headers, timeout=10)

            if response.status_code == 200:
                data = response.json()
                proxy_count = data.get('count', 0)
                return JsonResponse({
                    'success': True,
                    'message': f'API connection successful. Found {proxy_count} proxies available.'
                })
            elif response.status_code == 401:
                return JsonResponse({'error': 'Invalid API key - please check your webshare.io API key'}, status=400)
            elif response.status_code == 403:
                return JsonResponse({'error': 'Access forbidden - please check your webshare.io plan and permissions'}, status=400)
            else:
                return JsonResponse({'error': f'API test failed with status {response.status_code}. Please verify your API key.'}, status=400)

        except requests.exceptions.Timeout:
            return JsonResponse({'error': 'API connection timeout - please try again'}, status=400)
        except requests.exceptions.ConnectionError:
            return JsonResponse({'error': 'Cannot connect to webshare.io API'}, status=400)
        except Exception as e:
            return JsonResponse({'error': f'API test failed: {str(e)}'}, status=400)


@method_decorator(csrf_exempt, name='dispatch')
class TestDatabaseView(View):
    """Test database connection"""

    def post(self, request):
        try:
            data = json.loads(request.body)
            host = data.get('host', 'localhost')
            port = data.get('port', 3306)
            name = data.get('name', '')
            username = data.get('username', '')
            password = data.get('password', '')

            if not all([name, username]):
                return JsonResponse({'error': 'Database name and username are required'}, status=400)

            # Test database connection
            import mysql.connector
            from mysql.connector import Error

            try:
                connection = mysql.connector.connect(
                    host=host,
                    port=int(port),
                    database=name,
                    user=username,
                    password=password,
                    connection_timeout=10
                )

                if connection.is_connected():
                    cursor = connection.cursor()
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                    cursor.close()
                    connection.close()
                    return JsonResponse({'success': True, 'message': 'Database connection successful'})

            except Error as e:
                return JsonResponse({'error': f'Database connection failed: {str(e)}'}, status=400)

        except Exception as e:
            return JsonResponse({'error': f'Database test failed: {str(e)}'}, status=400)


@method_decorator(csrf_exempt, name='dispatch')
class CheckExistingUploadsView(View):
    """Check for existing uploads that can be continued"""

    def get(self, request):
        try:
            from .models import UploadedFile, ProductData, InterchangeData

            # Find uploads that have ProductData but no interchange data yet
            uploads_with_products = UploadedFile.objects.filter(
                product_data__isnull=False
            ).distinct()

            existing_uploads = []
            for upload in uploads_with_products:
                # Check if this upload has products that need interchange lookup
                products = ProductData.objects.filter(uploaded_file=upload)

                # Get products that have real interchange data (not debug records)
                # Check by part_number + part_type_id + whi_mfgcode combination
                # Use EXISTS to check if product has at least one real interchange
                from django.db.models import Exists, OuterRef
                real_interchanges_subquery = InterchangeData.objects.filter(
                    product_data=OuterRef('pk'),
                    part_number=OuterRef('PartNumber'),
                    part_type_id=OuterRef('PartTypeID'),
                    whi_mfgcode=upload.selected_mfgcode.mfgcode if upload.selected_mfgcode else ''
                ).exclude(
                    interchange_brand_name='DEBUG_URL'
                )

                products_with_interchanges = products.filter(
                    Exists(real_interchanges_subquery)
                )

                # If there are products without real interchanges, this upload can be continued
                if products.count() > products_with_interchanges.count():
                    existing_uploads.append({
                        'id': str(upload.id),
                        'filename': upload.filename,
                        'created_at': upload.created_at.isoformat(),
                        'product_count': products.count(),
                        'selected_mfgcode': upload.selected_mfgcode.brand if upload.selected_mfgcode else None
                    })

            return JsonResponse({
                'success': True,
                'uploads': existing_uploads
            })

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


class DataMappingPageView(View):
    """Serve the data mapping page"""

    def get(self, request, file_id):
        """Serve the data mapping template"""
        return render(request, 'core/data_mapping.html', {'file_id': file_id})


class DataMappingView(View):
    """Handle data mapping configuration"""

    def get(self, request, file_id):
        """Get mapping configuration and preview data"""
        try:
            uploaded_file = get_object_or_404(UploadedFile, id=file_id)

            # Get mapping configuration, create if doesn't exist
            mapping_config = DataMappingConfig.objects.filter(uploaded_file=uploaded_file).first()
            if not mapping_config:
                # Try to create mapping configuration from imported data
                from .tasks import process_uploaded_file
                try:
                    process_uploaded_file(str(uploaded_file.id))
                    mapping_config = DataMappingConfig.objects.filter(uploaded_file=uploaded_file).first()
                except Exception as e:
                    return JsonResponse({'error': f'Failed to create mapping configuration: {str(e)}'}, status=500)

                if not mapping_config:
                    return JsonResponse({'error': 'No mapping configuration found. Please re-upload the file.'}, status=404)

            # Get sample imported data (first 10 rows)
            sample_data = ImportedDataTemp.objects.filter(
                uploaded_file=uploaded_file
            ).order_by('row_number')[:10]

            sample_rows = [
                {
                    'row_number': row.row_number,
                    'raw_data': row.raw_data,
                    'is_valid': row.is_valid,
                    'validation_errors': row.validation_errors
                }
                for row in sample_data
            ]

            return JsonResponse({
                'file_info': {
                    'id': uploaded_file.id,
                    'filename': uploaded_file.filename,
                    'total_rows': uploaded_file.total_parts,
                    'status': uploaded_file.status
                },
                'source_columns': mapping_config.source_columns,
                'target_fields': mapping_config.target_fields,
                'current_mappings': mapping_config.column_mappings,
                'sample_data': sample_rows,
                'validation_rules': mapping_config.validation_rules
            })

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    def post(self, request, file_id):
        """Save mapping configuration"""
        try:
            uploaded_file = get_object_or_404(UploadedFile, id=file_id)
            data = json.loads(request.body)

            mapping_config = get_object_or_404(DataMappingConfig, uploaded_file=uploaded_file)
            mapping_config.column_mappings = data.get('mappings', {})
            mapping_config.validation_rules = data.get('validation_rules', {})
            mapping_config.save()

            return JsonResponse({'success': True})

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


class ValidateDataView(View):
    """Validate imported data based on mappings"""

    def post(self, request, file_id):
        """Run validation on imported data"""
        try:
            uploaded_file = get_object_or_404(UploadedFile, id=file_id)
            mapping_config = get_object_or_404(DataMappingConfig, uploaded_file=uploaded_file)

            # Get all imported data
            imported_data = ImportedDataTemp.objects.filter(uploaded_file=uploaded_file)

            validation_results = {
                'total_rows': imported_data.count(),
                'valid_rows': 0,
                'invalid_rows': 0,
                'errors': []
            }

            for row in imported_data:
                errors = []
                mapped_data = {}

                # Apply mappings and validate
                for target_field, source_column in mapping_config.column_mappings.items():
                    if source_column and source_column in row.raw_data:
                        value = row.raw_data[source_column]

                        # Apply validation rules
                        field_config = mapping_config.target_fields.get(target_field, {})

                        # Only validate required fields: PartNumber and PartTypeID
                        if target_field in ['PartNumber', 'PartTypeID'] and not value:
                            errors.append(f"{target_field} is required but empty")

                        if value and field_config.get('type') == 'DecimalField':
                            try:
                                float(str(value).replace(',', ''))
                            except (ValueError, TypeError):
                                errors.append(f"{target_field} must be a valid number")

                        mapped_data[target_field] = value

                # Update row validation status
                row.is_valid = len(errors) == 0
                row.validation_errors = errors
                row.mapped_data = mapped_data
                row.save()

                if row.is_valid:
                    validation_results['valid_rows'] += 1
                else:
                    validation_results['invalid_rows'] += 1
                    validation_results['errors'].extend([
                        f"Row {row.row_number}: {error}" for error in errors
                    ])

            return JsonResponse(validation_results)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


class ImportToProductionView(View):
    """Import validated data to production tables"""

    def post(self, request, file_id):
        """Import validated data to permanent tables"""
        try:
            uploaded_file = get_object_or_404(UploadedFile, id=file_id)

            # Get only valid rows
            valid_rows = ImportedDataTemp.objects.filter(
                uploaded_file=uploaded_file,
                is_valid=True
            )

            if not valid_rows.exists():
                return JsonResponse({'error': 'No valid rows to import'}, status=400)

            # Import to ProductData table
            products_created = 0
            products_skipped = 0

            for row in valid_rows:
                mapped_data = row.mapped_data

                if 'PartNumber' in mapped_data and mapped_data['PartNumber']:
                    # Prepare data for ProductData model
                    product_data = {
                        'uploaded_file': uploaded_file,
                        'PartNumber': mapped_data['PartNumber'],
                        'PartTypeID': mapped_data.get('PartTypeID', ''),
                        'AAIABrandID': mapped_data.get('AAIABrandID', ''),
                        'LineCode': mapped_data.get('LineCode', ''),
                        'whi_mfgcode': uploaded_file.selected_mfgcode.mfgcode if uploaded_file.selected_mfgcode else '',
                    }

                    # Add all other mapped fields
                    for field_name in ['CompanyName', 'AAIABrandLabel',
                                     'LineName', 'EpicorStdMfgPartNumber', 'PartType', 'LifeCycleCode',
                                     'NetworkItemDemand', 'ItemAffiliateEffectiveDate', 'cost']:
                        if field_name in mapped_data:
                            value = mapped_data[field_name]
                            # Handle date field conversion
                            if field_name == 'ItemAffiliateEffectiveDate' and value:
                                try:
                                    from datetime import datetime
                                    if isinstance(value, str):
                                        value = datetime.strptime(value, '%Y-%m-%d').date()
                                except:
                                    value = None
                            # Handle decimal field conversion
                            elif field_name == 'cost' and value:
                                try:
                                    value = float(str(value).replace(',', ''))
                                except:
                                    value = None
                            product_data[field_name] = value

                    from .models import ProductData
                    # Check for duplicates using composite key: PartNumber + PartTypeID + AAIABrandID + LineCode
                    product, created = ProductData.objects.get_or_create(
                        PartNumber=mapped_data['PartNumber'],
                        PartTypeID=mapped_data.get('PartTypeID', ''),
                        AAIABrandID=mapped_data.get('AAIABrandID', ''),
                        LineCode=mapped_data.get('LineCode', ''),
                        defaults=product_data
                    )

                    if created:
                        products_created += 1
                    else:
                        products_skipped += 1

            # Update file status
            uploaded_file.status = 'processed'
            uploaded_file.processed_parts = products_created
            uploaded_file.save()

            return JsonResponse({
                'success': True,
                'imported_count': products_created,
                'skipped_count': products_skipped,
                'message': f'Successfully imported {products_created} new products. {products_skipped} duplicates were skipped.',
                'redirect_to_step': 5  # Move to interchange step
            })

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


class InterchangeLookupView(View):
    """Handle WHI interchange lookup for imported products"""

    def get(self, request, file_id):
        """Get products needing interchange lookup"""
        try:
            uploaded_file = get_object_or_404(UploadedFile, id=file_id)

            # Get all products from this file that need interchange lookup
            from .models import ProductData, ImportedDataTemp
            products = ProductData.objects.filter(uploaded_file=uploaded_file)

            # Auto-import temp data if production is empty but temp has data
            if products.count() == 0:
                temp_data_count = ImportedDataTemp.objects.filter(uploaded_file=uploaded_file).count()
                # Also check if there are any ProductData records for this file at all (including from other uploads)
                any_existing_products = ProductData.objects.filter(uploaded_file=uploaded_file).exists()

                if temp_data_count > 0 and not any_existing_products:
                    try:
                        temp_records = ImportedDataTemp.objects.filter(uploaded_file=uploaded_file)

                        for temp_record in temp_records:
                            # Handle date conversion for ItemAffiliateEffectiveDate
                            effective_date = temp_record.mapped_data.get('ItemAffiliateEffectiveDate', '')
                            if effective_date:
                                try:
                                    from datetime import datetime
                                    # Try to parse various date formats
                                    if isinstance(effective_date, str):
                                        # Remove quotes and try to parse
                                        effective_date = effective_date.strip('"').strip("'")
                                        if ' ' in effective_date:
                                            # Has time component, extract just the date
                                            effective_date = effective_date.split(' ')[0]
                                        # Convert to proper date format
                                        parsed_date = datetime.strptime(effective_date, '%Y-%m-%d').date()
                                        effective_date = parsed_date
                                except:
                                    effective_date = None
                            else:
                                effective_date = None

                            # Create ProductData record from temp data (use mapped_data)
                            # Check if record already exists to prevent duplicates
                            existing_product = ProductData.objects.filter(
                                PartNumber=temp_record.mapped_data.get('PartNumber', ''),
                                PartTypeID=temp_record.mapped_data.get('PartTypeID', ''),
                                AAIABrandID=temp_record.mapped_data.get('AAIABrandID', ''),
                                LineCode=temp_record.mapped_data.get('LineCode', '')
                            ).first()

                            if not existing_product:
                                ProductData.objects.create(
                                    uploaded_file=uploaded_file,
                                    PartNumber=temp_record.mapped_data.get('PartNumber', ''),
                                    PartTypeID=temp_record.mapped_data.get('PartTypeID', ''),
                                    AAIABrandID=temp_record.mapped_data.get('AAIABrandID', ''),
                                    LineCode=temp_record.mapped_data.get('LineCode', ''),
                                    CompanyName=temp_record.mapped_data.get('CompanyName', ''),
                                    AAIABrandLabel=temp_record.mapped_data.get('AAIABrandLabel', ''),
                                    LineName=temp_record.mapped_data.get('LineName', ''),
                                    EpicorStdMfgPartNumber=temp_record.mapped_data.get('EpicorStdMfgPartNumber', ''),
                                    PartType=temp_record.mapped_data.get('PartType', ''),
                                    LifeCycleCode=temp_record.mapped_data.get('LifeCycleCode', ''),
                                    NetworkItemDemand=temp_record.mapped_data.get('NetworkItemDemand', ''),
                                    ItemAffiliateEffectiveDate=effective_date,
                                    cost=temp_record.mapped_data.get('cost', 0.0),
                                    whi_mfgcode=uploaded_file.selected_mfgcode.mfgcode if uploaded_file.selected_mfgcode else ''
                                )

                        # Refresh products queryset
                        products = ProductData.objects.filter(uploaded_file=uploaded_file)

                    except Exception as import_error:
                        return JsonResponse({'error': f'Failed to auto-import data: {str(import_error)}'}, status=500)

            # Check which products already have real interchange data (not debug records)
            # Use part_number + part_type_id + whi_mfgcode to properly identify processed products
            # Use EXISTS to check if product has at least one real interchange
            from django.db.models import Exists, OuterRef
            from .models import InterchangeData

            real_interchanges_subquery = InterchangeData.objects.filter(
                product_data=OuterRef('pk'),
                part_number=OuterRef('PartNumber'),
                part_type_id=OuterRef('PartTypeID'),
                whi_mfgcode=uploaded_file.selected_mfgcode.mfgcode if uploaded_file.selected_mfgcode else ''
            ).exclude(
                interchange_brand_name='DEBUG_URL'
            )

            products_with_real_interchanges = products.filter(
                Exists(real_interchanges_subquery)
            )

            products_needing_lookup = products.exclude(
                id__in=products_with_real_interchanges.values_list('id', flat=True)
            )

            # Get the WHI manufacturer code from the uploaded file or session
            whi_mfgcode = ''
            if uploaded_file.selected_mfgcode:
                whi_mfgcode = uploaded_file.selected_mfgcode.mfgcode
            else:
                # Fallback to session
                whi_mfgcode = request.session.get('selected_mfgcode', '')

            return JsonResponse({
                'total_products': products.count(),
                'products_with_interchanges': products_with_real_interchanges.count(),
                'products_needing_lookup': products_needing_lookup.count(),
                'whi_mfgcode': whi_mfgcode,
                'sample_products': [
                    {
                        'id': p.id,
                        'part_number': p.PartNumber,
                        'part_type': p.PartType,
                        'brand': p.AAIABrandLabel or p.CompanyName
                    } for p in products_needing_lookup[:10]
                ]
            })

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    def post(self, request, file_id):
        """Handle interchange lookup actions"""
        try:
            uploaded_file = get_object_or_404(UploadedFile, id=file_id)
            action = request.POST.get('action', 'preview')

            # Get WHI manufacturer code from the uploaded file or session
            whi_mfgcode = ''
            if uploaded_file.selected_mfgcode:
                whi_mfgcode = uploaded_file.selected_mfgcode.mfgcode
            else:
                # Fallback to session
                whi_mfgcode = request.session.get('selected_mfgcode', '')

            if not whi_mfgcode:
                return JsonResponse({'error': 'WHI manufacturer code not found. Please go back to Step 1 and select a manufacturer code.'}, status=400)

            if action == 'preview':
                # Test with first 5 products only
                from .tasks import preview_interchange_lookup_task
                task_id = preview_interchange_lookup_task.delay(str(uploaded_file.id), whi_mfgcode)

                return JsonResponse({
                    'success': True,
                    'task_id': str(task_id),
                    'message': 'Preview interchange lookup started (first 5 products)'
                })

            elif action == 'full_process':
                # Process all products after user confirmation
                from .tasks import start_interchange_lookup_task
                task_id = start_interchange_lookup_task.delay(str(uploaded_file.id), whi_mfgcode)

                return JsonResponse({
                    'success': True,
                    'task_id': str(task_id),
                    'message': 'Full interchange lookup started'
                })

            else:
                return JsonResponse({'error': 'Invalid action'}, status=400)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


class InterchangeResultsView(View):
    """Get interchange lookup results"""

    def get(self, request, file_id):
        """Get interchange results for preview or full processing"""
        try:
            uploaded_file = get_object_or_404(UploadedFile, id=file_id)

            from .models import ProductData, InterchangeData

            # Get products with interchange data
            products_with_interchanges = ProductData.objects.filter(
                uploaded_file=uploaded_file,
                interchanges__isnull=False
            ).distinct()

            results = []
            for product in products_with_interchanges:
                interchanges = product.interchanges.all()

                am_interchanges = []
                oe_interchanges = []

                for interchange in interchanges:
                    interchange_data = {
                        'brand_name': interchange.interchange_brand_name,
                        'part_number': interchange.interchange_part_number,
                        'mfgcode': interchange.interchange_mfgcode or 'N/A'
                    }

                    if interchange.interchange_type == 'AM':
                        am_interchanges.append(interchange_data)
                    else:
                        oe_interchanges.append(interchange_data)

                results.append({
                    'product': {
                        'part_number': product.PartNumber,
                        'part_type': product.PartType,
                        'brand': product.AAIABrandLabel or product.CompanyName
                    },
                    'am_interchanges': am_interchanges,
                    'oe_interchanges': oe_interchanges,
                    'total_interchanges': len(am_interchanges) + len(oe_interchanges)
                })

            return JsonResponse({
                'success': True,
                'results': results,
                'total_products_processed': len(results),
                'total_interchanges_found': sum(r['total_interchanges'] for r in results)
            })

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


class InterchangePageView(View):
    """Render the interchange lookup page"""

    def get(self, request, file_id):
        return render(request, 'core/interchange.html', {'file_id': file_id})


class DebugURLsView(View):
    """View debug URLs that were attempted during interchange lookup"""

    def get(self, request, file_id):
        try:
            uploaded_file = get_object_or_404(UploadedFile, id=file_id)

            from .models import InterchangeData

            # Get debug records that contain the URLs
            debug_records = InterchangeData.objects.filter(
                product_data__uploaded_file=uploaded_file,
                interchange_brand_name='DEBUG_URL'
            ).select_related('product_data')

            debug_data = []
            for record in debug_records:
                debug_data.append({
                    'part_number': record.product_data.PartNumber,
                    'part_type': record.product_data.PartTypeID,
                    'whi_mfgcode': record.whi_mfgcode,
                    'smartpage_url': record.smartpage_url,
                    'created_at': record.created_at.strftime('%Y-%m-%d %H:%M:%S')
                })

            return JsonResponse({
                'success': True,
                'debug_urls': debug_data,
                'total_attempts': len(debug_data)
            })

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


class InterchangeStatsView(View):
    """Get real-time interchange processing stats"""

    def get(self, request, file_id):
        try:
            uploaded_file = get_object_or_404(UploadedFile, id=file_id)

            from .models import ProductData, InterchangeData

            # Get total products
            total_products = ProductData.objects.filter(uploaded_file=uploaded_file).count()

            # Get products that have real interchange data (not debug records)
            # Use part_number + part_type_id + whi_mfgcode to properly identify processed products
            # Use EXISTS to check if product has at least one real interchange
            from django.db.models import Exists, OuterRef
            from .models import InterchangeData

            real_interchanges_subquery = InterchangeData.objects.filter(
                product_data=OuterRef('pk'),
                part_number=OuterRef('PartNumber'),
                part_type_id=OuterRef('PartTypeID'),
                whi_mfgcode=uploaded_file.selected_mfgcode.mfgcode if uploaded_file.selected_mfgcode else ''
            ).exclude(
                interchange_brand_name='DEBUG_URL'
            )

            products_with_real_interchanges = ProductData.objects.filter(
                uploaded_file=uploaded_file
            ).filter(
                Exists(real_interchanges_subquery)
            )

            products_with_interchanges = products_with_real_interchanges.count()

            # Products still needing lookup
            products_needing_lookup = total_products - products_with_interchanges

            # Get total interchange records found
            total_interchanges = InterchangeData.objects.filter(
                product_data__uploaded_file=uploaded_file
            ).exclude(
                interchange_brand_name='DEBUG_URL'
            ).count()

            return JsonResponse({
                'success': True,
                'total_products': total_products,
                'products_with_interchanges': products_with_interchanges,
                'products_needing_lookup': products_needing_lookup,
                'total_interchanges_found': total_interchanges,
                'processing_progress': round((products_with_interchanges / total_products * 100), 1) if total_products > 0 else 0
            })

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
