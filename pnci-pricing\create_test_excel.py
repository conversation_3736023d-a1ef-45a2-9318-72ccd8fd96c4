"""
Create a test Excel file for upload testing
"""

import pandas as pd
import os

def create_test_excel():
    # Sample automotive parts data
    data = {
        'Part Number': [
            'ATF-0WS',
            '0986437008', 
            'MS100914',
            '20-1001',
            'VR232',
            'F123456',
            'B789012',
            'C345678',
            'D901234',
            'E567890'
        ],
        'Description': [
            'Automatic Transmission Fluid',
            'Fuel Injection Pump',
            'Mass Air Flow Sensor',
            'Remanufactured Alternator',
            'Voltage Regulator',
            'Oil Filter',
            'Brake Pad Set',
            'Clutch Kit',
            'Drive Belt',
            'Engine Mount'
        ],
        'Manufacturer': [
            'AISIN',
            'BOSCH',
            'MOTORCRAFT',
            'CARDONE',
            'STANDARD',
            'FRAM',
            'BENDIX',
            'CENTERFORCE',
            'DAYCO',
            'ENERGY SUSPENSION'
        ],
        'Current Price': [
            29.99,
            450.00,
            125.50,
            89.95,
            45.00,
            12.99,
            65.00,
            275.00,
            35.50,
            55.00
        ],
        'Category': [
            'Fluids',
            'Fuel System',
            'Engine',
            'Electrical',
            'Electrical',
            'Filters',
            'Brakes',
            'Drivetrain',
            'Engine',
            'Suspension'
        ]
    }
    
    # Create DataFrame
    df = pd.DataFrame(data)
    
    # Save to Excel file
    output_file = 'test_parts_upload.xlsx'
    df.to_excel(output_file, index=False, sheet_name='Parts Data')
    
    print(f"✅ Test Excel file created: {output_file}")
    print(f"📊 Contains {len(df)} sample automotive parts")
    print(f"📁 File location: {os.path.abspath(output_file)}")
    
    # Show sample data
    print("\n📋 Sample data:")
    print(df.head().to_string(index=False))
    
    return output_file

if __name__ == '__main__':
    create_test_excel()
