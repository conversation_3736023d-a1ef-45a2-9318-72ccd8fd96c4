"""
Import manufacturers.sql data into SQLite database
"""

import sqlite3
import re
import os

def import_manufacturers():
    print("Starting manufacturer data import from manufacturers.sql...")
    
    # Paths
    sql_file = 'c:/mb/pnci-pricing/manufacturers.sql'
    sqlite_db = 'c:/mb/pnci-pricing/pnci-pricing/db.sqlite3'
    
    if not os.path.exists(sql_file):
        print(f"Error: {sql_file} not found!")
        return False
    
    if not os.path.exists(sqlite_db):
        print(f"Error: {sqlite_db} not found!")
        return False
    
    try:
        # Read the SQL file
        print('Reading manufacturers.sql file...')
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # Extract INSERT statements using regex
        insert_pattern = r"INSERT INTO `manufacturers` VALUES \((\d+), '([^']+)', '([^']+)', '[^']*'\);"
        matches = re.findall(insert_pattern, sql_content)
        
        print(f'Found {len(matches)} manufacturer records in SQL file')
        
        # Connect to SQLite database
        print('Connecting to SQLite database...')
        sqlite_conn = sqlite3.connect(sqlite_db)
        sqlite_cursor = sqlite_conn.cursor()
        
        # Clear existing manufacturer codes
        print('Clearing existing manufacturer codes...')
        sqlite_cursor.execute('DELETE FROM whi_mfgcodes')
        sqlite_conn.commit()
        print('Existing data cleared')
        
        # Import data to SQLite
        print('Importing manufacturers to local SQLite...')
        imported_count = 0
        
        for match in matches:
            id_val, mfg_code, mfg_name = match
            
            # Clean up the data
            mfgcode = mfg_code.strip()
            brand = mfg_name.strip()
            
            # Insert into SQLite (mapping to existing table structure)
            sqlite_cursor.execute(
                'INSERT INTO whi_mfgcodes (mfgcode, brand, created_at) VALUES (?, ?, datetime("now"))',
                (mfgcode, brand)
            )
            imported_count += 1
            
            if imported_count % 100 == 0:
                print(f'Imported {imported_count} manufacturers...')
        
        # Commit changes
        sqlite_conn.commit()
        
        # Get final count
        sqlite_cursor.execute('SELECT COUNT(*) FROM whi_mfgcodes')
        final_count = sqlite_cursor.fetchone()[0]
        
        # Show some sample data
        print('\nSample imported data:')
        sqlite_cursor.execute('SELECT mfgcode, brand FROM whi_mfgcodes ORDER BY brand LIMIT 10')
        samples = sqlite_cursor.fetchall()
        for mfgcode, brand in samples:
            print(f'  {mfgcode} - {brand}')
        
        # Close SQLite connection
        sqlite_cursor.close()
        sqlite_conn.close()
        
        # Summary
        print(f'''
Import completed successfully!
  Records found in SQL file: {len(matches)}
  Records imported: {imported_count}
  Total in local DB: {final_count}
        ''')
        
        return True
        
    except Exception as e:
        print(f'Error: {e}')
        return False

if __name__ == '__main__':
    success = import_manufacturers()
    if success:
        print("✅ Manufacturer data imported successfully!")
    else:
        print("❌ Failed to import manufacturer data")
