from celery import shared_task
from django.utils import timezone
from django.db import models
import pandas as pd
import time
import logging
from .models import (
    UploadedFile, PartNumber, InterchangeData, CrawlJob,
    PricingData, ManufacturerCode, ImportedDataTemp, DataMappingConfig
)
from .scrapers import WHISmartpageScraper, RetailerScraperFactory

logger = logging.getLogger(__name__)


@shared_task
def process_uploaded_file(file_id):
    """Process uploaded Excel file and import data into temporary table for validation"""
    try:
        uploaded_file = UploadedFile.objects.get(id=file_id)
        uploaded_file.status = 'processing'
        uploaded_file.save()

        # Read Excel file
        file_path = uploaded_file.file_path.path

        try:
            # Try reading with pandas first
            df = pd.read_excel(file_path)
        except Exception as e:
            # Fallback to openpyxl
            from openpyxl import load_workbook
            wb = load_workbook(file_path)
            ws = wb.active
            data = []
            headers = [cell.value for cell in ws[1]]
            for row in ws.iter_rows(min_row=2, values_only=True):
                data.append(dict(zip(headers, row)))
            df = pd.DataFrame(data)

        # Clean and process data
        df = df.dropna(how='all')  # Remove empty rows

        # Store source columns
        source_columns = df.columns.tolist()

        # Define target fields for the permanent table (matching your template structure)
        target_fields = {
            'AAIABrandID': {
                'type': 'CharField',
                'max_length': 50,
                'required': False,
                'description': 'AAIA Brand ID'
            },
            'CompanyName': {
                'type': 'CharField',
                'max_length': 255,
                'required': False,
                'description': 'Company Name'
            },
            'AAIABrandLabel': {
                'type': 'CharField',
                'max_length': 255,
                'required': False,
                'description': 'AAIA Brand Label'
            },
            'LineCode': {
                'type': 'CharField',
                'max_length': 50,
                'required': False,
                'description': 'Line Code'
            },
            'LineName': {
                'type': 'CharField',
                'max_length': 255,
                'required': False,
                'description': 'Line Name'
            },
            'PartNumber': {
                'type': 'CharField',
                'max_length': 100,
                'required': True,
                'description': 'Part Number (Required for interchange lookup)'
            },
            'EpicorStdMfgPartNumber': {
                'type': 'CharField',
                'max_length': 100,
                'required': False,
                'description': 'Epicor Standard Manufacturer Part Number'
            },
            'PartTypeID': {
                'type': 'CharField',
                'max_length': 50,
                'required': True,
                'description': 'Part Type ID (Required for interchange lookup)'
            },
            'PartType': {
                'type': 'CharField',
                'max_length': 100,
                'required': False,
                'description': 'Part Type Description'
            },
            'LifeCycleCode': {
                'type': 'CharField',
                'max_length': 50,
                'required': False,
                'description': 'Life Cycle Code'
            },
            'NetworkItemDemand': {
                'type': 'CharField',
                'max_length': 100,
                'required': False,
                'description': 'Network Item Demand'
            },
            'ItemAffiliateEffectiveDate': {
                'type': 'DateField',
                'required': False,
                'description': 'Item Affiliate Effective Date'
            },
            'cost': {
                'type': 'DecimalField',
                'max_digits': 10,
                'decimal_places': 2,
                'required': False,
                'description': 'Cost'
            }
        }

        # Create auto-mappings based on exact column name matches
        auto_mappings = {}

        # For each source column, check if it matches any target field exactly
        for source_col in source_columns:
            for target_field in target_fields.keys():
                if source_col == target_field:
                    auto_mappings[target_field] = source_col
                    break

        # Create mapping configuration
        mapping_config, created = DataMappingConfig.objects.get_or_create(
            uploaded_file=uploaded_file,
            defaults={
                'source_columns': source_columns,
                'target_fields': target_fields,
                'column_mappings': auto_mappings,
                'validation_rules': {}
            }
        )

        # Import all rows into temporary table
        imported_rows = []
        for index, row in df.iterrows():
            # Convert row to dictionary, handling NaN values and timestamps
            row_data = {}
            for col in df.columns:
                value = row[col]
                if pd.isna(value):
                    row_data[col] = None
                elif pd.api.types.is_datetime64_any_dtype(type(value)) or hasattr(value, 'strftime'):
                    # Convert datetime/timestamp to string
                    row_data[col] = str(value)
                elif isinstance(value, str):
                    row_data[col] = value.strip()
                else:
                    # Convert other types to string for JSON serialization
                    row_data[col] = str(value)

            imported_rows.append(ImportedDataTemp(
                uploaded_file=uploaded_file,
                row_number=index + 1,
                raw_data=row_data
            ))

        # Bulk create imported data
        ImportedDataTemp.objects.bulk_create(imported_rows, ignore_conflicts=True)

        uploaded_file.status = 'imported'  # New status for imported but not mapped
        uploaded_file.total_parts = len(imported_rows)
        uploaded_file.save()

        logger.info(f"Imported {len(imported_rows)} rows from {uploaded_file.filename} into temporary table")

    except Exception as e:
        logger.error(f"Error processing file {file_id}: {str(e)}")
        try:
            uploaded_file = UploadedFile.objects.get(id=file_id)
            uploaded_file.status = 'failed'
            uploaded_file.error_message = str(e)
            uploaded_file.save()
        except:
            pass


@shared_task
def start_crawling_task(job_id):
    """Start the crawling process for a job"""
    try:
        crawl_job = CrawlJob.objects.get(id=job_id)
        crawl_job.status = 'running'
        crawl_job.started_at = timezone.now()
        crawl_job.save()
        
        # Initialize scrapers with proxy configuration
        use_proxy = crawl_job.use_proxy
        whi_scraper = WHISmartpageScraper(use_proxy=use_proxy)
        retailer_scrapers = {}

        for retailer in crawl_job.retailers.all():
            retailer_scrapers[retailer.id] = RetailerScraperFactory.create_scraper(
                retailer.name, use_proxy=use_proxy
            )
        
        # Get part numbers to process
        part_numbers = PartNumber.objects.filter(
            uploaded_file=crawl_job.uploaded_file
        )
        
        processed_count = 0
        successful_count = 0
        failed_count = 0
        
        for part_number in part_numbers:
            try:
                # Step 1: Get interchanges from WHI smartpages
                interchanges = []
                if crawl_job.uploaded_file.selected_mfgcode:
                    mfg_code = crawl_job.uploaded_file.selected_mfgcode.mfgcode
                    interchanges = whi_scraper.get_interchanges(
                        mfg_code, part_number.part_number
                    )
                    
                    # Store interchange data
                    for interchange in interchanges:
                        InterchangeData.objects.get_or_create(
                            part_number=part_number,
                            interchange_part=interchange['part_number'],
                            defaults={
                                'interchange_brand': interchange.get('brand', ''),
                                'smartpage_url': interchange.get('url', '')
                            }
                        )
                
                # Step 2: Crawl retailers
                parts_to_search = [part_number.part_number]
                if interchanges:
                    parts_to_search.extend([i['part_number'] for i in interchanges])
                
                for retailer in crawl_job.retailers.all():
                    scraper = retailer_scrapers[retailer.id]
                    
                    for search_part in parts_to_search:
                        try:
                            pricing_data = scraper.search_part(search_part)
                            
                            if pricing_data:
                                PricingData.objects.create(
                                    crawl_job=crawl_job,
                                    part_number=part_number,
                                    retailer=retailer,
                                    searched_part=search_part,
                                    found_part=pricing_data.get('found_part', search_part),
                                    price=pricing_data.get('price'),
                                    availability=pricing_data.get('availability', ''),
                                    product_url=pricing_data.get('url', ''),
                                    product_name=pricing_data.get('name', ''),
                                    brand=pricing_data.get('brand', ''),
                                    additional_data=pricing_data.get('additional_data', {})
                                )
                                successful_count += 1
                                break  # Found a match, move to next retailer
                            
                            # Add delay between requests
                            time.sleep(retailer.crawl_delay)
                            
                        except Exception as e:
                            logger.error(f"Error crawling {retailer.name} for part {search_part}: {str(e)}")
                            failed_count += 1
                
                processed_count += 1
                
                # Update progress
                crawl_job.processed_parts = processed_count
                crawl_job.successful_crawls = successful_count
                crawl_job.failed_crawls = failed_count
                crawl_job.save()
                
            except Exception as e:
                logger.error(f"Error processing part {part_number.part_number}: {str(e)}")
                failed_count += 1
        
        # Complete the job
        crawl_job.status = 'completed'
        crawl_job.completed_at = timezone.now()
        crawl_job.processed_parts = processed_count
        crawl_job.successful_crawls = successful_count
        crawl_job.failed_crawls = failed_count
        crawl_job.save()
        
        logger.info(f"Completed crawl job {job_id}: {successful_count} successful, {failed_count} failed")
        
    except Exception as e:
        logger.error(f"Error in crawl job {job_id}: {str(e)}")
        crawl_job.status = 'failed'
        crawl_job.error_message = str(e)
        crawl_job.completed_at = timezone.now()
        crawl_job.save()


@shared_task
def populate_manufacturer_codes():
    """Populate manufacturer codes from embedded CSV data"""
    from .data.manufacturer_codes import MANUFACTURER_CODES_CSV
    import csv
    from io import StringIO
    
    try:
        # Clear existing codes
        ManufacturerCode.objects.all().delete()
        
        # Parse CSV data
        csv_reader = csv.DictReader(StringIO(MANUFACTURER_CODES_CSV))
        
        codes_to_create = []
        for row in csv_reader:
            codes_to_create.append(
                ManufacturerCode(
                    mfgcode=row['mfgcode'].strip('"'),
                    brand=row['brand'].strip('"')
                )
            )
        
        # Bulk create
        ManufacturerCode.objects.bulk_create(codes_to_create, batch_size=100)
        
        logger.info(f"Populated {len(codes_to_create)} manufacturer codes")
        
    except Exception as e:
        logger.error(f"Error populating manufacturer codes: {str(e)}")


@shared_task
def preview_interchange_lookup_task(uploaded_file_id, whi_mfgcode):
    """
    Preview WHI interchange lookup for first 5 products only
    """
    from .models import UploadedFile, ProductData, InterchangeData, ManufacturerCode, ProxyConfiguration
    import requests
    from bs4 import BeautifulSoup
    import time

    try:
        logger.info(f"TASK STARTED: preview_interchange_lookup_task for file {uploaded_file_id} with mfgcode {whi_mfgcode}")

        uploaded_file = UploadedFile.objects.get(id=uploaded_file_id)
        logger.info(f"Found uploaded file: {uploaded_file.filename}")

        # Get first 5 products that need interchange lookup
        all_products = ProductData.objects.filter(uploaded_file=uploaded_file)
        logger.info(f"Total products in file: {all_products.count()}")

        # Get first 5 products that need processing for preview
        products_needing_lookup = ProductData.get_products_needing_interchange(uploaded_file, whi_mfgcode)
        products = products_needing_lookup[:5]
        logger.info(f"Selected {products.count()} products for preview (from {products_needing_lookup.count()} needing lookup)")

        if not products.exists():
            logger.info("No products found for interchange lookup")
            return

        # Get proxy configuration - MANDATORY for WHI scraping
        proxy_config = ProxyConfiguration.objects.filter(is_enabled=True).first()
        if not proxy_config or not proxy_config.api_key:
            logger.warning("No enabled proxy configuration found. Will create debug records but skip actual scraping.")
            proxy_rotator = None
        else:
            # Setup webshare.io proxy rotator
            from .webshare_client import WebshareClient, ProxyRotator
            try:
                client = WebshareClient(proxy_config.api_key)
                proxy_rotator = ProxyRotator(client)
                if proxy_rotator.initialize_proxy_pool():
                    logger.info(f"Initialized webshare.io proxy pool: {proxy_config.name}")
                else:
                    logger.error("Failed to initialize webshare.io proxy pool")
                    proxy_rotator = None
            except Exception as proxy_error:
                logger.error(f"Error setting up webshare.io proxy: {proxy_error}")
                proxy_rotator = None

        # Get WHI brand name for the manufacturer code
        try:
            whi_mfg = ManufacturerCode.objects.get(mfgcode=whi_mfgcode)
            whi_brand_name = whi_mfg.brand
        except ManufacturerCode.DoesNotExist:
            whi_brand_name = whi_mfgcode

        logger.info(f"Starting PREVIEW interchange lookup for {products.count()} products using WHI code: {whi_mfgcode} with proxy: {proxy_config.name}")

        for product in products:
            try:
                # Build WHI smart page URL - CORRECT FORMAT
                smartpage_url = f"https://smartpages.nexpart.com/smartpage.php?mfrlinecode={whi_mfgcode}&partnumber={product.PartNumber}&acesptermid={product.PartTypeID}"

                logger.info(f"Processing {product.PartNumber} - {smartpage_url}")

                # ALWAYS store the URL for debugging, even if no interchanges found
                logger.info(f"Creating debug record for {product.PartNumber}")
                try:
                    debug_record, created = InterchangeData.objects.get_or_create(
                        product_data=product,
                        part_number=product.PartNumber,  # Add the missing part_number field
                        part_type_id=product.PartTypeID,  # Add the missing part_type_id field
                        interchange_type='AM',  # Use AM since DEBUG is too long
                        interchange_brand_name='DEBUG_URL',
                        interchange_part_number=f'DEBUG_{product.PartNumber}',
                        defaults={
                            'whi_mfgcode': whi_mfgcode,
                            'whi_brand_name': whi_brand_name,
                            'smartpage_url': smartpage_url,
                            'interchange_mfgcode': 'DEBUG'
                        }
                    )
                    logger.info(f"Debug record created with ID: {debug_record.id}")
                except Exception as debug_error:
                    logger.error(f"Failed to create debug record: {debug_error}")

                # Only scrape if we have proxy configuration
                if proxy_rotator:
                    try:
                        # Get a fresh proxy for each request (rotation)
                        current_proxy = proxy_rotator.get_random_proxy()

                        # Scrape the WHI smart page with MANDATORY proxy
                        response = requests.get(smartpage_url, proxies=current_proxy, timeout=30)
                        if response.status_code == 200:
                            soup = BeautifulSoup(response.content, 'html.parser')

                            # Look for interchange tables
                            # Aftermarket Interchange tab
                            am_interchanges = scrape_interchange_table_with_proxy(soup, 'AM', product, whi_mfgcode, whi_brand_name, smartpage_url)

                            # OE Interchange tab
                            oe_interchanges = scrape_interchange_table_with_proxy(soup, 'OE', product, whi_mfgcode, whi_brand_name, smartpage_url)

                            total_interchanges = am_interchanges + oe_interchanges
                            logger.info(f"{product.PartNumber}: {total_interchanges} interchanges")
                        else:
                            logger.warning(f"HTTP {response.status_code} for {smartpage_url}")
                    except Exception as scrape_error:
                        logger.error(f"Error scraping {smartpage_url}: {scrape_error}")
                else:
                    logger.info(f"Skipping scraping for {product.PartNumber} - no proxy configured")

                # Add delay to be respectful to WHI servers
                time.sleep(3)  # Slightly longer delay for preview

            except Exception as e:
                logger.error(f"Error processing product {product.PartNumber}: {str(e)}")
                continue

        logger.info(f"Preview interchange lookup completed for {products.count()} products")

    except Exception as e:
        logger.error(f"Error in preview interchange lookup task: {str(e)}")


@shared_task
def start_interchange_lookup_task(uploaded_file_id, whi_mfgcode):
    """
    Start WHI interchange lookup for all products in the uploaded file
    """
    from .models import UploadedFile, ProductData, InterchangeData, ManufacturerCode, ProxyConfiguration
    import requests
    from bs4 import BeautifulSoup
    import time

    try:
        uploaded_file = UploadedFile.objects.get(id=uploaded_file_id)

        # Get all products that need interchange lookup (exclude those with real interchange data)
        all_products = ProductData.objects.filter(uploaded_file=uploaded_file)

        # Exclude products that already have real interchange data (not debug records)
        # Get products that have real interchange data (exclude debug records)
        # Use the new model method to get products that need processing
        products_needing_lookup = ProductData.get_products_needing_interchange(uploaded_file, whi_mfgcode)

        total_products = products_needing_lookup.count()
        total_all_products = all_products.count()
        processed_count = 0

        logger.info(f"Resume check: {total_all_products} total products, {total_products} still need processing")

        if total_products == 0:
            logger.info("All products already have interchange data - nothing to process")
            return

        # Get WHI manufacturer code
        if uploaded_file.selected_mfgcode:
            whi_mfgcode = uploaded_file.selected_mfgcode.mfgcode
        else:
            logger.error("No WHI manufacturer code found for this file")
            return

        # Get proxy configuration - MANDATORY for WHI scraping
        proxy_config = ProxyConfiguration.objects.filter(is_enabled=True).first()
        if not proxy_config or not proxy_config.api_key:
            logger.error("No enabled proxy configuration found. Will create debug records but skip actual scraping.")
            # Create debug records for all products to show URLs that would be attempted
            from .models import InterchangeData
            for product in products_needing_lookup:
                smartpage_url = f"https://smartpages.nexpart.com/smartpage.php?mfrlinecode={whi_mfgcode}&partnumber={product.PartNumber}&acesptermid={product.PartTypeID}"
                InterchangeData.objects.get_or_create(
                    product_data=product,
                    part_number=product.PartNumber,  # Add the missing part_number field
                    part_type_id=product.PartTypeID,  # Add the missing part_type_id field
                    interchange_type='DEBUG',
                    interchange_part_number='NO_PROXY',
                    interchange_brand_name='DEBUG_URL',
                    defaults={
                        'whi_mfgcode': whi_mfgcode,
                        'whi_brand_name': 'NO_PROXY_CONFIG',
                        'interchange_mfgcode': None,
                        'smartpage_url': smartpage_url
                    }
                )
            logger.info(f"Created debug records for {products_needing_lookup.count()} products - no proxy available")
            return

        # Setup webshare.io proxy rotator
        from .webshare_client import WebshareClient, ProxyRotator
        try:
            client = WebshareClient(proxy_config.api_key)
            proxy_rotator = ProxyRotator(client)
            if proxy_rotator.initialize_proxy_pool():
                logger.info(f"Initialized webshare.io proxy pool: {proxy_config.name}")
            else:
                logger.error("Failed to initialize webshare.io proxy pool")
                return
        except Exception as proxy_error:
            logger.error(f"Error setting up webshare.io proxy: {proxy_error}")
            return

        # Get WHI brand name for the manufacturer code
        try:
            whi_mfg = ManufacturerCode.objects.get(mfgcode=whi_mfgcode)
            whi_brand_name = whi_mfg.brand
        except ManufacturerCode.DoesNotExist:
            whi_brand_name = whi_mfgcode

        logger.info(f"Starting FULL interchange lookup for {total_products} products using WHI code: {whi_mfgcode} with proxy: {proxy_config.name}")

        for product in products_needing_lookup:
            try:
                # Build WHI smart page URL - CORRECT FORMAT
                smartpage_url = f"https://smartpages.nexpart.com/smartpage.php?mfrlinecode={whi_mfgcode}&partnumber={product.PartNumber}&acesptermid={product.PartTypeID}"

                # Get a fresh proxy for each request (rotation)
                current_proxy = proxy_rotator.get_random_proxy()

                # Scrape the WHI smart page with MANDATORY proxy
                response = requests.get(smartpage_url, proxies=current_proxy, timeout=30)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')

                    # Look for interchange tables
                    # Aftermarket Interchange tab
                    am_interchanges = scrape_interchange_table_with_proxy(soup, 'AM', product, whi_mfgcode, whi_brand_name, smartpage_url)

                    # OE Interchange tab
                    oe_interchanges = scrape_interchange_table_with_proxy(soup, 'OE', product, whi_mfgcode, whi_brand_name, smartpage_url)

                    total_interchanges = am_interchanges + oe_interchanges
                    logger.info(f"{product.PartNumber}: {total_interchanges} interchanges")

                processed_count += 1

                # Add delay to be respectful to WHI servers
                time.sleep(2)

            except Exception as e:
                logger.error(f"Error processing product {product.PartNumber}: {str(e)}")
                continue

        logger.info(f"Full interchange lookup completed. Processed {processed_count}/{total_products} products")

    except Exception as e:
        logger.error(f"Error in interchange lookup task: {str(e)}")


def scrape_interchange_table_with_proxy(soup, interchange_type, product, whi_mfgcode, whi_brand_name, smartpage_url):
    """
    Scrape interchange data from WHI smart page tables
    Returns count of interchanges found
    """
    from .models import InterchangeData, ManufacturerCode

    interchange_count = 0

    try:
        # Look for the specific interchange content divs based on type
        if interchange_type == 'OE':
            content_div = soup.find('div', id='oeInterchangeBtnContent')
        elif interchange_type == 'AM':
            content_div = soup.find('div', id='amInterchangeBtnContent')
        else:
            logger.warning(f"Unknown interchange type: {interchange_type}")
            return 0

        if not content_div:
            logger.info(f"No {interchange_type} interchange content found")
            return 0

        # Find all pairWrap divs that contain the interchange data
        pair_wraps = content_div.find_all('div', class_='pairWrap')

        for pair_wrap in pair_wraps:
            # Skip header rows (they have accentcolor2 class)
            if 'accentcolor2' in pair_wrap.get('class', []):
                continue

            # Find manufacturer (col4_1) and part number (col4_2)
            manufacturer_div = pair_wrap.find('div', class_='col4_1')
            part_number_div = pair_wrap.find('div', class_='col4_2')

            if manufacturer_div and part_number_div:
                interchange_brand = manufacturer_div.get_text().strip()
                interchange_part = part_number_div.get_text().strip()

                if interchange_brand and interchange_part:
                    # Look up the interchange manufacturer code
                    interchange_mfgcode = None
                    try:
                        mfg_obj = ManufacturerCode.objects.filter(brand__icontains=interchange_brand).first()
                        if mfg_obj:
                            interchange_mfgcode = mfg_obj.mfgcode
                    except:
                        pass

                    # Create interchange record
                    InterchangeData.objects.get_or_create(
                        product_data=product,
                        part_number=product.PartNumber,  # Add the missing part_number field
                        part_type_id=product.PartTypeID,  # Add the missing part_type_id field
                        interchange_type=interchange_type,
                        interchange_part_number=interchange_part,
                        interchange_brand_name=interchange_brand,
                        defaults={
                            'whi_mfgcode': whi_mfgcode,
                            'whi_brand_name': whi_brand_name,
                            'interchange_mfgcode': interchange_mfgcode,
                            'smartpage_url': smartpage_url
                        }
                    )
                    interchange_count += 1

        # Only log if interchanges were found
        if interchange_count > 0:
            logger.info(f"Found {interchange_count} {interchange_type} interchanges for {product.PartNumber}")

    except Exception as e:
        logger.error(f"Error scraping {interchange_type} interchange table: {str(e)}")

    return interchange_count
