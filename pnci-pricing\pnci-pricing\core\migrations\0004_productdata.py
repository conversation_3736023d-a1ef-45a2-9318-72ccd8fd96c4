# Generated by Django 4.2.7 on 2025-06-03 14:33

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0003_datamappingconfig_importeddatatemp'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('AAIABrandID', models.CharField(blank=True, max_length=50, null=True)),
                ('CompanyName', models.CharField(blank=True, max_length=255, null=True)),
                ('AAIABrandLabel', models.CharField(blank=True, max_length=255, null=True)),
                ('LineCode', models.CharField(blank=True, max_length=50, null=True)),
                ('LineName', models.CharField(blank=True, max_length=255, null=True)),
                ('PartNumber', models.Char<PERSON><PERSON>(db_index=True, max_length=100)),
                ('EpicorStdMfgPartNumber', models.CharField(blank=True, max_length=100, null=True)),
                ('PartTypeID', models.CharField(db_index=True, max_length=50)),
                ('PartType', models.CharField(blank=True, max_length=100, null=True)),
                ('LifeCycleCode', models.CharField(blank=True, max_length=50, null=True)),
                ('NetworkItemDemand', models.CharField(blank=True, max_length=100, null=True)),
                ('ItemAffiliateEffectiveDate', models.DateField(blank=True, null=True)),
                ('cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('whi_mfgcode', models.CharField(blank=True, max_length=10, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('uploaded_file', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_data', to='core.uploadedfile')),
            ],
            options={
                'db_table': 'product_data',
                'indexes': [models.Index(fields=['PartNumber'], name='product_dat_PartNum_7a6931_idx'), models.Index(fields=['PartTypeID'], name='product_dat_PartTyp_0b4d44_idx'), models.Index(fields=['uploaded_file', 'PartNumber'], name='product_dat_uploade_97a8ad_idx')],
                'unique_together': {('uploaded_file', 'PartNumber')},
            },
        ),
    ]
