"""
Test file upload functionality
"""

import requests
import os

def test_upload():
    # Check if test file exists
    test_file = 'test_parts_upload.xlsx'
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found")
        return
    
    print(f"📁 Testing upload of {test_file}")
    print(f"📊 File size: {os.path.getsize(test_file)} bytes")
    
    # Prepare the upload
    url = 'http://localhost:8000/api/upload-file/'
    
    # Get CSRF token first
    session = requests.Session()
    response = session.get('http://localhost:8000/')
    
    if response.status_code != 200:
        print(f"❌ Failed to get main page: {response.status_code}")
        return
    
    # Extract CSRF token from response
    csrf_token = None
    for line in response.text.split('\n'):
        if 'csrfToken' in line and '=' in line:
            # Extract token from JavaScript
            start = line.find("'") + 1
            end = line.find("'", start)
            if start > 0 and end > start:
                csrf_token = line[start:end]
                break
    
    if not csrf_token:
        print("❌ Could not extract CSRF token")
        return
    
    print(f"🔑 CSRF Token: {csrf_token[:10]}...")
    
    # Upload the file
    with open(test_file, 'rb') as f:
        files = {'file': f}
        data = {
            'selected_mfgcode': 'AIS',  # Test with AISIN
        }
        headers = {
            'X-CSRFToken': csrf_token
        }
        
        print("📤 Uploading file...")
        response = session.post(url, files=files, data=data, headers=headers)
    
    print(f"📊 Response Status: {response.status_code}")
    print(f"📄 Response Content: {response.text}")
    
    if response.status_code == 200:
        try:
            result = response.json()
            if result.get('success'):
                print("✅ Upload successful!")
                print(f"📁 File ID: {result.get('file_id')}")
                print(f"📝 Filename: {result.get('filename')}")
                print(f"📊 Size: {result.get('size')} bytes")
            else:
                print(f"❌ Upload failed: {result.get('error')}")
        except Exception as e:
            print(f"❌ Failed to parse JSON response: {e}")
    else:
        print(f"❌ Upload failed with status {response.status_code}")

if __name__ == '__main__':
    test_upload()
