from django.test import TestCase
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from .models import ManufacturerCode, UploadedFile, Retailer
import io
import openpyxl


class WizardViewTest(TestCase):
    def test_wizard_view_loads(self):
        """Test that the main wizard view loads correctly"""
        response = self.client.get(reverse('core:wizard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'PNCI Competitive Pricing Analysis')


class FileUploadTest(TestCase):
    def setUp(self):
        # Create a simple Excel file for testing
        wb = openpyxl.Workbook()
        ws = wb.active
        ws['A1'] = 'Part Number'
        ws['B1'] = 'Manufacturer'
        ws['A2'] = 'TEST123'
        ws['B2'] = 'TEST MFG'
        
        self.excel_file = io.BytesIO()
        wb.save(self.excel_file)
        self.excel_file.seek(0)

    def test_file_upload_success(self):
        """Test successful file upload"""
        uploaded_file = SimpleUploadedFile(
            "test.xlsx",
            self.excel_file.getvalue(),
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        
        response = self.client.post(
            reverse('core:upload_file'),
            {'file': uploaded_file}
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertIn('file_id', data)

    def test_file_upload_invalid_type(self):
        """Test upload with invalid file type"""
        uploaded_file = SimpleUploadedFile(
            "test.txt",
            b"test content",
            content_type="text/plain"
        )
        
        response = self.client.post(
            reverse('core:upload_file'),
            {'file': uploaded_file}
        )
        
        self.assertEqual(response.status_code, 400)
        data = response.json()
        self.assertIn('error', data)


class ManufacturerCodeTest(TestCase):
    def setUp(self):
        ManufacturerCode.objects.create(
            mfg_code='TEST',
            brand_name='Test Brand'
        )

    def test_manufacturer_codes_api(self):
        """Test manufacturer codes API endpoint"""
        response = self.client.get(reverse('core:manufacturer_codes'))
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertIn('codes', data)
        self.assertEqual(len(data['codes']), 1)
        self.assertEqual(data['codes'][0]['mfg_code'], 'TEST')

    def test_manufacturer_codes_search(self):
        """Test manufacturer codes search functionality"""
        response = self.client.get(
            reverse('core:manufacturer_codes'),
            {'search': 'test'}
        )
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertEqual(len(data['codes']), 1)


class RetailerTest(TestCase):
    def setUp(self):
        self.retailer = Retailer.objects.create(
            name='Test Retailer',
            website='https://test.com',
            base_search_url='https://test.com/search',
            is_active=True
        )

    def test_retailer_creation(self):
        """Test retailer model creation"""
        self.assertEqual(self.retailer.name, 'Test Retailer')
        self.assertTrue(self.retailer.is_active)

    def test_retailer_str_method(self):
        """Test retailer string representation"""
        self.assertEqual(str(self.retailer), 'Test Retailer')
