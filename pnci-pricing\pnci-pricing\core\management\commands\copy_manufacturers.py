"""
Management command to copy manufacturers from remote whi_aces.manufacturers to local SQLite
"""

from django.core.management.base import BaseCommand
from django.db import transaction
import mysql.connector
from mysql.connector import Error
from core.models import ManufacturerCode


class Command(BaseCommand):
    help = 'Copy manufacturers from remote whi_aces.manufacturers table to local SQLite'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing manufacturer codes before importing',
        )
        parser.add_argument(
            '--limit',
            type=int,
            default=None,
            help='Limit number of records to import (for testing)',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting manufacturer data copy...'))
        
        # Database connection parameters
        db_config = {
            'host': 'mysql.sophio.com',
            'port': 3306,
            'database': 'whi_aces',
            'user': 'facetedapi',
            'password': 'dtutuc954',
        }
        
        try:
            # Connect to MySQL database
            self.stdout.write('Connecting to MySQL database...')
            connection = mysql.connector.connect(**db_config)
            cursor = connection.cursor()
            
            # First, check the table structure
            cursor.execute("DESCRIBE manufacturers")
            columns = cursor.fetchall()
            self.stdout.write('Table structure:')
            for column in columns:
                self.stdout.write(f'  {column[0]} - {column[1]}')

            # Get total count
            cursor.execute("SELECT COUNT(*) FROM manufacturers")
            total_count = cursor.fetchone()[0]
            self.stdout.write(f'Found {total_count} manufacturers in remote database')

            # Build query with optional limit - using actual column names
            query = "SELECT id, name FROM manufacturers ORDER BY name"
            if options['limit']:
                query += f" LIMIT {options['limit']}"
                self.stdout.write(f'Limiting import to {options["limit"]} records')
            
            # Fetch data
            cursor.execute(query)
            manufacturers = cursor.fetchall()
            
            # Close MySQL connection
            cursor.close()
            connection.close()
            self.stdout.write('MySQL connection closed')
            
            # Clear existing data if requested
            if options['clear']:
                self.stdout.write('Clearing existing manufacturer codes...')
                ManufacturerCode.objects.all().delete()
                self.stdout.write(self.style.WARNING('Existing data cleared'))
            
            # Import data to SQLite
            self.stdout.write('Importing manufacturers to local SQLite...')
            imported_count = 0
            skipped_count = 0
            
            with transaction.atomic():
                for mfr_id, mfr_name in manufacturers:
                    # Use id as mfgcode and name as brand
                    mfgcode = str(mfr_id)
                    brand = mfr_name.strip() if mfr_name else f'Manufacturer {mfr_id}'
                    
                    # Check if already exists
                    if ManufacturerCode.objects.filter(mfgcode=mfgcode).exists():
                        skipped_count += 1
                        continue
                    
                    # Create new manufacturer code
                    ManufacturerCode.objects.create(
                        mfgcode=mfgcode,
                        brand=brand
                    )
                    imported_count += 1
                    
                    # Progress indicator
                    if imported_count % 100 == 0:
                        self.stdout.write(f'Imported {imported_count} manufacturers...')
            
            # Summary
            self.stdout.write(
                self.style.SUCCESS(
                    f'Import completed!\n'
                    f'  Total in remote DB: {total_count}\n'
                    f'  Imported: {imported_count}\n'
                    f'  Skipped (duplicates): {skipped_count}\n'
                    f'  Total in local DB: {ManufacturerCode.objects.count()}'
                )
            )
            
        except Error as e:
            self.stdout.write(
                self.style.ERROR(f'MySQL Error: {e}')
            )
            return
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error: {e}')
            )
            return
