from django.core.management.base import BaseCommand
from core.models import Manufacturer<PERSON><PERSON>, Retailer
from core.data.manufacturer_codes import MANUFACTURER_CODES_CSV
import csv
from io import StringIO


class Command(BaseCommand):
    help = 'Setup initial data for the PNCI tool'

    def handle(self, *args, **options):
        self.stdout.write('Setting up initial data...')
        
        # Setup manufacturer codes
        self.setup_manufacturer_codes()
        
        # Setup retailers
        self.setup_retailers()
        
        self.stdout.write(
            self.style.SUCCESS('Successfully set up initial data')
        )

    def setup_manufacturer_codes(self):
        self.stdout.write('Setting up manufacturer codes...')

        # Check if codes already exist
        existing_count = ManufacturerCode.objects.count()
        if existing_count > 0:
            self.stdout.write(
                f'Manufacturer codes already exist ({existing_count} codes). Skipping...'
            )
            return

        # Parse CSV data
        csv_reader = csv.DictReader(StringIO(MANUFACTURER_CODES_CSV))

        codes_to_create = []
        for row in csv_reader:
            codes_to_create.append(
                ManufacturerCode(
                    mfg_code=row['mfgcode'].strip('"'),
                    brand_name=row['brand'].strip('"')
                )
            )

        # Bulk create
        ManufacturerCode.objects.bulk_create(codes_to_create, batch_size=100)

        self.stdout.write(
            f'Created {len(codes_to_create)} manufacturer codes'
        )

    def setup_retailers(self):
        self.stdout.write('Setting up retailers...')
        
        retailers_data = [
            {
                'name': 'Advance Auto Parts',
                'website': 'https://www.advanceautoparts.com',
                'base_search_url': 'https://www.advanceautoparts.com/web/SearchResults',
                'crawl_delay': 2
            },
            {
                'name': 'AutoZone',
                'website': 'https://www.autozone.com',
                'base_search_url': 'https://www.autozone.com/search',
                'crawl_delay': 2
            },
            {
                'name': 'NAPA Auto Parts',
                'website': 'https://www.napaonline.com',
                'base_search_url': 'https://www.napaonline.com/search',
                'crawl_delay': 2
            },
            {
                'name': "O'Reilly Auto Parts",
                'website': 'https://www.oreillyauto.com',
                'base_search_url': 'https://www.oreillyauto.com/search',
                'crawl_delay': 2
            }
        ]
        
        for retailer_data in retailers_data:
            retailer, created = Retailer.objects.get_or_create(
                name=retailer_data['name'],
                defaults=retailer_data
            )
            if created:
                self.stdout.write(f'Created retailer: {retailer.name}')
            else:
                self.stdout.write(f'Retailer already exists: {retailer.name}')
        
        self.stdout.write(f'Setup complete for {len(retailers_data)} retailers')
