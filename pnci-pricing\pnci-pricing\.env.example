# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration
DB_NAME=pnci_tool
DB_USER=root
DB_PASSWORD=your-mysql-password
DB_HOST=localhost
DB_PORT=3306

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Webshare.io Proxy Configuration
WEBSHARE_PROXY_ENABLED=False
WEBSHARE_API_TOKEN=your-webshare-api-token
WEBSHARE_PROXY_MODE=direct
WEBSHARE_COUNTRY_CODES=US,CA,GB
