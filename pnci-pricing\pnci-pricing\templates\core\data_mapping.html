<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Mapping - PNCI Tool</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .mapping-row {
            transition: all 0.2s ease;
        }
        .mapping-row:hover {
            background-color: #f8fafc;
        }
        .validation-error {
            animation: shake 0.5s ease-in-out;
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    </style>
</head>
<body class="bg-gray-50">
    {% csrf_token %}
    <div class="min-h-screen">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center">
                        <i class="fas fa-table text-blue-600 text-2xl mr-3"></i>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">Data Mapping & Validation</h1>
                            <p class="text-sm text-gray-600" id="fileInfo">Loading file information...</p>
                        </div>
                    </div>
                    <div class="flex space-x-3">
                        <button onclick="window.history.back()" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg">
                            <i class="fas fa-arrow-left mr-2"></i>Back
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            
            <!-- Progress Steps -->
            <div class="mb-8">
                <div class="flex items-center justify-center">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                                <i class="fas fa-check"></i>
                            </div>
                            <span class="ml-2 text-sm font-medium text-green-600">File Uploaded</span>
                        </div>
                        <div class="w-16 h-1 bg-blue-500"></div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                                2
                            </div>
                            <span class="ml-2 text-sm font-medium text-blue-600">Map Columns</span>
                        </div>
                        <div class="w-16 h-1 bg-gray-300"></div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">
                                3
                            </div>
                            <span class="ml-2 text-sm font-medium text-gray-500">Validate Data</span>
                        </div>
                        <div class="w-16 h-1 bg-gray-300"></div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">
                                4
                            </div>
                            <span class="ml-2 text-sm font-medium text-gray-500">Import to Production</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Column Mapping Section -->
            <div class="bg-white rounded-lg shadow-sm border mb-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-arrows-alt-h text-blue-600 mr-2"></i>
                        Column Mapping
                    </h2>
                    <p class="text-sm text-gray-600 mt-1">Map your Excel columns to the target database fields</p>
                </div>
                
                <div class="p-6">
                    <div id="mappingContainer">
                        <!-- Mapping rows will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Data Preview Section -->
            <div class="bg-white rounded-lg shadow-sm border mb-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-eye text-blue-600 mr-2"></i>
                        Data Preview
                    </h2>
                    <p class="text-sm text-gray-600 mt-1">Preview of your imported data (first 10 rows)</p>
                </div>
                
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200" id="previewTable">
                            <thead class="bg-gray-50">
                                <!-- Headers will be populated here -->
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <!-- Data rows will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-600" id="statusMessage">
                    Configure column mappings to proceed
                </div>
                
                <div class="flex space-x-3">
                    <button id="validateBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                        <i class="fas fa-check-circle mr-2"></i>
                        Validate Data
                    </button>
                    <button id="importBtn" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg hidden">
                        <i class="fas fa-database mr-2"></i>
                        Import to Production
                    </button>
                </div>
            </div>

            <!-- Validation Results Modal -->
            <div id="validationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
                <div class="flex items-center justify-center min-h-screen p-4">
                    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-96 overflow-y-auto">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">Validation Results</h3>
                        </div>
                        <div class="p-6" id="validationResults">
                            <!-- Results will be populated here -->
                        </div>
                        <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                            <button onclick="closeValidationModal()" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let fileId = null;
        let mappingData = null;
        let currentMappings = {};

        // Get file ID from URL
        const urlParts = window.location.pathname.split('/');
        fileId = urlParts[urlParts.length - 2]; // Assuming URL like /mapping/{file_id}/

        document.addEventListener('DOMContentLoaded', function() {
            loadMappingData();
            
            document.getElementById('validateBtn').addEventListener('click', validateData);
            document.getElementById('importBtn').addEventListener('click', importToProduction);
        });

        function loadMappingData() {
            fetch(`/api/data-mapping/${fileId}/`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert('Error: ' + data.error);
                    return;
                }
                
                mappingData = data;
                currentMappings = data.current_mappings || {};
                
                updateFileInfo(data.file_info);
                renderMappingInterface(data.source_columns, data.target_fields);
                renderDataPreview(data.sample_data, data.source_columns);
                updateValidateButton();
            })
            .catch(error => {
                console.error('Error loading mapping data:', error);
                alert('Failed to load mapping data');
            });
        }

        function updateFileInfo(fileInfo) {
            document.getElementById('fileInfo').textContent = 
                `${fileInfo.filename} - ${fileInfo.total_rows} rows - Status: ${fileInfo.status}`;
        }

        function renderMappingInterface(sourceColumns, targetFields) {
            const container = document.getElementById('mappingContainer');
            
            let html = '<div class="space-y-4">';
            
            for (const [targetField, fieldConfig] of Object.entries(targetFields)) {
                const currentMapping = currentMappings[targetField] || '';
                const isRequired = fieldConfig.required ? ' *' : '';
                
                html += `
                    <div class="mapping-row p-4 border border-gray-200 rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">
                                    ${targetField}${isRequired}
                                </label>
                                <p class="text-xs text-gray-500 mt-1">${fieldConfig.description}</p>
                                <span class="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded mt-1">
                                    ${fieldConfig.type}
                                </span>
                            </div>
                            <div class="text-center text-gray-400">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                            <div>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                        onchange="updateMapping('${targetField}', this.value)">
                                    <option value="">-- Select Source Column --</option>
                                    ${sourceColumns.map(col => 
                                        `<option value="${col}" ${col === currentMapping ? 'selected' : ''}>${col}</option>`
                                    ).join('')}
                                </select>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            html += '</div>';
            container.innerHTML = html;
        }

        function renderDataPreview(sampleData, sourceColumns) {
            const table = document.getElementById('previewTable');
            
            // Create headers
            let headerHtml = '<tr>';
            sourceColumns.forEach(col => {
                headerHtml += `<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">${col}</th>`;
            });
            headerHtml += '</tr>';
            table.querySelector('thead').innerHTML = headerHtml;
            
            // Create data rows
            let bodyHtml = '';
            sampleData.forEach(row => {
                bodyHtml += '<tr>';
                sourceColumns.forEach(col => {
                    const value = row.raw_data[col] || '';
                    bodyHtml += `<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${value}</td>`;
                });
                bodyHtml += '</tr>';
            });
            table.querySelector('tbody').innerHTML = bodyHtml;
        }

        function updateMapping(targetField, sourceColumn) {
            currentMappings[targetField] = sourceColumn;
            updateValidateButton();
            saveMappings();
        }

        function updateValidateButton() {
            const validateBtn = document.getElementById('validateBtn');
            // Only require PartNumber and PartTypeID mappings
            const requiredFields = ['PartNumber', 'PartTypeID'];
            const hasRequiredMappings = requiredFields.every(field => currentMappings[field]);

            validateBtn.disabled = !hasRequiredMappings;

            if (hasRequiredMappings) {
                document.getElementById('statusMessage').textContent = 'Ready to validate data';
            } else {
                document.getElementById('statusMessage').textContent = 'Please map PartNumber and PartTypeID fields';
            }
        }

        function saveMappings() {
            fetch(`/api/data-mapping/${fileId}/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                },
                body: JSON.stringify({
                    mappings: currentMappings
                })
            })
            .catch(error => {
                console.error('Error saving mappings:', error);
            });
        }

        function validateData() {
            const validateBtn = document.getElementById('validateBtn');
            validateBtn.disabled = true;
            validateBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Validating...';
            
            fetch(`/api/validate-data/${fileId}/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                }
            })
            .then(response => response.json())
            .then(data => {
                showValidationResults(data);
                
                if (data.valid_rows > 0) {
                    document.getElementById('importBtn').classList.remove('hidden');
                }
                
                validateBtn.disabled = false;
                validateBtn.innerHTML = '<i class="fas fa-check-circle mr-2"></i>Validate Data';
            })
            .catch(error => {
                console.error('Error validating data:', error);
                alert('Validation failed');
                validateBtn.disabled = false;
                validateBtn.innerHTML = '<i class="fas fa-check-circle mr-2"></i>Validate Data';
            });
        }

        function showValidationResults(results) {
            const modal = document.getElementById('validationModal');
            const resultsDiv = document.getElementById('validationResults');
            
            let html = `
                <div class="space-y-4">
                    <div class="grid grid-cols-3 gap-4 text-center">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="text-2xl font-bold text-blue-600">${results.total_rows}</div>
                            <div class="text-sm text-blue-600">Total Rows</div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="text-2xl font-bold text-green-600">${results.valid_rows}</div>
                            <div class="text-sm text-green-600">Valid Rows</div>
                        </div>
                        <div class="bg-red-50 p-4 rounded-lg">
                            <div class="text-2xl font-bold text-red-600">${results.invalid_rows}</div>
                            <div class="text-sm text-red-600">Invalid Rows</div>
                        </div>
                    </div>
            `;
            
            if (results.errors.length > 0) {
                html += `
                    <div class="mt-6">
                        <h4 class="font-medium text-gray-900 mb-2">Validation Errors:</h4>
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4 max-h-40 overflow-y-auto">
                            <ul class="text-sm text-red-700 space-y-1">
                                ${results.errors.slice(0, 20).map(error => `<li>• ${error}</li>`).join('')}
                                ${results.errors.length > 20 ? `<li class="font-medium">... and ${results.errors.length - 20} more errors</li>` : ''}
                            </ul>
                        </div>
                    </div>
                `;
            }
            
            html += '</div>';
            resultsDiv.innerHTML = html;
            modal.classList.remove('hidden');
        }

        function closeValidationModal() {
            document.getElementById('validationModal').classList.add('hidden');
        }

        function importToProduction() {
            if (!confirm('Are you sure you want to import the valid data to production? This action cannot be undone.')) {
                return;
            }
            
            const importBtn = document.getElementById('importBtn');
            importBtn.disabled = true;
            importBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Importing...';
            
            fetch(`/api/import-to-production/${fileId}/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`Success! ${data.message}`);

                    // Check if we should redirect to interchange step
                    if (data.redirect_to_step === 5) {
                        window.location.href = `/interchange/${fileId}/`;
                    } else {
                        window.location.href = '/'; // Redirect to main page
                    }
                } else {
                    alert('Error: ' + data.error);
                }

                importBtn.disabled = false;
                importBtn.innerHTML = '<i class="fas fa-database mr-2"></i>Import to Production';
            })
            .catch(error => {
                console.error('Error importing data:', error);
                alert('Import failed');
                importBtn.disabled = false;
                importBtn.innerHTML = '<i class="fas fa-database mr-2"></i>Import to Production';
            });
        }

        function getCsrfToken() {
            return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
        }
    </script>
</body>
</html>
