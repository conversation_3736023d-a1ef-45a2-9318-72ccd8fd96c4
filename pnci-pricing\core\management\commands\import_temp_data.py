from django.core.management.base import BaseCommand
from core.models import UploadedFile, ProductData, ImportedDataTemp
from datetime import datetime


class Command(BaseCommand):
    help = 'Import temp data to production for a specific file'

    def add_arguments(self, parser):
        parser.add_argument('file_id', type=str, help='UploadedFile ID')
        parser.add_argument('--force', action='store_true', help='Force import even if production data exists')

    def handle(self, *args, **options):
        file_id = options['file_id']
        force = options.get('force', False)
        
        try:
            # Get the uploaded file
            uploaded_file = UploadedFile.objects.get(id=file_id)
            self.stdout.write(f"File: {uploaded_file.filename}")
            
            # Check existing data
            existing_products = ProductData.objects.filter(uploaded_file=uploaded_file).count()
            temp_records_count = ImportedDataTemp.objects.filter(uploaded_file=uploaded_file).count()
            
            self.stdout.write(f"Existing ProductData records: {existing_products}")
            self.stdout.write(f"Temp records to import: {temp_records_count}")
            
            if existing_products > 0 and not force:
                self.stdout.write(self.style.WARNING("ProductData already exists. Use --force to override."))
                return
            
            if temp_records_count == 0:
                self.stdout.write(self.style.ERROR("No temp data to import"))
                return
            
            # Clear existing ProductData if force is used
            if force and existing_products > 0:
                ProductData.objects.filter(uploaded_file=uploaded_file).delete()
                self.stdout.write(f"Deleted {existing_products} existing ProductData records")
            
            # Import temp data
            temp_records = ImportedDataTemp.objects.filter(uploaded_file=uploaded_file)
            imported_count = 0
            
            for temp_record in temp_records:
                # Handle date conversion for ItemAffiliateEffectiveDate
                effective_date = temp_record.mapped_data.get('ItemAffiliateEffectiveDate', '')
                if effective_date:
                    try:
                        # Try to parse various date formats
                        if isinstance(effective_date, str):
                            # Remove quotes and try to parse
                            effective_date = effective_date.strip('"').strip("'")
                            if ' ' in effective_date:
                                # Has time component, extract just the date
                                effective_date = effective_date.split(' ')[0]
                            # Convert to proper date format
                            parsed_date = datetime.strptime(effective_date, '%Y-%m-%d').date()
                            effective_date = parsed_date
                    except:
                        effective_date = None
                else:
                    effective_date = None

                # Create ProductData record from temp data
                product, created = ProductData.objects.get_or_create(
                    uploaded_file=uploaded_file,
                    PartNumber=temp_record.mapped_data.get('PartNumber', ''),
                    PartTypeID=temp_record.mapped_data.get('PartTypeID', ''),
                    AAIABrandID=temp_record.mapped_data.get('AAIABrandID', ''),
                    LineCode=temp_record.mapped_data.get('LineCode', ''),
                    defaults={
                        'CompanyName': temp_record.mapped_data.get('CompanyName', ''),
                        'AAIABrandLabel': temp_record.mapped_data.get('AAIABrandLabel', ''),
                        'LineName': temp_record.mapped_data.get('LineName', ''),
                        'EpicorStdMfgPartNumber': temp_record.mapped_data.get('EpicorStdMfgPartNumber', ''),
                        'PartType': temp_record.mapped_data.get('PartType', ''),
                        'LifeCycleCode': temp_record.mapped_data.get('LifeCycleCode', ''),
                        'NetworkItemDemand': temp_record.mapped_data.get('NetworkItemDemand', ''),
                        'ItemAffiliateEffectiveDate': effective_date,
                        'cost': temp_record.mapped_data.get('cost', 0.0)
                    }
                )
                
                if created:
                    imported_count += 1
            
            self.stdout.write(self.style.SUCCESS(f"Successfully imported {imported_count} records"))
            
            # Verify the import
            final_count = ProductData.objects.filter(uploaded_file=uploaded_file).count()
            self.stdout.write(f"Final ProductData count: {final_count}")
            
        except UploadedFile.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"File {file_id} not found"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error: {str(e)}"))
