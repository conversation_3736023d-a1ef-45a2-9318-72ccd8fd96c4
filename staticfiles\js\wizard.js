/**
 * PNCI Pricing Tool - Wizard JavaScript
 * Handles the multi-step wizard interface for competitive pricing analysis
 */

// Global variables
let currentStep = 1;
let totalSteps = 6;
let selectedMfgCode = null;
let uploadedFileId = null;
let crawlJobId = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeWizard();
    initializeFileUpload();
    initializeManufacturerSearch();
    initializeRetailerSelection();
    initializeSettings();
});

/**
 * Initialize the wizard interface
 */
function initializeWizard() {
    // Set up navigation buttons
    const nextBtn = document.getElementById('nextBtn');
    const prevBtn = document.getElementById('prevBtn');
    
    if (nextBtn) {
        nextBtn.addEventListener('click', nextStep);
    }
    
    if (prevBtn) {
        prevBtn.addEventListener('click', prevStep);
    }
    
    // Initialize step indicators
    updateStepIndicators();
}

/**
 * Move to next step
 */
function nextStep() {
    if (validateCurrentStep()) {
        if (currentStep < totalSteps) {
            currentStep++;
            showStep(currentStep);
            updateStepIndicators();
            updateNavigationButtons();
        }
    }
}

/**
 * Move to previous step
 */
function prevStep() {
    if (currentStep > 1) {
        currentStep--;
        showStep(currentStep);
        updateStepIndicators();
        updateNavigationButtons();
    }
}

/**
 * Show specific step
 */
function showStep(step) {
    // Hide all steps
    const steps = document.querySelectorAll('.wizard-step');
    steps.forEach(s => s.classList.remove('active'));
    
    // Show current step
    const currentStepEl = document.getElementById(`step-${step}`);
    if (currentStepEl) {
        currentStepEl.classList.add('active');
    }
}

/**
 * Update step indicators
 */
function updateStepIndicators() {
    const indicators = document.querySelectorAll('.step-indicator li');
    indicators.forEach((indicator, index) => {
        const stepNum = index + 1;
        indicator.classList.remove('active', 'completed');
        
        if (stepNum === currentStep) {
            indicator.classList.add('active');
        } else if (stepNum < currentStep) {
            indicator.classList.add('completed');
        }
    });
}

/**
 * Update navigation buttons
 */
function updateNavigationButtons() {
    const nextBtn = document.getElementById('nextBtn');
    const prevBtn = document.getElementById('prevBtn');
    
    if (prevBtn) {
        prevBtn.classList.toggle('hidden', currentStep === 1);
    }
    
    if (nextBtn) {
        if (currentStep === totalSteps) {
            nextBtn.textContent = 'Finish';
            nextBtn.innerHTML = '<i class="fas fa-check mr-2"></i>Finish';
        } else {
            nextBtn.innerHTML = 'Next <i class="fas fa-arrow-right ml-2"></i>';
        }
    }
}

/**
 * Validate current step
 */
function validateCurrentStep() {
    switch (currentStep) {
        case 1:
            return validateManufacturerSelection();
        case 2:
            return validateFileUpload();
        case 3:
            return validateColumnMapping();
        case 4:
            return validateRetailerSelection();
        case 5:
            return true; // Crawling step
        case 6:
            return true; // Results step
        default:
            return true;
    }
}

/**
 * Validate manufacturer selection
 */
function validateManufacturerSelection() {
    const selected = document.querySelector('input[name="mfgcode"]:checked');
    if (!selected) {
        showError('Please select a manufacturer code before proceeding.');
        return false;
    }
    selectedMfgCode = selected.value;
    return true;
}

/**
 * Validate file upload
 */
function validateFileUpload() {
    if (!uploadedFileId) {
        showError('Please upload an Excel file before proceeding.');
        return false;
    }
    return true;
}

/**
 * Validate column mapping
 */
function validateColumnMapping() {
    // Add validation logic for column mapping
    return true;
}

/**
 * Validate retailer selection
 */
function validateRetailerSelection() {
    const selected = document.querySelectorAll('input[name="retailers"]:checked');
    if (selected.length === 0) {
        showError('Please select at least one retailer before proceeding.');
        return false;
    }
    return true;
}

/**
 * Initialize file upload functionality
 */
function initializeFileUpload() {
    const fileDropArea = document.getElementById('fileDropArea');
    const fileInput = document.getElementById('fileInput');
    
    if (fileDropArea && fileInput) {
        // Click to browse
        fileDropArea.addEventListener('click', () => fileInput.click());
        
        // Drag and drop
        fileDropArea.addEventListener('dragover', handleDragOver);
        fileDropArea.addEventListener('drop', handleFileDrop);
        
        // File input change
        fileInput.addEventListener('change', handleFileSelect);
    }
}

/**
 * Handle drag over
 */
function handleDragOver(e) {
    e.preventDefault();
    e.currentTarget.classList.add('drag-over');
}

/**
 * Handle file drop
 */
function handleFileDrop(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('drag-over');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        uploadFile(files[0]);
    }
}

/**
 * Handle file select
 */
function handleFileSelect(e) {
    const files = e.target.files;
    if (files.length > 0) {
        uploadFile(files[0]);
    }
}

/**
 * Upload file to server
 */
function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('csrfmiddlewaretoken', window.csrfToken);
    
    showLoading('Uploading file...');
    
    fetch('/api/upload-file/', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            uploadedFileId = data.file_id;
            showFileInfo(file, data);
        } else {
            showError(data.error || 'Failed to upload file');
        }
    })
    .catch(error => {
        hideLoading();
        showError('Error uploading file: ' + error.message);
    });
}

/**
 * Show file information
 */
function showFileInfo(file, data) {
    const fileInfo = document.getElementById('fileInfo');
    if (fileInfo) {
        fileInfo.innerHTML = `
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-600 mr-3"></i>
                    <div>
                        <h5 class="font-semibold text-green-800">${file.name}</h5>
                        <p class="text-green-700 text-sm">
                            ${data.total_parts || 0} parts found
                        </p>
                    </div>
                </div>
            </div>
        `;
        fileInfo.classList.remove('hidden');
    }
}

/**
 * Initialize manufacturer search
 */
function initializeManufacturerSearch() {
    const searchInput = document.getElementById('mfgCodeSearch');
    if (searchInput) {
        searchInput.addEventListener('input', filterManufacturerCodes);
    }
}

/**
 * Filter manufacturer codes based on search
 */
function filterManufacturerCodes() {
    const searchTerm = document.getElementById('mfgCodeSearch').value.toLowerCase();
    const options = document.querySelectorAll('.mfg-code-option');
    
    options.forEach(option => {
        const text = option.textContent.toLowerCase();
        const visible = text.includes(searchTerm);
        option.style.display = visible ? 'block' : 'none';
    });
}

/**
 * Initialize retailer selection
 */
function initializeRetailerSelection() {
    // Add any retailer-specific initialization here
}

/**
 * Initialize settings functionality
 */
function initializeSettings() {
    const settingsBtn = document.getElementById('settingsBtn');
    const backToWizardBtn = document.getElementById('backToWizardBtn');
    
    if (settingsBtn) {
        settingsBtn.addEventListener('click', showSettings);
    }
    
    if (backToWizardBtn) {
        backToWizardBtn.addEventListener('click', hideSettings);
    }
}

/**
 * Show settings panel
 */
function showSettings() {
    document.getElementById('wizardContent').classList.add('hidden');
    document.getElementById('settingsContent').classList.remove('hidden');
    document.getElementById('mainNavigation').classList.add('hidden');
    document.getElementById('settingsNavigation').classList.remove('hidden');
}

/**
 * Hide settings panel
 */
function hideSettings() {
    document.getElementById('wizardContent').classList.remove('hidden');
    document.getElementById('settingsContent').classList.add('hidden');
    document.getElementById('mainNavigation').classList.remove('hidden');
    document.getElementById('settingsNavigation').classList.add('hidden');
}

/**
 * Show loading indicator
 */
function showLoading(message = 'Loading...') {
    // Implementation for loading indicator
    console.log('Loading:', message);
}

/**
 * Hide loading indicator
 */
function hideLoading() {
    // Implementation to hide loading indicator
    console.log('Loading complete');
}

/**
 * Show error message
 */
function showError(message) {
    alert('Error: ' + message);
    console.error('Error:', message);
}

/**
 * Show success message
 */
function showSuccess(message) {
    console.log('Success:', message);
}
