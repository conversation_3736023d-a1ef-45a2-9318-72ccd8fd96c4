# PNCI Tool Requirements

## Functional Requirements

### 1. File Upload and Processing
- Accept Excel files (.xlsx, .xls) from Part Share exports
- Parse Excel files to extract part numbers and related data
- Validate file format and content structure
- Store uploaded file metadata in database

### 2. Manufacturer Code Selection
- Display searchable dropdown of WHI manufacturer codes
- Load manufacturer codes from embedded CSV data
- Allow filtering/searching of manufacturer codes
- Store selected manufacturer code for processing

### 3. WHI Smartpages Integration
- Crawl WHI smartpages using URLs like: https://smartpages.nexpart.com/smartpage.php?mfrlinecode={CODE}&partnumber={PART}&acesptermid=1684
- Extract interchange part numbers from smartpages
- Store interchange data in database
- Handle rate limiting and error responses

### 4. Retailer Selection and Crawling
- Support multiple retailers: Advance Auto Parts, AutoZone, NAPA, O'Reilly
- Allow multi-selection of retailers for crawling
- Implement web scraping for each retailer's website
- Use webshare.io proxy service for scraping operations
- Track crawling progress with real-time updates

### 5. Data Processing Logic
- Primary crawling: Use interchange part numbers from WHI smartpages
- Fallback crawling: Use original manufacturer part numbers if interchange results are limited
- Store all pricing data with retailer attribution
- Handle duplicate part numbers and pricing conflicts

### 6. Export Functionality
- Generate pivot table format with one column per retailer
- Export to Excel format with formatting and formulas
- Export to CSV format for raw data processing
- Include analysis summary statistics

### 7. Configuration Management
- Proxy configuration (webshare.io credentials)
- Database connection settings
- Retailer-specific scraping parameters
- Rate limiting and timeout settings

## Technical Requirements

### 1. Backend Framework
- Django 4.x with Python 3.9+
- MySQL database with proper indexing
- Celery for background task processing
- Redis for task queue and session storage

### 2. Frontend Framework
- Tailwind CSS for responsive design
- JavaScript for wizard navigation and AJAX calls
- Bootstrap components for UI elements
- Font Awesome icons

### 3. Third-Party Integrations
- webshare.io proxy service
- Excel file processing libraries (openpyxl, xlrd)
- Web scraping libraries (requests, BeautifulSoup, Selenium)
- Export libraries (xlsxwriter, pandas)

### 4. Security Requirements
- Input validation for all file uploads
- SQL injection prevention
- XSS protection
- CSRF protection
- Secure proxy credential storage

### 5. Performance Requirements
- Handle Excel files up to 10MB
- Support concurrent crawling operations
- Implement caching for manufacturer codes
- Optimize database queries for large datasets
- Progress tracking for long-running operations

### 6. Error Handling
- Graceful handling of network timeouts
- Retry logic for failed scraping attempts
- User-friendly error messages
- Logging for debugging and monitoring
