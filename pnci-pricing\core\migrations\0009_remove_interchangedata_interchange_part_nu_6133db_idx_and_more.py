# Generated by Django 4.2.7 on 2025-06-03 22:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0008_alter_interchangedata_unique_together_and_more'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='interchangedata',
            name='interchange_part_nu_6133db_idx',
        ),
        migrations.AlterUniqueTogether(
            name='interchangedata',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='interchangedata',
            name='part_type_id',
            field=models.Char<PERSON>ield(db_index=True, default='UNKNOWN', max_length=50),
            preserve_default=False,
        ),
        migrations.AlterUniqueTogether(
            name='interchangedata',
            unique_together={('part_number', 'part_type_id', 'whi_mfgcode', 'interchange_type', 'interchange_part_number', 'interchange_brand_name')},
        ),
        migrations.AddIndex(
            model_name='interchangedata',
            index=models.Index(fields=['part_type_id'], name='interchange_part_ty_b0da79_idx'),
        ),
        migrations.AddIndex(
            model_name='interchangedata',
            index=models.Index(fields=['part_number', 'part_type_id', 'whi_mfgcode'], name='interchange_part_nu_126e82_idx'),
        ),
    ]
