from django.core.management.base import BaseCommand
from core.models import UploadedFile, ProductData, InterchangeData, ManufacturerCode


class Command(BaseCommand):
    help = 'Test debug URL creation for interchange lookup'

    def add_arguments(self, parser):
        parser.add_argument('file_id', type=str, help='UploadedFile ID')

    def handle(self, *args, **options):
        file_id = options['file_id']
        
        try:
            # Get the uploaded file
            uploaded_file = UploadedFile.objects.get(id=file_id)
            self.stdout.write(f"File: {uploaded_file.filename}")
            
            if not uploaded_file.selected_mfgcode:
                self.stdout.write(self.style.ERROR("No manufacturer code selected"))
                return
                
            whi_mfgcode = uploaded_file.selected_mfgcode.mfgcode
            whi_brand_name = uploaded_file.selected_mfgcode.brand
            self.stdout.write(f"WHI Code: {whi_mfgcode}")
            self.stdout.write(f"WHI Brand: {whi_brand_name}")
            
            # Get first 5 products
            products = ProductData.objects.filter(uploaded_file=uploaded_file)[:5]
            self.stdout.write(f"Found {products.count()} products to test")
            
            # Clean up any existing debug records
            existing_debug = InterchangeData.objects.filter(
                product_data__uploaded_file=uploaded_file,
                interchange_brand_name='DEBUG_URL'
            )
            if existing_debug.exists():
                count = existing_debug.count()
                existing_debug.delete()
                self.stdout.write(f"Deleted {count} existing debug records")
            
            # Create debug records for each product
            for i, product in enumerate(products, 1):
                # Build the correct WHI smart page URL
                smartpage_url = f"https://smartpages.nexpart.com/smartpage.php?mfrlinecode={whi_mfgcode}&partnumber={product.PartNumber}&acesptermid={product.PartTypeID}"
                
                # Create debug record (using 'AM' type but special brand name for debugging)
                debug_record = InterchangeData.objects.create(
                    product_data=product,
                    interchange_type='AM',  # Use AM since DEBUG is too long
                    interchange_brand_name='DEBUG_URL',
                    interchange_part_number=f'DEBUG_{product.PartNumber}',
                    whi_mfgcode=whi_mfgcode,
                    whi_brand_name=whi_brand_name,
                    smartpage_url=smartpage_url,
                    interchange_mfgcode='DEBUG'
                )
                
                self.stdout.write(f"{i}. {product.PartNumber} (Type: {product.PartTypeID})")
                self.stdout.write(f"   URL: {smartpage_url}")
                self.stdout.write(f"   Debug Record ID: {debug_record.id}")
                self.stdout.write("")
            
            self.stdout.write(self.style.SUCCESS(f"Created {products.count()} debug records"))
            self.stdout.write("Now you can click 'Debug URLs' button to see these URLs!")
            
        except UploadedFile.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"File {file_id} not found"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error: {str(e)}"))
