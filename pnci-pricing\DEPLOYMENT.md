# PNCI Tool Deployment Guide

## Quick Start (Development)

### Prerequisites
- Python 3.9+
- MySQL 5.7+ or 8.0+
- Redis server

### Setup Steps

1. **Run the setup script**
   ```bash
   python setup.py
   ```

2. **Update .env file with your database credentials**
   ```env
   DB_NAME=pnci_tool
   DB_USER=your_mysql_user
   DB_PASSWORD=your_mysql_password
   DB_HOST=localhost
   DB_PORT=3306
   ```

3. **Start the services**
   
   **Option A: Using batch files (Windows)**
   ```bash
   # Terminal 1: Start Django server
   start_server.bat
   
   # Terminal 2: Start Celery worker
   start_celery.bat
   ```
   
   **Option B: Manual commands**
   ```bash
   # Terminal 1: Start Django server
   python manage.py runserver
   
   # Terminal 2: Start Celery worker
   celery -A pnci_tool worker --loglevel=info
   ```

4. **Access the application**
   Open http://localhost:8000 in your browser

## Production Deployment

### Environment Configuration

1. **Update .env for production**
   ```env
   DEBUG=False
   ALLOWED_HOSTS=your-domain.com,www.your-domain.com
   SECRET_KEY=your-production-secret-key
   
   # Database
   DB_NAME=pnci_tool_prod
   DB_USER=pnci_prod_user
   DB_PASSWORD=strong-production-password
   DB_HOST=your-db-host
   DB_PORT=3306
   
   # Redis
   CELERY_BROKER_URL=redis://your-redis-host:6379/0
   CELERY_RESULT_BACKEND=redis://your-redis-host:6379/0
   
   # Proxy (if using webshare.io)
   WEBSHARE_PROXY_ENABLED=True
   WEBSHARE_PROXY_USER=your-proxy-username
   WEBSHARE_PROXY_PASSWORD=your-proxy-password
   WEBSHARE_PROXY_ENDPOINT=proxy-endpoint:port
   ```

2. **Install production dependencies**
   ```bash
   pip install gunicorn
   ```

3. **Collect static files**
   ```bash
   python manage.py collectstatic --noinput
   ```

4. **Run database migrations**
   ```bash
   python manage.py migrate
   python manage.py setup_initial_data
   ```

### Web Server Configuration

#### Using Gunicorn + Nginx

1. **Create Gunicorn service file** (`/etc/systemd/system/pnci-tool.service`)
   ```ini
   [Unit]
   Description=PNCI Tool Django Application
   After=network.target
   
   [Service]
   User=www-data
   Group=www-data
   WorkingDirectory=/path/to/competitor-pricing
   Environment="PATH=/path/to/venv/bin"
   ExecStart=/path/to/venv/bin/gunicorn --workers 3 --bind unix:/path/to/competitor-pricing/pnci_tool.sock pnci_tool.wsgi:application
   Restart=always
   
   [Install]
   WantedBy=multi-user.target
   ```

2. **Create Celery service file** (`/etc/systemd/system/pnci-celery.service`)
   ```ini
   [Unit]
   Description=PNCI Tool Celery Worker
   After=network.target
   
   [Service]
   User=www-data
   Group=www-data
   WorkingDirectory=/path/to/competitor-pricing
   Environment="PATH=/path/to/venv/bin"
   ExecStart=/path/to/venv/bin/celery -A pnci_tool worker --loglevel=info
   Restart=always
   
   [Install]
   WantedBy=multi-user.target
   ```

3. **Nginx configuration** (`/etc/nginx/sites-available/pnci-tool`)
   ```nginx
   server {
       listen 80;
       server_name your-domain.com www.your-domain.com;
       
       location = /favicon.ico { access_log off; log_not_found off; }
       
       location /static/ {
           root /path/to/competitor-pricing;
       }
       
       location /media/ {
           root /path/to/competitor-pricing;
       }
       
       location / {
           include proxy_params;
           proxy_pass http://unix:/path/to/competitor-pricing/pnci_tool.sock;
       }
   }
   ```

4. **Enable and start services**
   ```bash
   sudo systemctl enable pnci-tool
   sudo systemctl enable pnci-celery
   sudo systemctl start pnci-tool
   sudo systemctl start pnci-celery
   sudo systemctl enable nginx
   sudo systemctl start nginx
   ```

### Security Considerations

1. **Database Security**
   - Use strong passwords
   - Limit database user permissions
   - Enable SSL connections if possible

2. **File Permissions**
   ```bash
   chmod 755 /path/to/competitor-pricing
   chmod 644 /path/to/competitor-pricing/.env
   chmod 755 /path/to/competitor-pricing/media
   chmod 755 /path/to/competitor-pricing/logs
   ```

3. **Firewall Configuration**
   ```bash
   # Allow HTTP and HTTPS
   sudo ufw allow 80
   sudo ufw allow 443
   
   # Allow SSH (if needed)
   sudo ufw allow 22
   
   # Enable firewall
   sudo ufw enable
   ```

### Monitoring and Maintenance

1. **Log Files**
   - Django logs: `logs/django.log`
   - Nginx logs: `/var/log/nginx/access.log`, `/var/log/nginx/error.log`
   - Systemd logs: `journalctl -u pnci-tool`, `journalctl -u pnci-celery`

2. **Health Checks**
   ```bash
   # Check service status
   sudo systemctl status pnci-tool
   sudo systemctl status pnci-celery
   sudo systemctl status nginx
   
   # Check application
   curl http://your-domain.com/
   ```

3. **Backup Strategy**
   - Database: Regular MySQL dumps
   - Files: Backup `media/` directory
   - Configuration: Backup `.env` and nginx configs

### Troubleshooting

1. **Common Issues**
   - **502 Bad Gateway**: Check Gunicorn service status
   - **Static files not loading**: Run `collectstatic` and check nginx config
   - **Celery tasks not running**: Check Redis connection and Celery service

2. **Debug Commands**
   ```bash
   # Test Django
   python manage.py check
   python manage.py test
   
   # Test Celery
   celery -A pnci_tool inspect ping
   
   # Test database connection
   python manage.py dbshell
   ```

## Docker Deployment (Alternative)

1. **Create Dockerfile**
   ```dockerfile
   FROM python:3.10-slim
   
   WORKDIR /app
   COPY requirements.txt .
   RUN pip install -r requirements.txt
   
   COPY . .
   
   EXPOSE 8000
   CMD ["gunicorn", "--bind", "0.0.0.0:8000", "pnci_tool.wsgi:application"]
   ```

2. **Create docker-compose.yml**
   ```yaml
   version: '3.8'
   
   services:
     web:
       build: .
       ports:
         - "8000:8000"
       environment:
         - DEBUG=False
       depends_on:
         - db
         - redis
     
     db:
       image: mysql:8.0
       environment:
         MYSQL_DATABASE: pnci_tool
         MYSQL_ROOT_PASSWORD: rootpassword
     
     redis:
       image: redis:alpine
     
     celery:
       build: .
       command: celery -A pnci_tool worker --loglevel=info
       depends_on:
         - db
         - redis
   ```

3. **Deploy with Docker**
   ```bash
   docker-compose up -d
   ```
