"""
URL configuration for pnci_tool project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from core.views import ServeWizardJSView, ServeFaviconView

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('core.urls')),
    # Static file serving (temporary fix for production)
    path('static/js/wizard.js', ServeWizardJSView.as_view(), name='serve_wizard_js'),
    path('favicon.ico', ServeFaviconView.as_view(), name='serve_favicon'),
]

# Serve static and media files (for production deployment)
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
# Comment out static file serving to use our custom views
# urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
