<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta name="referrer" content="same-origin">
    <title>PNCI Competitive Pricing Analysis</title>
    <!-- Use Tailwind CSS from CDN with production warning suppressed -->
    <script>
        // Suppress console warnings before loading Tailwind
        const originalWarn = console.warn;
        console.warn = function(...args) {
            const message = args.join(' ');
            if (message.includes('cdn.tailwindcss.com should not be used in production') ||
                message.includes('should not be used in production')) {
                return; // Suppress Tailwind production warnings
            }
            originalWarn.apply(console, args);
        };
    </script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Configure Tailwind after loading
        if (typeof tailwind !== 'undefined') {
            tailwind.config = {
                corePlugins: {
                    preflight: false,
                }
            }
        }
    </script>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .wizard-step {
            display: none;
        }
        .wizard-step.active {
            display: block;
        }
        .step-indicator {
            counter-reset: step;
        }
        .step-indicator li {
            counter-increment: step;
            position: relative;
        }
        .step-indicator li::before {
            content: counter(step);
            position: absolute;
            left: 50%;
            top: 0;
            transform: translateX(-50%);
            background: #6b7280;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            line-height: 1;
        }
        .step-indicator li.completed::before {
            background: #10b981;
        }
        .step-indicator li.active::before {
            background: #3b82f6;
        }
        .step-indicator li:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 15px;
            left: calc(50% + 15px);
            width: calc(100% - 30px);
            height: 2px;
            background: #e5e7eb;
            z-index: -1;
        }
        .step-indicator li.completed:not(:last-child)::after {
            background: #10b981;
        }
        .file-drop-area {
            border: 2px dashed #e5e7eb;
            border-radius: 8px;
            padding: 3rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .file-drop-area:hover,
        .file-drop-area.drag-over {
            border-color: #3b82f6;
            background-color: #f8fafc;
        }
        .retailer-card {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .retailer-card:hover {
            border-color: #3b82f6;
            background-color: #f8fafc;
        }
        .retailer-card.selected {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
        .crawl-item {
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .export-option {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .export-option:hover {
            border-color: #3b82f6;
            background-color: #f8fafc;
        }
        .mfg-code-option.focused {
            background-color: #dbeafe !important;
        }
        .mfg-code-option:hover {
            background-color: #f3f4f6;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto py-8 px-4">
        <div class="max-w-6xl mx-auto">
            <!-- Header -->
            <div class="bg-white shadow-sm rounded-lg mb-6">
                <div class="p-6">
                    <h1 class="text-3xl font-bold text-center text-gray-900">
                        <i class="fas fa-chart-line text-blue-600 mr-3"></i>
                        PNCI Competitive Pricing Analysis
                    </h1>
                </div>
            </div>

            <!-- Step Indicator with Settings Tab -->
            <div class="bg-white shadow-sm rounded-lg mb-6">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <ol class="flex justify-between step-indicator flex-grow list-none">
                            <li class="flex-1 text-center active" data-step="1">
                                <div class="pt-10">
                                    <strong class="block">Select Brand</strong>
                                    <small class="text-gray-500">Choose manufacturer</small>
                                </div>
                            </li>
                            <li class="flex-1 text-center" data-step="2">
                                <div class="pt-10">
                                    <strong class="block">Import Excel</strong>
                                    <small class="text-gray-500">Upload product data</small>
                                </div>
                            </li>
                            <li class="flex-1 text-center" data-step="3">
                                <div class="pt-10">
                                    <strong class="block">Map Columns</strong>
                                    <small class="text-gray-500">Validate data</small>
                                </div>
                            </li>
                            <li class="flex-1 text-center" data-step="4">
                                <div class="pt-10">
                                    <strong class="block">Review Data</strong>
                                    <small class="text-gray-500">Confirm import</small>
                                </div>
                            </li>
                            <li class="flex-1 text-center" data-step="5">
                                <div class="pt-10">
                                    <strong class="block">Find Interchanges</strong>
                                    <small class="text-gray-500">WHI lookup</small>
                                </div>
                            </li>
                            <li class="flex-1 text-center" data-step="6">
                                <div class="pt-10">
                                    <strong class="block">Select Retailers</strong>
                                    <small class="text-gray-500">Choose sources</small>
                                </div>
                            </li>
                            <li class="flex-1 text-center" data-step="7">
                                <div class="pt-10">
                                    <strong class="block">Run Crawls</strong>
                                    <small class="text-gray-500">Analyze pricing</small>
                                </div>
                            </li>
                            <li class="flex-1 text-center" data-step="8">
                                <div class="pt-10">
                                    <strong class="block">Export Results</strong>
                                    <small class="text-gray-500">Download data</small>
                                </div>
                            </li>
                        </ol>
                        <div class="ml-8">
                            <a href="/settings/" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg text-sm inline-block">
                                <i class="fas fa-cog mr-2"></i>
                                Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="bg-white shadow-sm rounded-lg">
                <div class="p-6">
                    <!-- Settings Panel -->
                    <div class="wizard-step" id="step-settings">
                        <h3 class="text-2xl font-bold mb-6">
                            <i class="fas fa-cog text-gray-600 mr-3"></i>
                            Settings
                        </h3>
                        <div class="max-w-4xl mx-auto">
                            <!-- Proxy Configuration Card -->
                            <div class="bg-gray-50 rounded-lg p-6 mb-6">
                                <h5 class="text-lg font-semibold mb-4">
                                    <i class="fas fa-shield-alt text-blue-600 mr-2"></i>
                                    Proxy Configuration
                                </h5>
                                
                                <!-- Enable Proxy Toggle -->
                                <div class="flex items-center justify-between mb-6">
                                    <div>
                                        <label class="font-medium">Enable Proxy</label>
                                        <p class="text-sm text-gray-500">Use proxy for web scraping</p>
                                    </div>
                                    <div>
                                        <label class="relative inline-flex items-center cursor-pointer">
                                            <input type="checkbox" id="enableProxy" class="sr-only peer">
                                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                        </label>
                                    </div>
                                </div>
                                
                                <hr class="mb-6">
                                
                                <!-- Webshare.io Configuration -->
                                <div id="proxyCredentials" class="hidden">
                                    <div class="grid grid-cols-1 gap-4">
                                        <div>
                                            <label for="webshareApiToken" class="block text-sm font-medium text-gray-700 mb-2">
                                                <i class="fas fa-key mr-2"></i>
                                                Webshare.io API Token
                                            </label>
                                            <input type="password" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" id="webshareApiToken" placeholder="Enter your webshare.io API token">
                                            <small class="text-gray-500 mt-1">Get your API token from <a href="https://www.webshare.io/dashboard" target="_blank" class="text-blue-600 hover:underline">webshare.io dashboard</a></small>
                                        </div>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <label for="proxyMode" class="block text-sm font-medium text-gray-700 mb-2">
                                                    <i class="fas fa-network-wired mr-2"></i>
                                                    Proxy Mode
                                                </label>
                                                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" id="proxyMode">
                                                    <option value="direct">Direct (Recommended)</option>
                                                    <option value="backbone">Backbone</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label for="countryCodes" class="block text-sm font-medium text-gray-700 mb-2">
                                                    <i class="fas fa-globe mr-2"></i>
                                                    Country Codes
                                                </label>
                                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" id="countryCodes" placeholder="US,CA,GB" value="US,CA,GB">
                                                <small class="text-gray-500 mt-1">Comma-separated country codes</small>
                                            </div>
                                        </div>
                                        <div class="flex gap-4">
                                            <button type="button" class="bg-blue-100 hover:bg-blue-200 text-blue-700 px-4 py-2 rounded-lg text-sm" id="testProxyBtn">
                                                <i class="fas fa-vial mr-2"></i>
                                                Test Connection
                                            </button>
                                            <button type="button" class="bg-green-100 hover:bg-green-200 text-green-700 px-4 py-2 rounded-lg text-sm" id="saveProxyBtn">
                                                <i class="fas fa-save mr-2"></i>
                                                Save Settings
                                            </button>
                                        </div>
                                        <div id="proxyTestResult" class="mt-2"></div>
                                    </div>
                                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
                                        <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                                        <small class="text-blue-800">Webshare.io proxies will be used for all crawling operations to avoid IP blocking and rate limiting.</small>
                                    </div>
                                </div>
                                
                                <div id="proxyDisabledInfo" class="">
                                    <div class="bg-gray-100 border border-gray-200 rounded-lg p-4">
                                        <i class="fas fa-info-circle text-gray-600 mr-2"></i>
                                        <small class="text-gray-700">Direct connection will be used for crawling operations.</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Database Configuration Card -->
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h5 class="text-lg font-semibold mb-4">
                                    <i class="fas fa-database text-green-600 mr-2"></i>
                                    MySQL Database Configuration
                                </h5>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="mysqlServer" class="block text-sm font-medium text-gray-700 mb-2">
                                            <i class="fas fa-server mr-2"></i>
                                            Server Location
                                        </label>
                                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" id="mysqlServer" placeholder="localhost or IP address" value="localhost">
                                    </div>
                                    <div>
                                        <label for="mysqlDatabase" class="block text-sm font-medium text-gray-700 mb-2">
                                            <i class="fas fa-database mr-2"></i>
                                            Database Name
                                        </label>
                                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" id="mysqlDatabase" placeholder="Enter database name">
                                    </div>
                                    <div>
                                        <label for="mysqlUser" class="block text-sm font-medium text-gray-700 mb-2">
                                            <i class="fas fa-user-cog mr-2"></i>
                                            Username
                                        </label>
                                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" id="mysqlUser" placeholder="Database username">
                                    </div>
                                    <div>
                                        <label for="mysqlPassword" class="block text-sm font-medium text-gray-700 mb-2">
                                            <i class="fas fa-key mr-2"></i>
                                            Password
                                        </label>
                                        <input type="password" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" id="mysqlPassword" placeholder="Database password">
                                    </div>
                                </div>
                                
                                <div class="mt-4">
                                    <button class="bg-green-100 hover:bg-green-200 text-green-700 px-4 py-2 rounded-lg text-sm" id="testConnectionBtn">
                                        <i class="fas fa-plug mr-2"></i>
                                        Test Connection
                                    </button>
                                    <div id="connectionStatus" class="mt-2"></div>
                                </div>
                                
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
                                    <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                                    <small class="text-blue-800">Database connection will be used to store and retrieve pricing analysis results.</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 1: Select Manufacturer Code -->
                    <div class="wizard-step active" id="step-1">
                        <h3 class="text-2xl font-bold mb-6">
                            <i class="fas fa-industry text-blue-600 mr-3"></i>
                            Step 1: Select WHI Manufacturer Code
                        </h3>
                        <div class="max-w-2xl mx-auto">
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                                <div class="flex items-start">
                                    <i class="fas fa-info-circle text-blue-600 text-xl mr-3 mt-1"></i>
                                    <div>
                                        <h5 class="font-semibold text-blue-800 mb-2">Why select a manufacturer code first?</h5>
                                        <p class="text-blue-700 text-sm">
                                            The WHI manufacturer code helps us find interchange parts and ensures accurate pricing analysis.
                                            Choose the code that best matches the brand of parts in your Excel file.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Continue Where You Left Off Button -->
                            <div class="mb-6">
                                <div class="text-center">
                                    <button class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg text-lg font-semibold" id="continueWhereLeftOffBtn" style="display: none;">
                                        <i class="fas fa-play mr-2"></i>
                                        Continue Where You Left Off
                                    </button>
                                    <p class="text-gray-600 mt-2 text-sm" id="continueWhereLeftOffText" style="display: none;">
                                        Found existing uploads ready for interchange lookup
                                    </p>
                                </div>
                            </div>

                            <div class="bg-white border border-gray-200 rounded-lg p-6">
                                <h5 class="text-lg font-semibold mb-4">
                                    <i class="fas fa-search text-gray-600 mr-2"></i>
                                    Search for Your Brand
                                </h5>
                                <div class="relative">
                                    <label for="mfgCodeSearch" class="block text-sm font-medium text-gray-700 mb-2">
                                        Type the brand name to search
                                    </label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-search text-gray-400"></i>
                                        </div>
                                        <input type="text"
                                               class="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-lg"
                                               id="mfgCodeSearch"
                                               placeholder="Type brand name (e.g., CHAMPION, BOSCH, FRAM...)"
                                               autocomplete="off">
                                    </div>

                                    <!-- Dropdown Results -->
                                    <div id="mfgCodeDropdown" class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg hidden max-h-60 overflow-y-auto">
                                        <div id="mfgCodeResults" class="py-1">
                                            <!-- Results will be populated here -->
                                        </div>
                                        <div id="mfgCodeLoading" class="px-4 py-2 text-gray-500 text-center hidden">
                                            <i class="fas fa-spinner fa-spin mr-2"></i>Searching...
                                        </div>
                                        <div id="mfgCodeNoResults" class="px-4 py-2 text-gray-500 text-center hidden">
                                            No brands found matching your search
                                        </div>
                                    </div>

                                    <!-- Hidden input to store selected value -->
                                    <input type="hidden" id="selectedMfgCodeValue" value="">
                                </div>

                                <!-- Selected Code Display -->
                                <div id="selectedMfgCodeDisplay" class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg hidden">
                                    <div class="flex items-center">
                                        <i class="fas fa-check-circle text-green-600 text-xl mr-3"></i>
                                        <div>
                                            <h6 class="font-semibold text-green-800">Selected Manufacturer:</h6>
                                            <p class="text-green-700" id="selectedMfgCode">None selected</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Import Excel File -->
                    <div class="wizard-step" id="step-2">
                        <h3 class="text-2xl font-bold mb-6">
                            <i class="fas fa-file-excel text-green-600 mr-3"></i>
                            Step 2: Import Excel File
                        </h3>
                        <div class="max-w-2xl mx-auto">
                            <div class="file-drop-area" id="fileDropArea">
                                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                                <h5 class="text-xl font-semibold mb-2">Drop your Excel file here</h5>
                                <p class="text-gray-500 mb-4">or click to browse</p>
                                <input type="file" id="fileInput" class="hidden" accept=".xlsx,.xls">
                                <small class="text-gray-400">Supported formats: .xlsx, .xls</small>
                            </div>
                            <div id="fileInfo" class="mt-4 hidden">
                                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                    <i class="fas fa-check-circle text-green-600 mr-2"></i>
                                    <strong id="fileName"></strong> uploaded successfully
                                    <br>
                                    <small id="fileDetails" class="text-green-700"></small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Map Columns -->
                    <div class="wizard-step" id="step-3">
                        <h3 class="text-2xl font-bold mb-6">
                            <i class="fas fa-table text-purple-600 mr-3"></i>
                            Step 3: Map Columns
                        </h3>
                        <div class="max-w-4xl mx-auto">
                            <div class="bg-purple-50 border border-purple-200 rounded-lg p-6 mb-6">
                                <div class="flex items-start">
                                    <i class="fas fa-info-circle text-purple-600 text-xl mr-3 mt-1"></i>
                                    <div>
                                        <h5 class="font-semibold text-purple-800 mb-2">Column Mapping Required</h5>
                                        <p class="text-purple-700 text-sm">
                                            Map your Excel columns to our database fields to ensure accurate data import.
                                            This step validates your data before permanent storage.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center">
                                <button class="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 rounded-lg text-lg font-semibold" id="openMappingBtn">
                                    <i class="fas fa-external-link-alt mr-2"></i>
                                    Open Column Mapping Interface
                                </button>
                                <p class="text-gray-600 mt-3">This will open the mapping interface in a new window</p>
                            </div>

                            <!-- Mapping Status -->
                            <div id="mappingStatus" class="mt-6 hidden">
                                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-check-circle text-green-600 text-xl mr-3"></i>
                                        <div>
                                            <h6 class="font-semibold text-green-800">Mapping Completed</h6>
                                            <p class="text-green-700 text-sm" id="mappingStatusText">Data successfully mapped and validated</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 4: Review Data -->
                    <div class="wizard-step" id="step-4">
                        <h3 class="text-2xl font-bold mb-6">
                            <i class="fas fa-eye text-indigo-600 mr-3"></i>
                            Step 4: Review Imported Data
                        </h3>
                        <div class="max-w-6xl mx-auto">
                            <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-6 mb-6">
                                <div class="flex items-start">
                                    <i class="fas fa-info-circle text-indigo-600 text-xl mr-3 mt-1"></i>
                                    <div>
                                        <h5 class="font-semibold text-indigo-800 mb-2">Review Your Data</h5>
                                        <p class="text-indigo-700 text-sm">
                                            Review the imported and mapped data below. This is what will be used for interchange lookup and pricing analysis.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Data Summary -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                                <div class="bg-white border border-gray-200 rounded-lg p-4 text-center">
                                    <div class="text-2xl font-bold text-blue-600" id="totalRowsCount">0</div>
                                    <div class="text-sm text-gray-600">Total Rows</div>
                                </div>
                                <div class="bg-white border border-gray-200 rounded-lg p-4 text-center">
                                    <div class="text-2xl font-bold text-green-600" id="validRowsCount">0</div>
                                    <div class="text-sm text-gray-600">Valid Rows</div>
                                </div>
                                <div class="bg-white border border-gray-200 rounded-lg p-4 text-center">
                                    <div class="text-2xl font-bold text-red-600" id="invalidRowsCount">0</div>
                                    <div class="text-sm text-gray-600">Invalid Rows</div>
                                </div>
                            </div>

                            <!-- Data Preview Table -->
                            <div class="bg-white border border-gray-200 rounded-lg">
                                <div class="px-6 py-4 border-b border-gray-200">
                                    <h5 class="text-lg font-semibold text-gray-900">Data Preview (First 10 Rows)</h5>
                                </div>
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200" id="dataPreviewTable">
                                        <thead class="bg-gray-50">
                                            <!-- Headers will be populated here -->
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <!-- Data rows will be populated here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="mt-6 text-center">
                                <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-3 rounded-lg text-lg font-semibold" id="confirmDataBtn">
                                    <i class="fas fa-check mr-2"></i>
                                    Confirm Data & Continue
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Step 5: Find Interchanges -->
                    <div class="wizard-step" id="step-5">
                        <h3 class="text-2xl font-bold mb-6">
                            <i class="fas fa-search text-orange-600 mr-3"></i>
                            Step 5: Find Interchanges
                        </h3>
                        <div class="max-w-4xl mx-auto">
                            <div class="bg-orange-50 border border-orange-200 rounded-lg p-6 mb-6">
                                <div class="flex items-start">
                                    <i class="fas fa-info-circle text-orange-600 text-xl mr-3 mt-1"></i>
                                    <div>
                                        <h5 class="font-semibold text-orange-800 mb-2">WHI Interchange Lookup</h5>
                                        <p class="text-orange-700 text-sm">
                                            We'll search the WHI database for interchange parts using your selected manufacturer code.
                                            This expands your part list to include compatible alternatives.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div id="interchangeStatus">
                                <div class="text-center">
                                    <button class="bg-orange-600 hover:bg-orange-700 text-white px-8 py-3 rounded-lg text-lg font-semibold" id="startInterchangeBtn">
                                        <i class="fas fa-play mr-2"></i>
                                        Start Interchange Lookup
                                    </button>
                                    <p class="text-gray-600 mt-3">This process may take a few minutes depending on your data size</p>
                                </div>
                            </div>

                            <!-- Interchange Progress -->
                            <div id="interchangeProgress" class="hidden">
                                <div class="bg-white border border-gray-200 rounded-lg p-6">
                                    <h6 class="text-lg font-semibold mb-4">Interchange Lookup Progress</h6>
                                    <div class="w-full bg-gray-200 rounded-full h-4 mb-4">
                                        <div class="bg-orange-600 h-4 rounded-full transition-all duration-300" id="interchangeProgressBar" style="width: 0%"></div>
                                    </div>
                                    <div class="text-center">
                                        <span id="interchangeProgressText" class="text-gray-600">0% Complete</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Interchange Results -->
                            <div id="interchangeResults" class="hidden">
                                <div class="bg-white border border-gray-200 rounded-lg">
                                    <div class="px-6 py-4 border-b border-gray-200">
                                        <h5 class="text-lg font-semibold text-gray-900">Interchange Summary by Manufacturer</h5>
                                    </div>
                                    <div class="p-6">
                                        <div id="interchangeSummaryTable">
                                            <!-- Summary table will be populated here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 6: Select Retailers -->
                    <div class="wizard-step" id="step-6">
                        <h3 class="text-2xl font-bold mb-6">
                            <i class="fas fa-store text-blue-600 mr-3"></i>
                            Step 6: Select Retailers to Scrape
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {% for retailer in retailers %}
                            <div class="retailer-card" data-retailer="{{ retailer.name|lower|cut:' '|cut:"'" }}">
                                <div class="flex items-center">
                                    <div class="mr-4">
                                        <input class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500" type="checkbox" id="retailer{{ retailer.id }}">
                                    </div>
                                    <div class="flex-grow">
                                        <h5 class="text-lg font-semibold">{{ retailer.name }}</h5>
                                        <small class="text-gray-500">{{ retailer.website }}</small>
                                    </div>
                                    <i class="fas fa-car text-red-500 text-2xl"></i>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6">
                            <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                            Select at least one retailer to proceed with the analysis.
                        </div>
                    </div>

                    <!-- Step 7: Run Crawls -->
                    <div class="wizard-step" id="step-7">
                        <h3 class="text-2xl font-bold mb-6">
                            <i class="fas fa-spider text-gray-800 mr-3"></i>
                            Step 7: Run Crawls
                        </h3>
                        <div id="crawlStatus">
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                                <i class="fas fa-play-circle text-blue-600 mr-2"></i>
                                Ready to start crawling selected retailers...
                            </div>
                            <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg text-lg font-semibold" id="startCrawlBtn">
                                <i class="fas fa-play mr-2"></i>
                                Start Crawling
                            </button>
                        </div>
                        <div id="crawlProgress" class="hidden">
                            <div id="retailerProgress"></div>
                            <div class="mt-6">
                                <h6 class="text-lg font-semibold mb-2">Overall Progress</h6>
                                <div class="w-full bg-gray-200 rounded-full h-4">
                                    <div class="bg-green-600 h-4 rounded-full transition-all duration-300" id="overallProgress" style="width: 0%"></div>
                                </div>
                                <div class="text-center mt-2">
                                    <span id="progressText" class="text-gray-600">0% Complete</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 8: Export Results -->
                    <div class="wizard-step" id="step-8">
                        <h3 class="text-2xl font-bold mb-6">
                            <i class="fas fa-download text-green-600 mr-3"></i>
                            Step 8: Export Results
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="export-option" data-format="excel">
                                <div class="text-center">
                                    <i class="fas fa-file-excel text-green-600 text-4xl mb-4"></i>
                                    <h5 class="text-xl font-semibold mb-2">Excel Spreadsheet</h5>
                                    <p class="text-gray-600 mb-4">Complete analysis with formulas and charts</p>
                                    <button class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg">
                                        <i class="fas fa-download mr-2"></i>
                                        Download Excel
                                    </button>
                                </div>
                            </div>
                            <div class="export-option" data-format="csv">
                                <div class="text-center">
                                    <i class="fas fa-file-csv text-blue-600 text-4xl mb-4"></i>
                                    <h5 class="text-xl font-semibold mb-2">CSV File</h5>
                                    <p class="text-gray-600 mb-4">Raw data for further processing</p>
                                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
                                        <i class="fas fa-download mr-2"></i>
                                        Download CSV
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mt-6">
                            <i class="fas fa-check-circle text-green-600 mr-2"></i>
                            Analysis complete! Your competitive pricing data is ready for export.
                        </div>
                        <div class="mt-6">
                            <h6 class="text-lg font-semibold mb-4">Analysis Summary</h6>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                                <div class="bg-gray-50 border rounded-lg p-4">
                                    <h4 class="text-2xl font-bold text-blue-600 mb-1" id="totalProducts">0</h4>
                                    <small class="text-gray-600">Products Analyzed</small>
                                </div>
                                <div class="bg-gray-50 border rounded-lg p-4">
                                    <h4 class="text-2xl font-bold text-green-600 mb-1" id="retailersCount">0</h4>
                                    <small class="text-gray-600">Retailers Scraped</small>
                                </div>
                                <div class="bg-gray-50 border rounded-lg p-4">
                                    <h4 class="text-2xl font-bold text-yellow-600 mb-1" id="pricePoints">0</h4>
                                    <small class="text-gray-600">Price Points Found</small>
                                </div>
                                <div class="bg-gray-50 border rounded-lg p-4">
                                    <h4 class="text-2xl font-bold text-indigo-600 mb-1" id="successRate">0%</h4>
                                    <small class="text-gray-600">Success Rate</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="flex justify-between mt-8" id="mainNavigation">
                        <button class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg hidden" id="prevBtn">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Previous
                        </button>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg ml-auto" id="nextBtn">
                            Next
                            <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                    
                    <!-- Settings Navigation -->
                    <div class="flex justify-center mt-8 hidden" id="settingsNavigation">
                        <button class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg" id="backToWizardBtn">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to Wizard
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% load static %}
    {% csrf_token %}
    <script>
        // Make CSRF token available to JavaScript
        window.csrfToken = '{{ csrf_token }}';

        // Force cache refresh - version timestamp
        window.appVersion = Date.now();
        console.log('PNCI Pricing Tool loaded - Version:', window.appVersion);
    </script>
    <script>
        // Embedded wizard.js to avoid static file serving issues
        // Global variables
        let currentStep = 1;
        let totalSteps = 6;
        let selectedMfgCode = null;
        let uploadedFileId = null;
        let crawlJobId = null;

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('PNCI Pricing Tool - Wizard initialized');
            initializeManufacturerSearch();
            initializeWizard();
        });

        /**
         * Initialize manufacturer search functionality
         */
        function initializeManufacturerSearch() {
            const searchInput = document.getElementById('mfgCodeSearch');
            const dropdown = document.getElementById('mfgCodeDropdown');
            const results = document.getElementById('mfgCodeResults');
            const loading = document.getElementById('mfgCodeLoading');
            const noResults = document.getElementById('mfgCodeNoResults');
            const selectedDisplay = document.getElementById('selectedMfgCodeDisplay');
            const selectedValue = document.getElementById('selectedMfgCodeValue');
            const selectedText = document.getElementById('selectedMfgCode');

            if (!searchInput) return;

            let searchTimeout;
            let currentFocus = -1;

            // Search input event
            searchInput.addEventListener('input', function() {
                const query = this.value.trim();

                if (query.length < 2) {
                    hideDropdown();
                    return;
                }

                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    performSearch(query);
                }, 300);
            });

            // Keyboard navigation
            searchInput.addEventListener('keydown', function(e) {
                const items = dropdown.querySelectorAll('.mfg-code-option');

                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    currentFocus++;
                    if (currentFocus >= items.length) currentFocus = 0;
                    setActive(items);
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    currentFocus--;
                    if (currentFocus < 0) currentFocus = items.length - 1;
                    setActive(items);
                } else if (e.key === 'Enter') {
                    e.preventDefault();
                    if (currentFocus > -1 && items[currentFocus]) {
                        selectOption(items[currentFocus]);
                    }
                } else if (e.key === 'Escape') {
                    hideDropdown();
                }
            });

            // Click outside to close
            document.addEventListener('click', function(e) {
                if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
                    hideDropdown();
                }
            });

            function performSearch(query) {
                showLoading();

                fetch(`/api/manufacturer-codes/?search=${encodeURIComponent(query)}&limit=20`)
                    .then(response => response.json())
                    .then(data => {
                        hideLoading();
                        displayResults(data.codes || []);
                    })
                    .catch(error => {
                        hideLoading();
                        console.error('Search error:', error);
                        showNoResults();
                    });
            }

            function displayResults(codes) {
                results.innerHTML = '';
                currentFocus = -1;

                if (codes.length === 0) {
                    showNoResults();
                    return;
                }

                codes.forEach(code => {
                    const item = document.createElement('div');
                    item.className = 'mfg-code-option px-4 py-2 cursor-pointer hover:bg-gray-100';
                    item.innerHTML = `
                        <div class="font-medium">${code.brand}</div>
                        <div class="text-sm text-gray-500">${code.mfgcode}</div>
                    `;
                    item.dataset.mfgcode = code.mfgcode;
                    item.dataset.brand = code.brand;

                    item.addEventListener('click', () => selectOption(item));
                    results.appendChild(item);
                });

                showDropdown();
            }

            function selectOption(item) {
                const mfgcode = item.dataset.mfgcode;
                const brand = item.dataset.brand;

                selectedValue.value = mfgcode;
                selectedText.textContent = `${brand} (${mfgcode})`;
                searchInput.value = brand;
                selectedMfgCode = mfgcode;

                selectedDisplay.classList.remove('hidden');
                hideDropdown();

                console.log('Selected manufacturer:', { mfgcode, brand });
            }

            function setActive(items) {
                items.forEach((item, index) => {
                    item.classList.toggle('focused', index === currentFocus);
                });
            }

            function showDropdown() {
                dropdown.classList.remove('hidden');
                noResults.classList.add('hidden');
            }

            function hideDropdown() {
                dropdown.classList.add('hidden');
                loading.classList.add('hidden');
                noResults.classList.add('hidden');
            }

            function showLoading() {
                results.innerHTML = '';
                loading.classList.remove('hidden');
                showDropdown();
            }

            function hideLoading() {
                loading.classList.add('hidden');
            }

            function showNoResults() {
                results.innerHTML = '';
                noResults.classList.remove('hidden');
                showDropdown();
            }
        }

        /**
         * Initialize wizard functionality
         */
        function initializeWizard() {
            const nextBtn = document.getElementById('nextBtn');
            const prevBtn = document.getElementById('prevBtn');

            if (nextBtn) {
                nextBtn.addEventListener('click', nextStep);
            }

            if (prevBtn) {
                prevBtn.addEventListener('click', prevStep);
            }
        }

        /**
         * Move to next step
         */
        function nextStep() {
            if (validateCurrentStep()) {
                if (currentStep < totalSteps) {
                    currentStep++;
                    showStep(currentStep);
                    updateStepIndicators();
                    updateNavigationButtons();
                }
            }
        }

        /**
         * Move to previous step
         */
        function prevStep() {
            if (currentStep > 1) {
                currentStep--;
                showStep(currentStep);
                updateStepIndicators();
                updateNavigationButtons();
            }
        }

        /**
         * Show specific step
         */
        function showStep(step) {
            const steps = document.querySelectorAll('.wizard-step');
            steps.forEach(s => s.classList.remove('active'));

            const currentStepEl = document.getElementById(`step-${step}`);
            if (currentStepEl) {
                currentStepEl.classList.add('active');
            }
        }

        /**
         * Update step indicators
         */
        function updateStepIndicators() {
            const indicators = document.querySelectorAll('.step-indicator li');
            indicators.forEach((indicator, index) => {
                const stepNum = index + 1;
                indicator.classList.remove('active', 'completed');

                if (stepNum === currentStep) {
                    indicator.classList.add('active');
                } else if (stepNum < currentStep) {
                    indicator.classList.add('completed');
                }
            });
        }

        /**
         * Update navigation buttons
         */
        function updateNavigationButtons() {
            const nextBtn = document.getElementById('nextBtn');
            const prevBtn = document.getElementById('prevBtn');

            if (prevBtn) {
                prevBtn.classList.toggle('hidden', currentStep === 1);
            }

            if (nextBtn) {
                if (currentStep === totalSteps) {
                    nextBtn.innerHTML = '<i class="fas fa-check mr-2"></i>Finish';
                } else {
                    nextBtn.innerHTML = 'Next <i class="fas fa-arrow-right ml-2"></i>';
                }
            }
        }

        /**
         * Validate current step
         */
        function validateCurrentStep() {
            switch (currentStep) {
                case 1:
                    return validateManufacturerSelection();
                default:
                    return true;
            }
        }

        /**
         * Validate manufacturer selection
         */
        function validateManufacturerSelection() {
            if (!selectedMfgCode) {
                showError('Please select a manufacturer code before proceeding.');
                return false;
            }
            return true;
        }

        // Basic utility functions
        function showError(message) {
            alert('Error: ' + message);
            console.error('Error:', message);
        }

        function showSuccess(message) {
            console.log('Success:', message);
        }

        function showLoading(message = 'Loading...') {
            console.log('Loading:', message);
        }

        function hideLoading() {
            console.log('Loading complete');
        }
    </script>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
