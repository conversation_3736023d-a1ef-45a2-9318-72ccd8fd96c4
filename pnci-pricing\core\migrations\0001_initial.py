# Generated by Django 4.2.7 on 2025-06-03 12:10

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ManufacturerCode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('mfg_code', models.Char<PERSON>ield(db_index=True, max_length=10, unique=True)),
                ('brand_name', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'manufacturer_codes',
                'ordering': ['mfg_code'],
            },
        ),
        migrations.CreateModel(
            name='ProxyConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('is_enabled', models.<PERSON>oleanField(default=False)),
                ('proxy_type', models.CharField(default='http', max_length=20)),
                ('endpoint', models.CharField(max_length=255)),
                ('username', models.CharField(blank=True, max_length=100)),
                ('password', models.CharField(blank=True, max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'proxy_configurations',
            },
        ),
        migrations.CreateModel(
            name='Retailer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('website', models.URLField()),
                ('base_search_url', models.URLField()),
                ('is_active', models.BooleanField(default=True)),
                ('crawl_delay', models.PositiveIntegerField(default=1)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'retailers',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='UploadedFile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('filename', models.CharField(max_length=255)),
                ('file_path', models.FileField(upload_to='uploads/')),
                ('file_size', models.PositiveIntegerField()),
                ('status', models.CharField(choices=[('uploaded', 'Uploaded'), ('processing', 'Processing'), ('processed', 'Processed'), ('error', 'Error')], default='uploaded', max_length=20)),
                ('total_parts', models.PositiveIntegerField(default=0)),
                ('processed_parts', models.PositiveIntegerField(default=0)),
                ('error_message', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('selected_mfg_code', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.manufacturercode')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'uploaded_files',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PartNumber',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('part_number', models.CharField(db_index=True, max_length=100)),
                ('manufacturer', models.CharField(blank=True, max_length=100)),
                ('description', models.TextField(blank=True)),
                ('row_data', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('uploaded_file', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='part_numbers', to='core.uploadedfile')),
            ],
            options={
                'db_table': 'part_numbers',
            },
        ),
        migrations.CreateModel(
            name='InterchangeData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('interchange_part', models.CharField(db_index=True, max_length=100)),
                ('interchange_brand', models.CharField(blank=True, max_length=100)),
                ('smartpage_url', models.URLField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('part_number', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interchanges', to='core.partnumber')),
            ],
            options={
                'db_table': 'interchange_data',
            },
        ),
        migrations.CreateModel(
            name='CrawlJob',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('running', 'Running'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('total_parts', models.PositiveIntegerField(default=0)),
                ('processed_parts', models.PositiveIntegerField(default=0)),
                ('successful_crawls', models.PositiveIntegerField(default=0)),
                ('failed_crawls', models.PositiveIntegerField(default=0)),
                ('use_proxy', models.BooleanField(default=False)),
                ('error_message', models.TextField(blank=True)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('retailers', models.ManyToManyField(to='core.retailer')),
                ('uploaded_file', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='crawl_jobs', to='core.uploadedfile')),
            ],
            options={
                'db_table': 'crawl_jobs',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PricingData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('searched_part', models.CharField(max_length=100)),
                ('found_part', models.CharField(blank=True, max_length=100)),
                ('price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('availability', models.CharField(blank=True, max_length=100)),
                ('product_url', models.URLField(blank=True)),
                ('product_name', models.CharField(blank=True, max_length=255)),
                ('brand', models.CharField(blank=True, max_length=100)),
                ('additional_data', models.JSONField(default=dict)),
                ('crawled_at', models.DateTimeField(auto_now_add=True)),
                ('crawl_job', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pricing_data', to='core.crawljob')),
                ('part_number', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pricing_data', to='core.partnumber')),
                ('retailer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.retailer')),
            ],
            options={
                'db_table': 'pricing_data',
                'indexes': [models.Index(fields=['part_number', 'retailer'], name='pricing_dat_part_nu_f39ef9_idx'), models.Index(fields=['crawl_job'], name='pricing_dat_crawl_j_9c625d_idx'), models.Index(fields=['price'], name='pricing_dat_price_7717b9_idx')],
                'unique_together': {('crawl_job', 'part_number', 'retailer', 'searched_part')},
            },
        ),
        migrations.AddIndex(
            model_name='partnumber',
            index=models.Index(fields=['part_number'], name='part_number_part_nu_f5f090_idx'),
        ),
        migrations.AddIndex(
            model_name='partnumber',
            index=models.Index(fields=['uploaded_file', 'part_number'], name='part_number_uploade_5be12e_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='partnumber',
            unique_together={('uploaded_file', 'part_number')},
        ),
        migrations.AddIndex(
            model_name='interchangedata',
            index=models.Index(fields=['interchange_part'], name='interchange_interch_b52812_idx'),
        ),
        migrations.AddIndex(
            model_name='interchangedata',
            index=models.Index(fields=['part_number'], name='interchange_part_nu_04d401_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='interchangedata',
            unique_together={('part_number', 'interchange_part')},
        ),
    ]
