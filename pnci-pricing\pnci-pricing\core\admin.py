from django.contrib import admin
from .models import (
    ManufacturerCode, UploadedFile, PartNumber, InterchangeData, ProductData,
    Retailer, CrawlJob, PricingData, ProxyConfiguration, CrawlLog
)


@admin.register(ManufacturerCode)
class ManufacturerCodeAdmin(admin.ModelAdmin):
    list_display = ['mfgcode', 'brand', 'created_at']
    search_fields = ['mfgcode', 'brand']
    list_filter = ['created_at']
    ordering = ['mfgcode']


@admin.register(UploadedFile)
class UploadedFileAdmin(admin.ModelAdmin):
    list_display = ['filename', 'status', 'total_parts', 'processed_parts', 'created_at']
    list_filter = ['status', 'created_at']
    search_fields = ['filename']
    readonly_fields = ['id', 'created_at', 'updated_at']


@admin.register(PartNumber)
class PartNumberAdmin(admin.ModelAdmin):
    list_display = ['part_number', 'manufacturer', 'uploaded_file', 'created_at']
    list_filter = ['manufacturer', 'created_at']
    search_fields = ['part_number', 'manufacturer', 'description']
    raw_id_fields = ['uploaded_file']


@admin.register(ProductData)
class ProductDataAdmin(admin.ModelAdmin):
    list_display = ['PartNumber', 'PartTypeID', 'AAIABrandLabel', 'CompanyName', 'created_at']
    list_filter = ['PartTypeID', 'AAIABrandLabel', 'created_at']
    search_fields = ['PartNumber', 'AAIABrandLabel', 'CompanyName', 'PartType']
    raw_id_fields = ['uploaded_file']


@admin.register(InterchangeData)
class InterchangeDataAdmin(admin.ModelAdmin):
    list_display = ['product_data', 'interchange_type', 'interchange_part_number', 'interchange_brand_name', 'created_at']
    list_filter = ['interchange_type', 'created_at']
    search_fields = ['interchange_part_number', 'interchange_brand_name', 'whi_brand_name']
    raw_id_fields = ['product_data']


@admin.register(Retailer)
class RetailerAdmin(admin.ModelAdmin):
    list_display = ['name', 'website', 'is_active', 'crawl_delay', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'website']


@admin.register(CrawlJob)
class CrawlJobAdmin(admin.ModelAdmin):
    list_display = ['id', 'uploaded_file', 'status', 'progress_percentage', 'created_at']
    list_filter = ['status', 'use_proxy', 'created_at']
    readonly_fields = ['id', 'progress_percentage', 'created_at']
    raw_id_fields = ['uploaded_file']
    filter_horizontal = ['retailers']


@admin.register(PricingData)
class PricingDataAdmin(admin.ModelAdmin):
    list_display = ['part_number', 'retailer', 'searched_part', 'price', 'availability', 'crawled_at']
    list_filter = ['retailer', 'availability', 'crawled_at']
    search_fields = ['searched_part', 'found_part', 'product_name']
    raw_id_fields = ['crawl_job', 'part_number', 'retailer']


@admin.register(ProxyConfiguration)
class ProxyConfigurationAdmin(admin.ModelAdmin):
    list_display = ['name', 'is_enabled', 'proxy_type', 'endpoint', 'updated_at']
    list_filter = ['is_enabled', 'proxy_type']
    search_fields = ['name', 'endpoint']


@admin.register(CrawlLog)
class CrawlLogAdmin(admin.ModelAdmin):
    list_display = ['started_at', 'url_short', 'method', 'status_code', 'proxy_info', 'response_time_ms', 'crawl_type', 'part_number']
    list_filter = ['proxy_used', 'proxy_provider', 'status_code', 'crawl_type', 'started_at']
    search_fields = ['url', 'part_number', 'manufacturer_code', 'proxy_ip']
    readonly_fields = ['started_at', 'completed_at', 'response_time_ms']
    ordering = ['-started_at']

    def url_short(self, obj):
        """Display shortened URL for better readability"""
        if len(obj.url) > 60:
            return obj.url[:57] + '...'
        return obj.url
    url_short.short_description = 'URL'

    def proxy_info(self, obj):
        """Display proxy information"""
        if obj.proxy_used and obj.proxy_ip:
            return f"{obj.proxy_ip}:{obj.proxy_port}"
        return "Direct"
    proxy_info.short_description = 'Proxy'

    # Add custom actions
    actions = ['mark_as_retry']

    def mark_as_retry(self, request, queryset):
        """Mark selected logs for retry"""
        count = queryset.update(retry_count=0)
        self.message_user(request, f'{count} logs marked for retry.')
    mark_as_retry.short_description = "Mark selected logs for retry"
