<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PNCI Competitive Pricing Analysis</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .wizard-step {
            display: none;
        }
        .wizard-step.active {
            display: block;
        }
        .step-indicator {
            counter-reset: step;
        }
        .step-indicator li {
            counter-increment: step;
            position: relative;
        }
        .step-indicator li::before {
            content: counter(step);
            position: absolute;
            left: 50%;
            top: 0;
            transform: translateX(-50%);
            background: #6c757d;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            line-height: 1;
        }
        .step-indicator li.completed::before {
            background: #198754;
        }
        .step-indicator li.active::before {
            background: #0d6efd;
        }
        .step-indicator li:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 15px;
            left: calc(50% + 15px);
            width: calc(100% - 30px);
            height: 2px;
            background: #dee2e6;
            z-index: -1;
        }
        .step-indicator li.completed:not(:last-child)::after {
            background: #198754;
        }
        .file-drop-area {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 3rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .file-drop-area:hover,
        .file-drop-area.drag-over {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }
        .retailer-card {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .retailer-card:hover {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }
        .retailer-card.selected {
            border-color: #0d6efd;
            background-color: #e7f3ff;
        }
        .crawl-item {
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .export-option {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .export-option:hover {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- Header -->
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <h1 class="card-title text-center mb-0">
                            <i class="fas fa-chart-line text-primary me-2"></i>
                            PNCI Competitive Pricing Analysis
                        </h1>
                    </div>
                </div>

                <!-- Step Indicator with Settings Tab -->
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <ol class="list-unstyled d-flex justify-content-between step-indicator mb-0 flex-grow-1">
                                <li class="flex-fill text-center active" data-step="1">
                                    <div class="pt-4">
                                        <strong>Import Excel</strong>
                                        <small class="d-block text-muted">Upload product data</small>
                                    </div>
                                </li>
                                <li class="flex-fill text-center" data-step="2">
                                    <div class="pt-4">
                                        <strong>Select Retailers</strong>
                                        <small class="d-block text-muted">Choose sources</small>
                                    </div>
                                </li>
                                <li class="flex-fill text-center" data-step="3">
                                    <div class="pt-4">
                                        <strong>Run Crawls</strong>
                                        <small class="d-block text-muted">Analyze pricing</small>
                                    </div>
                                </li>
                                <li class="flex-fill text-center" data-step="4">
                                    <div class="pt-4">
                                        <strong>Export Results</strong>
                                        <small class="d-block text-muted">Download data</small>
                                    </div>
                                </li>
                            </ol>
                            <div class="ms-4">
                                <button class="btn btn-outline-secondary btn-sm" id="settingsBtn">
                                    <i class="fas fa-cog me-2"></i>
                                    Settings
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="card shadow-sm">
                    <div class="card-body">
                        <!-- Settings Panel -->
                        <div class="wizard-step" id="step-settings">
                            <h3 class="mb-4">
                                <i class="fas fa-cog text-secondary me-2"></i>
                                Settings
                            </h3>
                            <div class="row">
                                <div class="col-md-10 mx-auto">
                                    <!-- Proxy Configuration Card -->
                                    <div class="card mb-4">
                                        <div class="card-body">
                                            <h5 class="card-title mb-4">
                                                <i class="fas fa-shield-alt text-primary me-2"></i>
                                                Proxy Configuration
                                            </h5>
                                            
                                            <!-- Enable Proxy Toggle -->
                                            <div class="row align-items-center mb-4">
                                                <div class="col-md-6">
                                                    <label class="form-label mb-0">
                                                        <strong>Enable Proxy</strong>
                                                        <small class="d-block text-muted">Use proxy for web scraping</small>
                                                    </label>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" id="enableProxy" style="transform: scale(1.5);">
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <hr>
                                            
                                            <!-- Proxy Credentials -->
                                            <div id="proxyCredentials" class="d-none">
                                                <div class="row g-3">
                                                    <div class="col-md-6">
                                                        <label for="proxyUserId" class="form-label">
                                                            <i class="fas fa-user me-2"></i>
                                                            User ID
                                                        </label>
                                                        <input type="text" class="form-control" id="proxyUserId" placeholder="Enter proxy username">
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label for="proxyPassword" class="form-label">
                                                            <i class="fas fa-lock me-2"></i>
                                                            Password
                                                        </label>
                                                        <input type="password" class="form-control" id="proxyPassword" placeholder="Enter proxy password">
                                                    </div>
                                                </div>
                                                <div class="alert alert-info mt-3">
                                                    <i class="fas fa-info-circle me-2"></i>
                                                    <small>Proxy settings will be applied to all retailer crawling operations.</small>
                                                </div>
                                            </div>
                                            
                                            <div id="proxyDisabledInfo" class="">
                                                <div class="alert alert-secondary">
                                                    <i class="fas fa-info-circle me-2"></i>
                                                    <small>Direct connection will be used for crawling operations.</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Database Configuration Card -->
                                    <div class="card">
                                        <div class="card-body">
                                            <h5 class="card-title mb-4">
                                                <i class="fas fa-database text-success me-2"></i>
                                                MySQL Database Configuration
                                            </h5>
                                            
                                            <div class="row g-3">
                                                <div class="col-md-6">
                                                    <label for="mysqlServer" class="form-label">
                                                        <i class="fas fa-server me-2"></i>
                                                        Server Location
                                                    </label>
                                                    <input type="text" class="form-control" id="mysqlServer" placeholder="localhost or IP address" value="localhost">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="mysqlDatabase" class="form-label">
                                                        <i class="fas fa-database me-2"></i>
                                                        Database Name
                                                    </label>
                                                    <input type="text" class="form-control" id="mysqlDatabase" placeholder="Enter database name">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="mysqlUser" class="form-label">
                                                        <i class="fas fa-user-cog me-2"></i>
                                                        Username
                                                    </label>
                                                    <input type="text" class="form-control" id="mysqlUser" placeholder="Database username">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="mysqlPassword" class="form-label">
                                                        <i class="fas fa-key me-2"></i>
                                                        Password
                                                    </label>
                                                    <input type="password" class="form-control" id="mysqlPassword" placeholder="Database password">
                                                </div>
                                            </div>
                                            
                                            <div class="row mt-3">
                                                <div class="col-md-12">
                                                    <button class="btn btn-outline-success btn-sm" id="testConnectionBtn">
                                                        <i class="fas fa-plug me-2"></i>
                                                        Test Connection
                                                    </button>
                                                    <div id="connectionStatus" class="mt-2"></div>
                                                </div>
                                            </div>
                                            
                                            <div class="alert alert-info mt-3">
                                                <i class="fas fa-info-circle me-2"></i>
                                                <small>Database connection will be used to store and retrieve pricing analysis results.</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 1: Import Excel File -->
                        <div class="wizard-step active" id="step-1">
                            <h3 class="mb-4">
                                <i class="fas fa-file-excel text-success me-2"></i>
                                Step 1: Import Excel File
                            </h3>
                            <div class="row">
                                <div class="col-md-8 mx-auto">
                                    <div class="file-drop-area" id="fileDropArea">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                        <h5>Drop your Excel file here</h5>
                                        <p class="text-muted">or click to browse</p>
                                        <input type="file" id="fileInput" class="d-none" accept=".xlsx,.xls">
                                        <small class="text-muted">Supported formats: .xlsx, .xls</small>
                                    </div>
                                    <div id="fileInfo" class="mt-3 d-none">
                                        <div class="alert alert-success">
                                            <i class="fas fa-check-circle me-2"></i>
                                            <strong id="fileName"></strong> uploaded successfully
                                            <br>
                                            <small id="fileDetails"></small>
                                        </div>
                                    </div>
									<div id="mfgCodeSelection" class="mt-4">
                                        <div class="card">
                                            <div class="card-body">
                                                <h5 class="card-title">
                                                    <i class="fas fa-tag text-warning me-2"></i>
                                                    Select WHI Manufacturer Code
                                                </h5>
                                                <p class="text-muted mb-3">Choose the manufacturer code that matches your product data:</p>
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <select class="form-select" id="mfgCodeSelect" size="1">
                                                            <option value="">-- Select Manufacturer Code --</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="input-group">
                                                            <span class="input-group-text">
                                                                <i class="fas fa-search"></i>
                                                            </span>
                                                            <input type="text" class="form-control" id="mfgCodeSearch" placeholder="Search codes...">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="mt-3">
                                                    <small class="text-muted">
                                                        <i class="fas fa-info-circle me-1"></i>
                                                        Selected code: <span id="selectedMfgCode" class="fw-bold text-primary">None</span>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>									
									</div>											
                                </div>
                            </div>
                        </div>

                        <!-- Step 2: Choose Retailers -->
                        <div class="wizard-step" id="step-2">
                            <h3 class="mb-4">
                                <i class="fas fa-store text-primary me-2"></i>
                                Step 2: Select Retailers to Scrape
                            </h3>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="retailer-card" data-retailer="advance-auto">
                                        <div class="d-flex align-items-center">
                                            <div class="form-check me-3">
                                                <input class="form-check-input" type="checkbox" id="advanceAuto">
                                            </div>
                                            <div class="flex-grow-1">
                                                <h5 class="mb-1">Advance Auto Parts</h5>
                                                <small class="text-muted">advanceautoparts.com</small>
                                            </div>
                                            <i class="fas fa-car text-danger fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="retailer-card" data-retailer="autozone">
                                        <div class="d-flex align-items-center">
                                            <div class="form-check me-3">
                                                <input class="form-check-input" type="checkbox" id="autozone">
                                            </div>
                                            <div class="flex-grow-1">
                                                <h5 class="mb-1">AutoZone</h5>
                                                <small class="text-muted">autozone.com</small>
                                            </div>
                                            <i class="fas fa-wrench text-warning fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="retailer-card" data-retailer="napa">
                                        <div class="d-flex align-items-center">
                                            <div class="form-check me-3">
                                                <input class="form-check-input" type="checkbox" id="napa">
                                            </div>
                                            <div class="flex-grow-1">
                                                <h5 class="mb-1">NAPA Auto Parts</h5>
                                                <small class="text-muted">napaonline.com</small>
                                            </div>
                                            <i class="fas fa-cog text-primary fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="retailer-card" data-retailer="oreilly">
                                        <div class="d-flex align-items-center">
                                            <div class="form-check me-3">
                                                <input class="form-check-input" type="checkbox" id="oreilly">
                                            </div>
                                            <div class="flex-grow-1">
                                                <h5 class="mb-1">O'Reilly Auto Parts</h5>
                                                <small class="text-muted">oreillyauto.com</small>
                                            </div>
                                            <i class="fas fa-tools text-success fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-info mt-4">
                                <i class="fas fa-info-circle me-2"></i>
                                Select at least one retailer to proceed with the analysis.
                            </div>
                        </div>

                        <!-- Step 3: Run Crawls -->
                        <div class="wizard-step" id="step-3">
                            <h3 class="mb-4">
                                <i class="fas fa-spider text-dark me-2"></i>
                                Step 3: Run Crawls
                            </h3>
                            <div id="crawlStatus">
                                <div class="alert alert-primary">
                                    <i class="fas fa-play-circle me-2"></i>
                                    Ready to start crawling selected retailers...
                                </div>
                                <button class="btn btn-primary btn-lg" id="startCrawlBtn">
                                    <i class="fas fa-play me-2"></i>
                                    Start Crawling
                                </button>
                            </div>
                            <div id="crawlProgress" class="d-none">
                                <div class="crawl-item">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">Advance Auto Parts</h6>
                                        <span class="badge bg-primary" id="advanceStatus">Waiting...</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" id="advanceProgress" style="width: 0%"></div>
                                    </div>
                                </div>
                                <div class="crawl-item">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">AutoZone</h6>
                                        <span class="badge bg-primary" id="autozoneStatus">Waiting...</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" id="autozoneProgress" style="width: 0%"></div>
                                    </div>
                                </div>
                                <div class="crawl-item">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">NAPA Auto Parts</h6>
                                        <span class="badge bg-primary" id="napaStatus">Waiting...</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" id="napaProgress" style="width: 0%"></div>
                                    </div>
                                </div>
                                <div class="crawl-item">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">O'Reilly Auto Parts</h6>
                                        <span class="badge bg-primary" id="oreillyStatus">Waiting...</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" id="oreillyProgress" style="width: 0%"></div>
                                    </div>
                                </div>
                                <div class="mt-4">
                                    <h6>Overall Progress</h6>
                                    <div class="progress progress-lg">
                                        <div class="progress-bar bg-success" id="overallProgress" style="width: 0%"></div>
                                    </div>
                                    <div class="text-center mt-2">
                                        <span id="progressText">0% Complete</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 4: Export Results -->
                        <div class="wizard-step" id="step-4">
                            <h3 class="mb-4">
                                <i class="fas fa-download text-success me-2"></i>
                                Step 4: Export Results
                            </h3>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="export-option" data-format="excel">
                                        <div class="text-center">
                                            <i class="fas fa-file-excel text-success fa-3x mb-3"></i>
                                            <h5>Excel Spreadsheet</h5>
                                            <p class="text-muted">Complete analysis with formulas and charts</p>
                                            <button class="btn btn-success">
                                                <i class="fas fa-download me-2"></i>
                                                Download Excel
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="export-option" data-format="csv">
                                        <div class="text-center">
                                            <i class="fas fa-file-csv text-primary fa-3x mb-3"></i>
                                            <h5>CSV File</h5>
                                            <p class="text-muted">Raw data for further processing</p>
                                            <button class="btn btn-primary">
                                                <i class="fas fa-download me-2"></i>
                                                Download CSV
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-success mt-4">
                                <i class="fas fa-check-circle me-2"></i>
                                Analysis complete! Your competitive pricing data is ready for export.
                            </div>
                            <div class="mt-4">
                                <h6>Analysis Summary</h6>
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <div class="border rounded p-3">
                                            <h4 class="text-primary mb-1">247</h4>
                                            <small class="text-muted">Products Analyzed</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="border rounded p-3">
                                            <h4 class="text-success mb-1">4</h4>
                                            <small class="text-muted">Retailers Scraped</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="border rounded p-3">
                                            <h4 class="text-warning mb-1">892</h4>
                                            <small class="text-muted">Price Points Found</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="border rounded p-3">
                                            <h4 class="text-info mb-1">98.5%</h4>
                                            <small class="text-muted">Success Rate</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Navigation Buttons -->
                        <div class="d-flex justify-content-between mt-4" id="mainNavigation">
                            <button class="btn btn-outline-secondary" id="prevBtn" style="display: none;">
                                <i class="fas fa-arrow-left me-2"></i>
                                Previous
                            </button>
                            <button class="btn btn-primary ms-auto" id="nextBtn">
                                Next
                                <i class="fas fa-arrow-right ms-2"></i>
                            </button>
                        </div>
                        
                        <!-- Settings Navigation -->
                        <div class="d-flex justify-content-center mt-4 d-none" id="settingsNavigation">
                            <button class="btn btn-secondary" id="backToWizardBtn">
                                <i class="fas fa-arrow-left me-2"></i>
                                Back to Wizard
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentStep = 1;
        let fileUploaded = false;
        let selectedRetailers = [];
        let crawlComplete = false;
        let inSettingsMode = false;

        // Store manufacturer codes data
        let manufacturerCodes = [];
        
        // Embedded CSV data for manufacturer codes
        const manufacturerCodesCSV = `mfgcode,brand
"1FR","1 FACTORY RADIO"
"EH1","1-800-RADIATOR/CARB APPROVED"
"EHR","1800RADIATOR OEQ"
"EHJ","1800RADIATOR OEQ A/C SERVICE KIT"
"EHB","1800RADIATOR OEQ RADIATOR & HOSE KIT"
"EHS","1800RADIATOR PREMIUM"
"EHF","1800RADIATOR PREMIUM 48 STATE"
"EHA","1800RADIATOR PREMIUM A/C SERVICE KIT"
"EHD","1800RADIATOR PREMIUM RADIATOR & HOSE KIT"
"3DC","3D CARBON"
"914","914RUBBER"
"2EA","A2A EXHAUST ACCESSORIES"
"AB4","AB CATALYTIC"
"A3V","ABEX BRAKE"
"ABF","ABS PRIVATE BRAND"
"ABS","ABSOLUTE"
"ABT","ABT AUTO TRANS"
"ACC","AC COMPRESSOR"
"ACL","AC COMPRESSOR LINES"
"ACD","ACCELERATOR CABLE"
"ACE","ACE STEERING"
"ACT","ACURA/HONDA TIMING BELT"
"ACV","AC VALVE"
"AD1","ADVANTAGE 1"
"ADC","ADC CATALYTIC"
"ADP","ADAPTER"
"ADS","ADS SHOCKS"
"ADV","ADVANTAGE"
"AER","AEROQUIP"
"AES","AES SHOCKS"
"AFE","AFE POWER"
"AFP","AFP PRIVATE BRAND"
"AGC","AGC GLASS"
"AHF","AH FABCO"
"AIC","AIC A/C"
"AIF","AIC FILTER"
"AIL","AIL LIGHTING"
"AIM","AIM IMPORT"
"AIN","AIN IMPORT"
"AIR","AIR FILTER"
"AIS","AIS IMPORT"
"AIT","AIT IMPORT"
"AJB","AJB BRAKE"
"AJU","AJU AUTO JAPAN"
"AKG","AKG GASKET"
"ALA","ALARM"
"ALC","ALCOHOL"
"ALD","ALDOR"
"ALF","ALF FILTER"
"ALG","ALG GASKET"
"ALI","ALIGNMENT"
"ALK","ALK GASKET"
"ALL","ALLISON"
"ALM","ALM FILTER"
"ALN","ALN FILTER"
"ALP","ALP FILTER"
"ALR","ALR FILTER"
"ALS","ALS FILTER"
"ALT","ALTERNATOR"
"ALU","ALUMINUM"
"ALV","ALV FILTER"
"ALW","ALW FILTER"
"ALX","ALX FILTER"
"ALY","ALY FILTER"
"ALZ","ALZ FILTER"
"AMC","AMC"
"AMD","AMD FILTER"
"AME","AME FILTER"
"AMF","AMF FILTER"
"AMG","AMG FILTER"
"AMH","AMH FILTER"
"AMI","AMI FILTER"
"AMJ","AMJ FILTER"
"AMK","AMK FILTER"