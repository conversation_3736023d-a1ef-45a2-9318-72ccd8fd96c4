import requests
from bs4 import BeautifulSoup
import time
import re
import logging
from urllib.parse import urljoin, quote
from decimal import Decimal, InvalidOperation
from .webshare_client import get_proxy_rotator

logger = logging.getLogger(__name__)


class BaseScraper:
    """Base scraper class with common functionality"""

    def __init__(self, use_proxy=False):
        self.use_proxy = use_proxy
        self.session = requests.Session()
        self.proxy_rotator = None
        self.setup_session()

    def setup_session(self):
        """Setup session with headers and proxy"""
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

        if self.use_proxy:
            self.proxy_rotator = get_proxy_rotator()
            if self.proxy_rotator:
                if not self.proxy_rotator.initialize_proxy_pool():
                    logger.warning("Failed to initialize proxy pool, falling back to direct connection")
                    self.use_proxy = False
                else:
                    logger.info(f"Initialized proxy pool with {self.proxy_rotator.get_proxy_info()['total_proxies']} proxies")

    def get_proxy_for_request(self):
        """Get a proxy for the current request"""
        if not self.use_proxy or not self.proxy_rotator:
            return None

        proxy = self.proxy_rotator.get_next_proxy()
        if proxy:
            logger.debug(f"Using proxy: {proxy['http'].split('@')[1]}")
        return proxy
    
    def get_page(self, url, timeout=30, max_retries=3):
        """Get page content with error handling and proxy rotation"""
        last_exception = None

        for attempt in range(max_retries):
            try:
                # Get proxy for this request
                proxy = self.get_proxy_for_request()

                # Make request with or without proxy
                response = self.session.get(
                    url,
                    timeout=timeout,
                    proxies=proxy,
                    allow_redirects=True
                )
                response.raise_for_status()

                logger.debug(f"Successfully fetched {url} (attempt {attempt + 1})")
                return response

            except requests.exceptions.ProxyError as e:
                logger.warning(f"Proxy error on attempt {attempt + 1} for {url}: {str(e)}")
                last_exception = e
                # Continue to next attempt with different proxy

            except requests.exceptions.Timeout as e:
                logger.warning(f"Timeout on attempt {attempt + 1} for {url}: {str(e)}")
                last_exception = e
                time.sleep(1)  # Brief delay before retry

            except requests.exceptions.RequestException as e:
                logger.warning(f"Request error on attempt {attempt + 1} for {url}: {str(e)}")
                last_exception = e
                time.sleep(1)  # Brief delay before retry

            except Exception as e:
                logger.error(f"Unexpected error on attempt {attempt + 1} for {url}: {str(e)}")
                last_exception = e
                break  # Don't retry on unexpected errors

        logger.error(f"Failed to fetch {url} after {max_retries} attempts. Last error: {str(last_exception)}")
        return None
    
    def extract_price(self, price_text):
        """Extract price from text"""
        if not price_text:
            return None
        
        # Remove currency symbols and extract numbers
        price_match = re.search(r'[\d,]+\.?\d*', price_text.replace(',', ''))
        if price_match:
            try:
                return Decimal(price_match.group())
            except InvalidOperation:
                return None
        return None


class WHISmartpageScraper(BaseScraper):
    """Scraper for WHI smartpages to get interchange data"""

    BASE_URL = "https://smartpages.nexpart.com/smartpage.php"

    def get_interchanges(self, mfg_code, part_number):
        """Get interchange part numbers from WHI smartpage"""
        try:
            url = f"{self.BASE_URL}?mfrlinecode={mfg_code}&partnumber={quote(part_number)}&acesptermid=1684"
            logger.info(f"Fetching interchanges for {mfg_code}-{part_number} from WHI smartpages")

            response = self.get_page(url)

            if not response:
                logger.warning(f"Failed to fetch WHI smartpage for {mfg_code}-{part_number}")
                return []

            soup = BeautifulSoup(response.content, 'html.parser')
            interchanges = []

            # Look for interchange tables or sections
            # This is a placeholder - actual implementation would depend on WHI page structure
            interchange_sections = soup.find_all('div', class_='interchange') or soup.find_all('table')

            for section in interchange_sections:
                # Extract part numbers and brands
                part_links = section.find_all('a') or section.find_all('td')

                for link in part_links:
                    text = link.get_text(strip=True)
                    if text and len(text) > 3:  # Basic validation
                        # Try to extract part number pattern
                        part_match = re.search(r'[A-Z0-9\-\.]{4,}', text)
                        if part_match:
                            interchanges.append({
                                'part_number': part_match.group(),
                                'brand': '',  # Extract brand if available
                                'url': url
                            })

            # Remove duplicates
            seen = set()
            unique_interchanges = []
            for item in interchanges:
                if item['part_number'] not in seen:
                    seen.add(item['part_number'])
                    unique_interchanges.append(item)

            logger.info(f"Found {len(unique_interchanges)} interchanges for {mfg_code}-{part_number}")
            return unique_interchanges[:50]  # Limit to 50 interchanges

        except Exception as e:
            logger.error(f"Error getting interchanges for {mfg_code}-{part_number}: {str(e)}")
            return []


class AdvanceAutoScraper(BaseScraper):
    """Scraper for Advance Auto Parts"""
    
    BASE_URL = "https://www.advanceautoparts.com"
    SEARCH_URL = "https://www.advanceautoparts.com/web/SearchResults"
    
    def search_part(self, part_number):
        """Search for part on Advance Auto Parts"""
        try:
            params = {
                'searchTerm': part_number,
                'isSearchByPartNumber': 'true'
            }
            
            response = self.session.get(self.SEARCH_URL, params=params)
            if not response:
                return None
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for product results
            product = soup.find('div', class_='product-tile') or soup.find('div', class_='search-result')
            
            if product:
                # Extract price
                price_elem = product.find('span', class_='price') or product.find('div', class_='price')
                price = self.extract_price(price_elem.get_text() if price_elem else '')
                
                # Extract product name
                name_elem = product.find('h3') or product.find('a', class_='product-name')
                name = name_elem.get_text(strip=True) if name_elem else ''
                
                # Extract availability
                availability_elem = product.find('span', class_='availability')
                availability = availability_elem.get_text(strip=True) if availability_elem else ''
                
                # Extract product URL
                link_elem = product.find('a')
                product_url = urljoin(self.BASE_URL, link_elem.get('href')) if link_elem else ''
                
                return {
                    'found_part': part_number,
                    'price': price,
                    'name': name,
                    'availability': availability,
                    'url': product_url,
                    'brand': 'Advance Auto Parts'
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error searching Advance Auto for {part_number}: {str(e)}")
            return None


class AutoZoneScraper(BaseScraper):
    """Scraper for AutoZone"""
    
    BASE_URL = "https://www.autozone.com"
    SEARCH_URL = "https://www.autozone.com/search"
    
    def search_part(self, part_number):
        """Search for part on AutoZone"""
        try:
            params = {'searchText': part_number}
            
            response = self.session.get(self.SEARCH_URL, params=params)
            if not response:
                return None
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for product results
            product = soup.find('div', class_='product-item') or soup.find('div', class_='search-product')
            
            if product:
                # Extract price
                price_elem = product.find('span', class_='price-current')
                price = self.extract_price(price_elem.get_text() if price_elem else '')
                
                # Extract product name
                name_elem = product.find('h4') or product.find('a', class_='product-title')
                name = name_elem.get_text(strip=True) if name_elem else ''
                
                # Extract availability
                availability = 'In Stock'  # Default assumption
                
                # Extract product URL
                link_elem = product.find('a')
                product_url = urljoin(self.BASE_URL, link_elem.get('href')) if link_elem else ''
                
                return {
                    'found_part': part_number,
                    'price': price,
                    'name': name,
                    'availability': availability,
                    'url': product_url,
                    'brand': 'AutoZone'
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error searching AutoZone for {part_number}: {str(e)}")
            return None


class NAPAScraper(BaseScraper):
    """Scraper for NAPA Auto Parts"""
    
    BASE_URL = "https://www.napaonline.com"
    SEARCH_URL = "https://www.napaonline.com/search"
    
    def search_part(self, part_number):
        """Search for part on NAPA"""
        try:
            params = {'query': part_number}
            
            response = self.session.get(self.SEARCH_URL, params=params)
            if not response:
                return None
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for product results
            product = soup.find('div', class_='product-card') or soup.find('div', class_='search-item')
            
            if product:
                # Extract price
                price_elem = product.find('span', class_='price')
                price = self.extract_price(price_elem.get_text() if price_elem else '')
                
                # Extract product name
                name_elem = product.find('h3') or product.find('div', class_='product-name')
                name = name_elem.get_text(strip=True) if name_elem else ''
                
                # Extract availability
                availability = 'Available'  # Default assumption
                
                # Extract product URL
                link_elem = product.find('a')
                product_url = urljoin(self.BASE_URL, link_elem.get('href')) if link_elem else ''
                
                return {
                    'found_part': part_number,
                    'price': price,
                    'name': name,
                    'availability': availability,
                    'url': product_url,
                    'brand': 'NAPA'
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error searching NAPA for {part_number}: {str(e)}")
            return None


class OReillyScraper(BaseScraper):
    """Scraper for O'Reilly Auto Parts"""
    
    BASE_URL = "https://www.oreillyauto.com"
    SEARCH_URL = "https://www.oreillyauto.com/search"
    
    def search_part(self, part_number):
        """Search for part on O'Reilly"""
        try:
            params = {'q': part_number}
            
            response = self.session.get(self.SEARCH_URL, params=params)
            if not response:
                return None
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for product results
            product = soup.find('div', class_='product-tile') or soup.find('div', class_='search-result-item')
            
            if product:
                # Extract price
                price_elem = product.find('span', class_='price-display')
                price = self.extract_price(price_elem.get_text() if price_elem else '')
                
                # Extract product name
                name_elem = product.find('h4') or product.find('span', class_='product-name')
                name = name_elem.get_text(strip=True) if name_elem else ''
                
                # Extract availability
                availability = 'In Stock'  # Default assumption
                
                # Extract product URL
                link_elem = product.find('a')
                product_url = urljoin(self.BASE_URL, link_elem.get('href')) if link_elem else ''
                
                return {
                    'found_part': part_number,
                    'price': price,
                    'name': name,
                    'availability': availability,
                    'url': product_url,
                    'brand': "O'Reilly"
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error searching O'Reilly for {part_number}: {str(e)}")
            return None


class RetailerScraperFactory:
    """Factory to create retailer scrapers"""

    SCRAPERS = {
        'Advance Auto Parts': AdvanceAutoScraper,
        'AutoZone': AutoZoneScraper,
        'NAPA Auto Parts': NAPAScraper,
        "O'Reilly Auto Parts": OReillyScraper,
    }

    @classmethod
    def create_scraper(cls, retailer_name, use_proxy=False):
        """Create scraper instance for retailer"""
        scraper_class = cls.SCRAPERS.get(retailer_name)
        if scraper_class:
            return scraper_class(use_proxy=use_proxy)
        else:
            raise ValueError(f"No scraper available for retailer: {retailer_name}")
