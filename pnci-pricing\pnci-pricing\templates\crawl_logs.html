<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crawl Logs Dashboard - PNCI Pricing Tool</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 20px;
            background: #f8f9fa;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .filters {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .filter-group {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 10px;
        }
        .filter-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .filter-group select, .filter-group input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .logs-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .logs-table th,
        .logs-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        .logs-table th {
            background: #f8f9fa;
            font-weight: 600;
            position: sticky;
            top: 0;
        }
        .logs-table tbody tr:hover {
            background: #f8f9fa;
        }
        .status-success {
            color: #28a745;
            font-weight: bold;
        }
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        .proxy-badge {
            background: #17a2b8;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .direct-badge {
            background: #6c757d;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .url-cell {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .error-message {
            color: #dc3545;
            font-size: 12px;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕷️ Crawl Logs Dashboard</h1>
            <p>Real-time tracking of WHI SmartPages interchange crawling with proxy usage</p>
        </div>

        <div class="stats" id="stats">
            <div class="stat-card">
                <div class="stat-number" id="total-requests">-</div>
                <div class="stat-label">Total Requests</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successful-requests">-</div>
                <div class="stat-label">Successful</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="proxy-requests">-</div>
                <div class="stat-label">Via Proxy</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avg-response-time">-</div>
                <div class="stat-label">Avg Response (ms)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="unique-ips">-</div>
                <div class="stat-label">Unique Proxy IPs</div>
            </div>
        </div>

        <div class="filters">
            <div class="filter-group">
                <label>Time Range:</label>
                <select id="hours-filter">
                    <option value="1">Last Hour</option>
                    <option value="6">Last 6 Hours</option>
                    <option value="24" selected>Last 24 Hours</option>
                    <option value="168">Last Week</option>
                </select>
            </div>
            <div class="filter-group">
                <label>Crawl Type:</label>
                <select id="crawl-type-filter">
                    <option value="">All Types</option>
                    <option value="interchange_preview">Preview</option>
                    <option value="interchange_full">Full Lookup</option>
                    <option value="test_interchange">Test</option>
                </select>
            </div>
            <div class="filter-group">
                <label>Proxy Only:</label>
                <input type="checkbox" id="proxy-only-filter">
            </div>
            <div class="filter-group">
                <label>Limit:</label>
                <select id="limit-filter">
                    <option value="25">25</option>
                    <option value="50" selected>50</option>
                    <option value="100">100</option>
                    <option value="200">200</option>
                </select>
            </div>
            <button class="btn" onclick="loadLogs()">Refresh</button>
            <button class="btn" onclick="autoRefresh()">Auto Refresh</button>
        </div>

        <div id="logs-container">
            <div class="loading">Loading crawl logs...</div>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;

        function formatTimestamp(timestamp) {
            if (!timestamp) return '-';
            return new Date(timestamp).toLocaleString();
        }

        function formatUrl(url) {
            if (url.length > 60) {
                return url.substring(0, 57) + '...';
            }
            return url;
        }

        function formatResponseTime(ms) {
            if (!ms) return '-';
            return ms.toLocaleString() + 'ms';
        }

        function getStatusClass(statusCode) {
            if (!statusCode) return 'status-error';
            if (statusCode >= 200 && statusCode < 300) return 'status-success';
            return 'status-error';
        }

        function loadLogs() {
            const hours = document.getElementById('hours-filter').value;
            const crawlType = document.getElementById('crawl-type-filter').value;
            const proxyOnly = document.getElementById('proxy-only-filter').checked;
            const limit = document.getElementById('limit-filter').value;

            let url = `/api/crawl-logs/?hours=${hours}&limit=${limit}`;
            if (crawlType) url += `&crawl_type=${crawlType}`;
            if (proxyOnly) url += `&proxy_only=true`;

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    updateStats(data.stats);
                    updateLogsTable(data.logs);
                })
                .catch(error => {
                    console.error('Error loading logs:', error);
                    document.getElementById('logs-container').innerHTML = 
                        '<div class="loading">Error loading logs: ' + error.message + '</div>';
                });
        }

        function updateStats(stats) {
            document.getElementById('total-requests').textContent = stats.total_requests;
            document.getElementById('successful-requests').textContent = stats.successful_requests;
            document.getElementById('proxy-requests').textContent = stats.proxy_requests;
            document.getElementById('avg-response-time').textContent = Math.round(stats.avg_response_time);
            document.getElementById('unique-ips').textContent = stats.unique_ips.length;
        }

        function updateLogsTable(logs) {
            let html = `
                <table class="logs-table">
                    <thead>
                        <tr>
                            <th>Time</th>
                            <th>URL</th>
                            <th>Status</th>
                            <th>Response Time</th>
                            <th>Proxy</th>
                            <th>Part Number</th>
                            <th>Mfg Code</th>
                            <th>Type</th>
                            <th>Error</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            logs.forEach(log => {
                const proxyInfo = log.proxy_used ? 
                    `<span class="proxy-badge">${log.proxy_ip}:${log.proxy_port}</span>` :
                    `<span class="direct-badge">Direct</span>`;

                html += `
                    <tr>
                        <td>${formatTimestamp(log.started_at)}</td>
                        <td class="url-cell" title="${log.url}">${formatUrl(log.url)}</td>
                        <td class="${getStatusClass(log.status_code)}">${log.status_code || 'Error'}</td>
                        <td>${formatResponseTime(log.response_time_ms)}</td>
                        <td>${proxyInfo}</td>
                        <td>${log.part_number || '-'}</td>
                        <td>${log.manufacturer_code || '-'}</td>
                        <td>${log.crawl_type}</td>
                        <td class="error-message" title="${log.error_message || ''}">${log.error_message ? log.error_message.substring(0, 50) + '...' : '-'}</td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            document.getElementById('logs-container').innerHTML = html;
        }

        function autoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                document.querySelector('button[onclick="autoRefresh()"]').textContent = 'Auto Refresh';
            } else {
                autoRefreshInterval = setInterval(loadLogs, 5000); // Refresh every 5 seconds
                document.querySelector('button[onclick="autoRefresh()"]').textContent = 'Stop Auto Refresh';
                loadLogs(); // Load immediately
            }
        }

        // Load logs on page load
        document.addEventListener('DOMContentLoaded', loadLogs);
    </script>
</body>
</html>
