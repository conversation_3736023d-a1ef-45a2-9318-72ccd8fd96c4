# Generated by Django 4.2.7 on 2025-06-03 13:32

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_alter_manufacturercode_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DataMappingConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('source_columns', models.JSONField()),
                ('column_mappings', models.J<PERSON>NField(default=dict)),
                ('target_fields', models.J<PERSON>NField(default=dict)),
                ('validation_rules', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('uploaded_file', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='core.uploadedfile')),
            ],
        ),
        migrations.CreateModel(
            name='ImportedDataTemp',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('row_number', models.IntegerField()),
                ('raw_data', models.JSONField()),
                ('is_valid', models.BooleanField(default=False)),
                ('validation_errors', models.JSONField(blank=True, default=list)),
                ('is_mapped', models.BooleanField(default=False)),
                ('mapped_data', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('uploaded_file', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.uploadedfile')),
            ],
            options={
                'ordering': ['row_number'],
                'unique_together': {('uploaded_file', 'row_number')},
            },
        ),
    ]
