"""
Test step 4 data confirmation functionality
"""

import requests
import json

def test_step4_functionality():
    print("🔍 Testing Step 4 Data Confirmation functionality...")
    
    # Use the file ID from our previous test
    file_id = "154f0d79-28dc-4cfe-b62a-ab6daba7f742"
    
    print(f"📁 Testing with file ID: {file_id}")
    
    # Test the data mapping API that step 4 uses
    print("\n1. Testing data mapping API...")
    
    url = f'http://localhost:8000/api/data-mapping/{file_id}/'
    
    try:
        response = requests.get(url, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print("   ✅ API working successfully!")
            print(f"   📊 File info:")
            print(f"      - Filename: {data['file_info']['filename']}")
            print(f"      - Total rows: {data['file_info']['total_rows']}")
            print(f"      - Status: {data['file_info']['status']}")
            
            print(f"   📋 Source columns: {data['source_columns']}")
            print(f"   🎯 Target fields count: {len(data['target_fields'])}")
            print(f"   🔗 Current mappings: {data['current_mappings']}")
            print(f"   📄 Sample data rows: {len(data['sample_data'])}")
            
            if data['sample_data']:
                first_row = data['sample_data'][0]
                print(f"   📝 First row:")
                print(f"      - Row number: {first_row['row_number']}")
                print(f"      - Is valid: {first_row['is_valid']}")
                print(f"      - Raw data keys: {list(first_row['raw_data'].keys())}")
                if first_row['validation_errors']:
                    print(f"      - Validation errors: {first_row['validation_errors']}")
            
        else:
            print(f"   ❌ API failed with status {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test validation API
    print("\n2. Testing data validation API...")
    
    validation_url = f'http://localhost:8000/api/validate-data/{file_id}/'
    
    try:
        # Get CSRF token first
        session = requests.Session()
        main_response = session.get('http://localhost:8000/')
        
        csrf_token = None
        for line in main_response.text.split('\n'):
            if 'csrfToken' in line and '=' in line:
                start = line.find("'") + 1
                end = line.find("'", start)
                if start > 0 and end > start:
                    csrf_token = line[start:end]
                    break
        
        if csrf_token:
            headers = {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrf_token
            }
            
            response = session.post(validation_url, headers=headers, json={})
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                validation_data = response.json()
                print("   ✅ Validation API working!")
                print(f"   📊 Validation results:")
                print(f"      - Total rows: {validation_data.get('total_rows', 0)}")
                print(f"      - Valid rows: {validation_data.get('valid_rows', 0)}")
                print(f"      - Invalid rows: {validation_data.get('invalid_rows', 0)}")
                print(f"      - Errors: {len(validation_data.get('errors', []))}")
            else:
                print(f"   ⚠️ Validation API returned {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
        else:
            print("   ❌ Could not get CSRF token")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print("\n3. Testing step 4 workflow...")
    print("   📝 Expected workflow:")
    print("      1. User navigates to step 4")
    print("      2. Data preview loads automatically")
    print("      3. User sees data statistics and preview table")
    print("      4. User clicks 'Confirm Data & Continue'")
    print("      5. System proceeds to step 5 (Interchange Lookup)")
    
    print("\n✅ Step 4 functionality test completed!")
    print(f"🌐 Manual test URL: http://137.220.60.137:8000/")
    print("   Instructions:")
    print("   1. Select a manufacturer (e.g., AISIN)")
    print("   2. Upload the test Excel file")
    print("   3. Click 'Open Column Mapping Interface' and set up mappings")
    print("   4. Navigate to step 4 and test the 'Confirm Data & Continue' button")

if __name__ == '__main__':
    test_step4_functionality()
