"""
Test MySQL connection and check table structure
"""

import mysql.connector
from mysql.connector import <PERSON>rror

def test_connection():
    print("Testing MySQL connection...")
    
    # MySQL connection parameters
    mysql_config = {
        'host': 'mysql.sophio.com',
        'port': 3306,
        'database': 'whi_aces',
        'user': 'facetedapi',
        'password': 'dtutuc954',
    }
    
    try:
        # Connect to MySQL database
        print('Connecting to MySQL database...')
        mysql_conn = mysql.connector.connect(**mysql_config)
        mysql_cursor = mysql_conn.cursor()
        
        # Check table structure
        print('Checking manufacturers table structure...')
        mysql_cursor.execute("DESCRIBE manufacturers")
        columns = mysql_cursor.fetchall()
        
        print('Table structure:')
        for column in columns:
            print(f'  {column[0]} - {column[1]}')
        
        # Get sample data
        print('\nGetting sample data...')
        mysql_cursor.execute("SELECT * FROM manufacturers LIMIT 5")
        sample_data = mysql_cursor.fetchall()
        
        print('Sample data:')
        for row in sample_data:
            print(f'  {row}')
        
        # Get total count
        mysql_cursor.execute("SELECT COUNT(*) FROM manufacturers")
        total_count = mysql_cursor.fetchone()[0]
        print(f'\nTotal manufacturers: {total_count}')
        
        # Close connection
        mysql_cursor.close()
        mysql_conn.close()
        print('Connection closed successfully')
        
        return True
        
    except Error as e:
        print(f'MySQL Error: {e}')
        return False
        
    except Exception as e:
        print(f'Error: {e}')
        return False

if __name__ == '__main__':
    success = test_connection()
    if success:
        print("✅ MySQL connection test successful!")
    else:
        print("❌ MySQL connection test failed")
