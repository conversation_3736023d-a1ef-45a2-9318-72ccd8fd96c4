# Generated by Django 4.2.7 on 2025-06-03 22:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0007_rename_interchange_data_interchange_part_number_idx_interchange_interch_2632d6_idx_and_more'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='interchangedata',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='interchangedata',
            name='part_number',
            field=models.CharField(db_index=True, default='UNKNOWN', max_length=100),
            preserve_default=False,
        ),
        migrations.AlterUniqueTogether(
            name='interchangedata',
            unique_together={('part_number', 'whi_mfgcode', 'interchange_type', 'interchange_part_number', 'interchange_brand_name')},
        ),
        migrations.AddIndex(
            model_name='interchangedata',
            index=models.Index(fields=['part_number'], name='interchange_part_nu_2692ba_idx'),
        ),
        migrations.AddIndex(
            model_name='interchangedata',
            index=models.Index(fields=['part_number', 'whi_mfgcode'], name='interchange_part_nu_6133db_idx'),
        ),
    ]
