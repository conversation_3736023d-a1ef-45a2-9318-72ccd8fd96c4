Serving on http://0.0.0.0:8000
Not Found: /static/js/wizard.js
Not Found: /favicon.ico
Not Found: /static/js/wizard.js
Serving on http://0.0.0.0:8000
Not Found: /static/js/wizard.js
Not Found: /static/js/wizard.js
Watching for file changes with StatReloader
Serving on http://0.0.0.0:8000
Not Found: /static/js/wizard.js
Not Found: /static/js/wizard.js
Not Found: /static/js/wizard.js
Not Found: /favicon.ico
Not Found: /static/js/wizard.js
Not Found: /favicon.ico
Serving on http://0.0.0.0:8000
Not Found: /static/js/wizard.js
Serving on http://0.0.0.0:8000
Not Found: /static/js/wizard.js
Serving on http://0.0.0.0:8000
Not Found: /static/js/wizard.js
Not Found: /static/js/wizard.js
Serving on http://0.0.0.0:8000
Serving on http://0.0.0.0:8000
Internal Server Error: /
Traceback (most recent call last):
  File "C:\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Python310\lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Python310\lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
  File "C:\Python310\lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
  File "C:\Python310\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Python310\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Python310\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Python310\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Python310\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Python310\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Python310\lib\site-packages\django\template\base.py", line 1064, in render
    output = self.filter_expression.resolve(context)
  File "C:\Python310\lib\site-packages\django\template\base.py", line 742, in resolve
    new_obj = func(obj, *arg_vals)
  File "C:\Python310\lib\site-packages\django\template\defaultfilters.py", line 772, in date
    return formats.date_format(value, arg)
  File "C:\Python310\lib\site-packages\django\utils\formats.py", line 158, in date_format
    return dateformat.format(
  File "C:\Python310\lib\site-packages\django\utils\dateformat.py", line 324, in format
    return df.format(format_string)
  File "C:\Python310\lib\site-packages\django\utils\dateformat.py", line 48, in format
    pieces.append(str(getattr(self, piece)()))
  File "C:\Python310\lib\site-packages\django\utils\dateformat.py", line 297, in U
    value = datetime.combine(value, time.min)
TypeError: combine() argument 1 must be datetime.date, not SafeString
Serving on http://0.0.0.0:8000
Serving on http://0.0.0.0:8000
Serving on http://0.0.0.0:8000
