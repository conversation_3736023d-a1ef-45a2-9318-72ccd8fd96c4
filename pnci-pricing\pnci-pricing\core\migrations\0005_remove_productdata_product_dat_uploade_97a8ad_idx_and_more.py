# Generated by Django 4.2.7 on 2025-06-03 15:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0004_productdata'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='productdata',
            name='product_dat_uploade_97a8ad_idx',
        ),
        migrations.AlterUniqueTogether(
            name='productdata',
            unique_together={('PartNumber', 'PartTypeID', 'AAIABrandID', 'LineCode')},
        ),
        migrations.AddIndex(
            model_name='productdata',
            index=models.Index(fields=['AAIABrandID'], name='product_dat_AAIABra_44fd99_idx'),
        ),
        migrations.AddIndex(
            model_name='productdata',
            index=models.Index(fields=['LineCode'], name='product_dat_LineCod_c5e7b9_idx'),
        ),
        migrations.AddIndex(
            model_name='productdata',
            index=models.Index(fields=['PartNumber', 'PartTypeID', 'AAIABrandID', 'LineCode'], name='product_dat_PartNum_3f4793_idx'),
        ),
    ]
