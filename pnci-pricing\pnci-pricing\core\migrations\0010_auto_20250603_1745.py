# Generated by Django 4.2.7 on 2025-06-03 22:45

from django.db import migrations
from urllib.parse import urlparse, parse_qs


def extract_part_data_from_urls(apps, schema_editor):
    """Extract part_number and part_type_id from existing smartpage_url data"""
    InterchangeData = apps.get_model('core', 'InterchangeData')

    # Get all records that need updating
    records = InterchangeData.objects.filter(
        part_number='UNKNOWN',
        part_type_id='UNKNOWN'
    )

    updated_count = 0

    for record in records:
        if record.smartpage_url:
            try:
                # Parse the URL to extract query parameters
                # Example: https://smartpages.nexpart.com/smartpage.php?mfrlinecode=AIS&partnumber=TKH-002&acesptermid=16088
                parsed_url = urlparse(record.smartpage_url)
                query_params = parse_qs(parsed_url.query)

                # Extract partnumber and acesptermid
                part_number = query_params.get('partnumber', [None])[0]
                part_type_id = query_params.get('acesptermid', [None])[0]

                if part_number and part_type_id:
                    record.part_number = part_number
                    record.part_type_id = part_type_id
                    record.save()
                    updated_count += 1

            except Exception as e:
                # If URL parsing fails, leave as UNKNOWN
                print(f"Failed to parse URL {record.smartpage_url}: {e}")
                continue

    print(f"Updated {updated_count} InterchangeData records with extracted part_number and part_type_id")


def reverse_extract_part_data(apps, schema_editor):
    """Reverse migration - set back to UNKNOWN"""
    InterchangeData = apps.get_model('core', 'InterchangeData')
    InterchangeData.objects.all().update(
        part_number='UNKNOWN',
        part_type_id='UNKNOWN'
    )


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0009_remove_interchangedata_interchange_part_nu_6133db_idx_and_more'),
    ]

    operations = [
        migrations.RunPython(extract_part_data_from_urls, reverse_extract_part_data),
    ]
