from django.core.management.base import BaseCommand
from core.models import Retailer


class Command(BaseCommand):
    help = 'Create retailer records'

    def handle(self, *args, **options):
        self.stdout.write('Creating retailers...')
        
        retailers_data = [
            {
                'name': 'Advance Auto Parts',
                'website': 'https://www.advanceautoparts.com',
                'base_search_url': 'https://www.advanceautoparts.com/web/SearchResults',
                'crawl_delay': 2
            },
            {
                'name': 'AutoZone',
                'website': 'https://www.autozone.com',
                'base_search_url': 'https://www.autozone.com/search',
                'crawl_delay': 2
            },
            {
                'name': 'NAPA Auto Parts',
                'website': 'https://www.napaonline.com',
                'base_search_url': 'https://www.napaonline.com/search',
                'crawl_delay': 2
            },
            {
                'name': "O'Reilly Auto Parts",
                'website': 'https://www.oreillyauto.com',
                'base_search_url': 'https://www.oreillyauto.com/search',
                'crawl_delay': 2
            }
        ]
        
        created_count = 0
        for retailer_data in retailers_data:
            retailer, created = Retailer.objects.get_or_create(
                name=retailer_data['name'],
                defaults=retailer_data
            )
            if created:
                self.stdout.write(f'Created retailer: {retailer.name}')
                created_count += 1
            else:
                self.stdout.write(f'Retailer already exists: {retailer.name}')
        
        self.stdout.write(
            self.style.SUCCESS(f'Setup complete. Created {created_count} new retailers.')
        )
