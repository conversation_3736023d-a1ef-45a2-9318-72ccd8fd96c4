import sqlite3

# Connect to the database
conn = sqlite3.connect('db.sqlite3')
cursor = conn.cursor()

# Query for popular brands
brands_to_search = ['CHAMPION', 'BOSCH', 'FRAM', 'MOTORCRAFT', 'AC DELCO']

print("Popular automotive brands in the database:")
for brand in brands_to_search:
    cursor.execute('SELECT mfgcode, brand FROM whi_mfgcodes WHERE brand LIKE ?', (f'%{brand}%',))
    results = cursor.fetchall()
    if results:
        print(f"\n{brand} brands:")
        for row in results[:3]:  # Show first 3 matches
            print(f'  {row[0]} - {row[1]}')

# Show table structure
cursor.execute("PRAGMA table_info(whi_mfgcodes)")
columns = cursor.fetchall()
print(f"\nTable structure for 'whi_mfgcodes':")
for col in columns:
    print(f"  {col[1]} ({col[2]})")

conn.close()
